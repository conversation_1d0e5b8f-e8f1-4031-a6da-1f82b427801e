/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { Route as rootRouteImport } from './routes/__root'
import { Route as WhiteboardRouteImport } from './routes/whiteboard'
import { Route as UploadRouteImport } from './routes/upload'
import { Route as DashboardRouteImport } from './routes/dashboard'
import { Route as BidsRouteImport } from './routes/bids'
import { Route as IndexRouteImport } from './routes/index'
import { Route as ViewerDocumentIdRouteImport } from './routes/viewer.$documentId'
import { Route as ReportProjectIdRouteImport } from './routes/report.$projectId'
import { Route as DemoTanstackQueryRouteImport } from './routes/demo.tanstack-query'
import { Route as ComparisonDoc1Doc2RouteImport } from './routes/comparison.$doc1.$doc2'

const WhiteboardRoute = WhiteboardRouteImport.update({
  id: '/whiteboard',
  path: '/whiteboard',
  getParentRoute: () => rootRouteImport,
} as any)
const UploadRoute = UploadRouteImport.update({
  id: '/upload',
  path: '/upload',
  getParentRoute: () => rootRouteImport,
} as any)
const DashboardRoute = DashboardRouteImport.update({
  id: '/dashboard',
  path: '/dashboard',
  getParentRoute: () => rootRouteImport,
} as any)
const BidsRoute = BidsRouteImport.update({
  id: '/bids',
  path: '/bids',
  getParentRoute: () => rootRouteImport,
} as any)
const IndexRoute = IndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRouteImport,
} as any)
const ViewerDocumentIdRoute = ViewerDocumentIdRouteImport.update({
  id: '/viewer/$documentId',
  path: '/viewer/$documentId',
  getParentRoute: () => rootRouteImport,
} as any)
const ReportProjectIdRoute = ReportProjectIdRouteImport.update({
  id: '/report/$projectId',
  path: '/report/$projectId',
  getParentRoute: () => rootRouteImport,
} as any)
const DemoTanstackQueryRoute = DemoTanstackQueryRouteImport.update({
  id: '/demo/tanstack-query',
  path: '/demo/tanstack-query',
  getParentRoute: () => rootRouteImport,
} as any)
const ComparisonDoc1Doc2Route = ComparisonDoc1Doc2RouteImport.update({
  id: '/comparison/$doc1/$doc2',
  path: '/comparison/$doc1/$doc2',
  getParentRoute: () => rootRouteImport,
} as any)

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/bids': typeof BidsRoute
  '/dashboard': typeof DashboardRoute
  '/upload': typeof UploadRoute
  '/whiteboard': typeof WhiteboardRoute
  '/demo/tanstack-query': typeof DemoTanstackQueryRoute
  '/report/$projectId': typeof ReportProjectIdRoute
  '/viewer/$documentId': typeof ViewerDocumentIdRoute
  '/comparison/$doc1/$doc2': typeof ComparisonDoc1Doc2Route
}
export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/bids': typeof BidsRoute
  '/dashboard': typeof DashboardRoute
  '/upload': typeof UploadRoute
  '/whiteboard': typeof WhiteboardRoute
  '/demo/tanstack-query': typeof DemoTanstackQueryRoute
  '/report/$projectId': typeof ReportProjectIdRoute
  '/viewer/$documentId': typeof ViewerDocumentIdRoute
  '/comparison/$doc1/$doc2': typeof ComparisonDoc1Doc2Route
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/': typeof IndexRoute
  '/bids': typeof BidsRoute
  '/dashboard': typeof DashboardRoute
  '/upload': typeof UploadRoute
  '/whiteboard': typeof WhiteboardRoute
  '/demo/tanstack-query': typeof DemoTanstackQueryRoute
  '/report/$projectId': typeof ReportProjectIdRoute
  '/viewer/$documentId': typeof ViewerDocumentIdRoute
  '/comparison/$doc1/$doc2': typeof ComparisonDoc1Doc2Route
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | '/bids'
    | '/dashboard'
    | '/upload'
    | '/whiteboard'
    | '/demo/tanstack-query'
    | '/report/$projectId'
    | '/viewer/$documentId'
    | '/comparison/$doc1/$doc2'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/'
    | '/bids'
    | '/dashboard'
    | '/upload'
    | '/whiteboard'
    | '/demo/tanstack-query'
    | '/report/$projectId'
    | '/viewer/$documentId'
    | '/comparison/$doc1/$doc2'
  id:
    | '__root__'
    | '/'
    | '/bids'
    | '/dashboard'
    | '/upload'
    | '/whiteboard'
    | '/demo/tanstack-query'
    | '/report/$projectId'
    | '/viewer/$documentId'
    | '/comparison/$doc1/$doc2'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  BidsRoute: typeof BidsRoute
  DashboardRoute: typeof DashboardRoute
  UploadRoute: typeof UploadRoute
  WhiteboardRoute: typeof WhiteboardRoute
  DemoTanstackQueryRoute: typeof DemoTanstackQueryRoute
  ReportProjectIdRoute: typeof ReportProjectIdRoute
  ViewerDocumentIdRoute: typeof ViewerDocumentIdRoute
  ComparisonDoc1Doc2Route: typeof ComparisonDoc1Doc2Route
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/whiteboard': {
      id: '/whiteboard'
      path: '/whiteboard'
      fullPath: '/whiteboard'
      preLoaderRoute: typeof WhiteboardRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/upload': {
      id: '/upload'
      path: '/upload'
      fullPath: '/upload'
      preLoaderRoute: typeof UploadRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/dashboard': {
      id: '/dashboard'
      path: '/dashboard'
      fullPath: '/dashboard'
      preLoaderRoute: typeof DashboardRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/bids': {
      id: '/bids'
      path: '/bids'
      fullPath: '/bids'
      preLoaderRoute: typeof BidsRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/viewer/$documentId': {
      id: '/viewer/$documentId'
      path: '/viewer/$documentId'
      fullPath: '/viewer/$documentId'
      preLoaderRoute: typeof ViewerDocumentIdRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/report/$projectId': {
      id: '/report/$projectId'
      path: '/report/$projectId'
      fullPath: '/report/$projectId'
      preLoaderRoute: typeof ReportProjectIdRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/demo/tanstack-query': {
      id: '/demo/tanstack-query'
      path: '/demo/tanstack-query'
      fullPath: '/demo/tanstack-query'
      preLoaderRoute: typeof DemoTanstackQueryRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/comparison/$doc1/$doc2': {
      id: '/comparison/$doc1/$doc2'
      path: '/comparison/$doc1/$doc2'
      fullPath: '/comparison/$doc1/$doc2'
      preLoaderRoute: typeof ComparisonDoc1Doc2RouteImport
      parentRoute: typeof rootRouteImport
    }
  }
}

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  BidsRoute: BidsRoute,
  DashboardRoute: DashboardRoute,
  UploadRoute: UploadRoute,
  WhiteboardRoute: WhiteboardRoute,
  DemoTanstackQueryRoute: DemoTanstackQueryRoute,
  ReportProjectIdRoute: ReportProjectIdRoute,
  ViewerDocumentIdRoute: ViewerDocumentIdRoute,
  ComparisonDoc1Doc2Route: ComparisonDoc1Doc2Route,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()
