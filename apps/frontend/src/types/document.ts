// Document and OCR chunk type definitions for PDF viewer

export interface BoundingBox {
  x1: number;
  y1: number;
  x2: number;
  y2: number;
  width: number;
  height: number;
}

export interface Rectangle {
  x: number;
  y: number;
  width: number;
  height: number;
}

export interface DocumentChunk {
  id: string;
  page_number: number;
  chunk_type: 'text' | 'image' | 'table' | 'title' | 'list';
  content: string;
  bounding_box: BoundingBox;
  confidence?: number;
}

export interface ChunkOverlay {
  chunk: DocumentChunk;
  displayCoords: Rectangle;
  isHighlighted: boolean;
  isSelected: boolean;
  similarity?: number;
  comparisonChunkId?: string;
}

export interface DocumentMetadata {
  id: string;
  filename: string;
  company_name: string;
  page_count: number;
  status: 'pending' | 'processing' | 'processed' | 'error';
  total_chunks: number;
  chunk_statistics: {
    text_chunks: number;
    table_chunks: number;
    image_chunks: number;
    title_chunks: number;
    list_chunks: number;
  };
  processing_time?: number;
  ocr_confidence?: number;
}

export interface ComparisonChunk {
  sourceChunk: DocumentChunk;
  targetChunk: DocumentChunk;
  similarity: number;
  analysisType: 'semantic' | 'layout' | 'textual';
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
}

export interface DocumentComparisonResult {
  document1_id: string;
  document2_id: string;
  document1_name: string;
  document2_name: string;
  company1_name: string;
  company2_name: string;
  analysis_type: string;
  threshold_used: number;
  similar_chunks_found: number;
  overall_similarity: {
    average_score: number;
    max_score: number;
    risk_assessment: 'low' | 'medium' | 'high';
  };
  similar_chunks: ComparisonChunk[];
}

export interface ChunkSearchResult {
  chunk_id: string;
  document_id: string;
  content: string;
  page_number: number;
  chunk_type: string;
  bounding_box: BoundingBox;
  similarity_score: number;
}

// PDF Viewer specific types
export interface PDFViewerState {
  currentPage: number;
  totalPages: number;
  scale: number;
  isLoading: boolean;
  error: string | null;
}

export interface ViewerControls {
  zoomIn: () => void;
  zoomOut: () => void;
  resetZoom: () => void;
  goToPage: (page: number) => void;
  nextPage: () => void;
  previousPage: () => void;
}

export interface ChunkFilterOptions {
  chunkTypes: Set<string>;
  similarityThreshold: number;
  pageRange: [number, number] | null;
  searchText: string;
  showOnlyHighlighted: boolean;
}

export interface TooltipData {
  chunk: DocumentChunk;
  position: { x: number; y: number };
  isVisible: boolean;
  comparison?: {
    similarity: number;
    riskLevel: string;
    comparedWith: string;
  };
}

// Hook return types
export interface UseDocumentChunks {
  chunks: DocumentChunk[];
  isLoading: boolean;
  error: string | null;
  refetch: () => void;
}

export interface UseChunkOverlays {
  overlays: ChunkOverlay[];
  selectedChunks: Set<string>;
  highlightedChunks: Set<string>;
  selectChunk: (chunkId: string, multi?: boolean) => void;
  highlightChunk: (chunkId: string) => void;
  clearSelection: () => void;
  clearHighlights: () => void;
}

// Event types
export interface ChunkInteractionEvent {
  chunk: DocumentChunk;
  overlay: ChunkOverlay;
  event: React.MouseEvent | React.KeyboardEvent;
  action: 'click' | 'hover' | 'focus' | 'select';
}