export interface Position {
  x: number
  y: number
}

export interface Tender {
  id: string
  title: string
  publishDate: string
  department: string
  budget: number
  bids: Bid[]
  analysisStatus: 'pending' | 'analyzing' | 'completed'
}

export interface Bid {
  id: string
  companyName: string
  fileName: string
  uploadDate: string
  totalPrice: number
  analysisResults: AnalysisResult[]
}

export type AnalysisType = 'document_layout_embedding' | 'layout_ocr_comparison' | 'llm_service_price_extraction' | 'semantic' | 'stylometry' | 'structural' | 'numerical'

export interface SuspiciousSegment {
  id: string
  content: string
  sourceBid: string
  bidCompany: string
  category: AnalysisType
  suspicionLevel: 'low' | 'medium' | 'high' | 'critical'
  position: Position
  similarSegments: string[]
  similarity: number
  chapter?: string
  // Comparison data
  comparisonContent?: string
  comparisonBid?: string
  comparisonCompany?: string
  comparisonChapter?: string
}

export interface AnalysisResult {
  bidPair: [string, string]
  overallSimilarity: number
  categoryScores: {
    document_layout_embedding: number
    layout_ocr_comparison: number
    llm_service_price_extraction: number
    semantic?: number
    stylometry?: number
    structural?: number
    numerical?: number
  }
  suspiciousSegments: SuspiciousSegment[]
  riskLevel: 'low' | 'medium' | 'high' | 'critical'
}

export interface Connection {
  id: string
  fromSegmentId: string
  toSegmentId: string
  strength: number
  type: AnalysisType
}

export interface AIInsight {
  groupId: string
  segments: SuspiciousSegment[]
  explanation: string
  evidenceStrength: number
  recommendedAction: string
}

export interface AnalysisCategory {
  type: AnalysisType
  name: string
  description: string
  color: string
  segments: SuspiciousSegment[]
}