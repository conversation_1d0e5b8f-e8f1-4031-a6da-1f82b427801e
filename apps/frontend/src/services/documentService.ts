// Document service for API integration with the enhanced backend

import type { 
  DocumentChunk, 
  DocumentMetadata, 
  DocumentComparisonResult, 
  ChunkSearchResult 
} from '../types/document';

interface ApiResponse<T> {
  data?: T;
  error?: string;
  status: number;
}

class DocumentService {
  private baseUrl: string;

  constructor() {
    this.baseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api/v1';
  }

  /**
   * Get document metadata
   */
  async getDocumentMetadata(documentId: string): Promise<DocumentMetadata> {
    const response = await fetch(`${this.baseUrl}/documents/${documentId}/status`);
    
    if (!response.ok) {
      throw new Error(`Failed to fetch document metadata: ${response.statusText}`);
    }

    const data = await response.json();
    return this.transformDocumentStatus(data);
  }

  /**
   * Get document chunks with optional filtering
   */
  async getDocumentChunks(
    documentId: string,
    options: {
      page_number?: number;
      chunk_type?: string;
      limit?: number;
      offset?: number;
    } = {}
  ): Promise<{ chunks: DocumentChunk[]; total: number; hasMore: boolean }> {
    const params = new URLSearchParams();
    
    if (options.page_number !== undefined) params.set('page_number', options.page_number.toString());
    if (options.chunk_type) params.set('chunk_type', options.chunk_type);
    if (options.limit) params.set('limit', options.limit.toString());
    if (options.offset) params.set('offset', options.offset.toString());

    const url = `${this.baseUrl}/documents/${documentId}/chunks?${params.toString()}`;
    const response = await fetch(url);

    if (!response.ok) {
      throw new Error(`Failed to fetch document chunks: ${response.statusText}`);
    }

    const data = await response.json();
    return {
      chunks: data.chunks.map(this.transformChunk),
      total: data.total_chunks,
      hasMore: data.pagination.has_more
    };
  }

  /**
   * Get specific chunk details
   */
  async getChunkDetails(documentId: string, chunkId: string): Promise<DocumentChunk> {
    const response = await fetch(`${this.baseUrl}/documents/${documentId}/chunks/${chunkId}`);
    
    if (!response.ok) {
      throw new Error(`Failed to fetch chunk details: ${response.statusText}`);
    }

    const data = await response.json();
    return this.transformChunk(data);
  }

  /**
   * Search for similar chunks within a document
   */
  async searchSimilarChunks(
    documentId: string,
    queryText: string,
    threshold = 0.7,
    limit = 10
  ): Promise<ChunkSearchResult[]> {
    const formData = new FormData();
    formData.append('query_text', queryText);
    formData.append('threshold', threshold.toString());
    formData.append('limit', limit.toString());

    const response = await fetch(`${this.baseUrl}/documents/${documentId}/chunks/search`, {
      method: 'POST',
      body: formData
    });

    if (!response.ok) {
      throw new Error(`Failed to search similar chunks: ${response.statusText}`);
    }

    const data = await response.json();
    return data.matches;
  }

  /**
   * Analyze semantic similarity between two documents
   */
  async analyzeSemanticSimilarity(
    document1Id: string,
    document2Id: string,
    threshold = 0.8
  ): Promise<DocumentComparisonResult> {
    const formData = new FormData();
    formData.append('document1_id', document1Id);
    formData.append('document2_id', document2Id);
    formData.append('threshold', threshold.toString());

    const response = await fetch(`${this.baseUrl}/analysis/semantic`, {
      method: 'POST',
      body: formData
    });

    if (!response.ok) {
      throw new Error(`Failed to analyze semantic similarity: ${response.statusText}`);
    }

    return await response.json();
  }

  /**
   * Compare two specific chunks
   */
  async compareChunks(chunk1Id: string, chunk2Id: string): Promise<{
    chunk1_info: any;
    chunk2_info: any;
    comparison_result: {
      similarity_score: number;
      risk_level: string;
      analysis_type: string;
    };
  }> {
    const formData = new FormData();
    formData.append('chunk1_id', chunk1Id);
    formData.append('chunk2_id', chunk2Id);

    const response = await fetch(`${this.baseUrl}/analysis/compare-chunks`, {
      method: 'POST',
      body: formData
    });

    if (!response.ok) {
      throw new Error(`Failed to compare chunks: ${response.statusText}`);
    }

    return await response.json();
  }

  /**
   * Get document processing status
   */
  async getProcessingStatus(documentId: string): Promise<{
    status: string;
    progress: string;
    message: string;
    chunks_extracted: number;
  }> {
    const response = await fetch(`${this.baseUrl}/documents/${documentId}/status`);
    
    if (!response.ok) {
      throw new Error(`Failed to get processing status: ${response.statusText}`);
    }

    return await response.json();
  }

  /**
   * Trigger document processing
   */
  async processDocument(documentId: string, generateEmbeddings = true): Promise<{
    status: string;
    message: string;
  }> {
    const response = await fetch(`${this.baseUrl}/documents/${documentId}/process`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        generate_embeddings: generateEmbeddings
      })
    });

    if (!response.ok) {
      throw new Error(`Failed to process document: ${response.statusText}`);
    }

    return await response.json();
  }

  /**
   * Create a new project
   */
  async createProject(
    name: string,
    tenderId?: string
  ): Promise<{
    project_id: string;
    name: string;
    status: string;
    tender_id?: string;
    message: string;
  }> {
    const formData = new FormData();
    formData.append('name', name);
    if (tenderId) formData.append('tender_id', tenderId);

    const response = await fetch(`${this.baseUrl}/projects/create`, {
      method: 'POST',
      body: formData
    });

    if (!response.ok) {
      throw new Error(`Failed to create project: ${response.statusText}`);
    }

    return await response.json();
  }

  /**
   * Check if a project exists
   */
  async checkProjectExists(projectId: string): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/projects/${projectId}/exists`);
      if (!response.ok) return false;
      
      const data = await response.json();
      return data.exists === true;
    } catch {
      return false;
    }
  }

  /**
   * Upload a new document (creates project if needed)
   */
  async uploadDocument(
    projectId: string,
    companyName: string,
    file: File,
    processImmediately = true
  ): Promise<{
    document_id: string;
    status: string;
    message: string;
    project_id: string;
  }> {
    // First, try to create/ensure project exists
    let actualProjectId = projectId;
    
    try {
      // Check if projectId is a UUID format
      const isUuid = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(projectId);
      
      if (isUuid) {
        // Check if this UUID project actually exists in the projects table
        const projectExists = await this.checkProjectExists(projectId);
        if (!projectExists) {
          console.log('UUID project not found in projects table, creating new project for:', projectId);
          const project = await this.createProject(`Project for Tender ${projectId}`, projectId);
          actualProjectId = project.project_id;
          console.log('Created project:', actualProjectId);
        } else {
          console.log('Project exists, using:', projectId);
        }
      } else {
        // Not a UUID format, definitely need to create a project
        console.log('Creating project for tender ID:', projectId);
        const project = await this.createProject(`Project for Tender ${projectId}`, projectId);
        actualProjectId = project.project_id;
        console.log('Created project:', actualProjectId);
      }
    } catch (error) {
      console.warn('Could not create/check project, trying upload anyway:', error);
      // Continue with original projectId if project creation fails
    }

    const formData = new FormData();
    formData.append('project_id', actualProjectId);
    formData.append('company_name', companyName);
    formData.append('file', file);
    formData.append('process_immediately', processImmediately.toString());

    const response = await fetch(`${this.baseUrl}/documents/upload`, {
      method: 'POST',
      body: formData
    });

    if (!response.ok) {
      throw new Error(`Failed to upload document: ${response.statusText}`);
    }

    const result = await response.json();
    return {
      ...result,
      project_id: actualProjectId
    };
  }

  /**
   * Get document file URL for PDF viewing
   */
  getDocumentUrl(documentId: string): string {
    return `${this.baseUrl}/documents/${documentId}/file`;
  }

  /**
   * Get service status
   */
  async getServiceStatus(): Promise<{
    ocr_service: { available: boolean; info: any };
    embedding_service: { available: boolean; info: any };
    system_status: string;
  }> {
    const response = await fetch(`${this.baseUrl}/documents/service/status`);
    
    if (!response.ok) {
      throw new Error(`Failed to get service status: ${response.statusText}`);
    }

    return await response.json();
  }

  // Transform functions to normalize API responses

  private transformDocumentStatus(data: Record<string, unknown>): DocumentMetadata {
    return {
      id: data.document_id,
      filename: data.filename,
      company_name: data.company_name || 'Unknown',
      page_count: data.page_count || 0,
      status: data.status,
      total_chunks: data.chunks_extracted || 0,
      chunk_statistics: {
        text_chunks: 0, // Would need to be provided by API
        table_chunks: 0,
        image_chunks: 0,
        title_chunks: 0,
        list_chunks: 0
      },
      processing_time: data.processing_time,
      ocr_confidence: data.ocr_confidence
    };
  }

  private transformChunk(data: any): DocumentChunk {
    return {
      id: data.id,
      page_number: data.page_number,
      chunk_type: data.chunk_type,
      content: data.content || '',
      bounding_box: {
        x1: data.bounding_box.x1,
        y1: data.bounding_box.y1,
        x2: data.bounding_box.x2,
        y2: data.bounding_box.y2,
        width: data.bounding_box.width,
        height: data.bounding_box.height
      },
      confidence: data.confidence
    };
  }

  /**
   * Batch operations for multiple documents
   */
  async getMultipleDocumentChunks(documentIds: string[]): Promise<Record<string, DocumentChunk[]>> {
    const promises = documentIds.map(id => 
      this.getDocumentChunks(id).then(result => ({ id, chunks: result.chunks }))
    );

    const results = await Promise.allSettled(promises);
    const chunkMap: Record<string, DocumentChunk[]> = {};

    results.forEach((result, index) => {
      if (result.status === 'fulfilled') {
        chunkMap[result.value.id] = result.value.chunks;
      } else {
        console.error(`Failed to load chunks for document ${documentIds[index]}:`, result.reason);
        chunkMap[documentIds[index]] = [];
      }
    });

    return chunkMap;
  }

  /**
   * Cache management
   */
  private cache = new Map<string, { data: any; timestamp: number }>();
  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

  private getCachedData<T>(key: string): T | null {
    const cached = this.cache.get(key);
    if (cached && Date.now() - cached.timestamp < this.CACHE_DURATION) {
      return cached.data as T;
    }
    return null;
  }

  private setCachedData<T>(key: string, data: T): void {
    this.cache.set(key, { data, timestamp: Date.now() });
  }

  private clearCache(pattern?: string): void {
    if (pattern) {
      for (const key of Array.from(this.cache.keys()).filter(key => key.includes(pattern))) {
        this.cache.delete(key);
      }
    } else {
      this.cache.clear();
    }
  }
}

// Export singleton instance
export const documentService = new DocumentService();