import { api } from './api';
import type { Tender } from '../types';

export interface TenderCreate {
  title: string;
  department: string;
  budget: number;
  publish_date?: string;
  description?: string;
  evaluation_criteria?: string;
  submission_deadline?: string;
}

export interface TenderResponse {
  id: string;
  title: string;
  department: string;
  budget: number;
  publish_date: string;
  description?: string;
  evaluation_criteria?: string;
  submission_deadline?: string;
  analysis_status: 'pending' | 'analyzing' | 'completed' | 'failed';
  created_at: string;
  updated_at: string;
}

export interface TenderSummary {
  tender_id: string;
  title: string;
  department: string;
  budget: number;
  analysis_status: string;
  bid_count: number;
  analysis_results_count: number;
  created_at: string;
  updated_at: string;
}

export const tenderService = {
  async getTenders(skip = 0, limit = 100): Promise<TenderResponse[]> {
    return api.get<TenderResponse[]>(`/tenders?skip=${skip}&limit=${limit}`);
  },

  async createTender(tenderData: TenderCreate): Promise<TenderResponse> {
    return api.post<TenderResponse>('/tenders', tenderData);
  },

  async getTender(tenderId: string): Promise<TenderResponse> {
    return api.get<TenderResponse>(`/tenders/${tenderId}`);
  },

  async updateTender(tenderId: string, tenderData: TenderCreate): Promise<TenderResponse> {
    return api.put<TenderResponse>(`/tenders/${tenderId}`, tenderData);
  },

  async deleteTender(tenderId: string): Promise<{ message: string }> {
    return api.delete<{ message: string }>(`/tenders/${tenderId}`);
  },

  async getTenderSummary(tenderId: string): Promise<TenderSummary> {
    return api.get<TenderSummary>(`/tenders/${tenderId}/summary`);
  },

  // Convert backend response to frontend Tender type
  convertToFrontendTender(backendTender: TenderResponse): Tender {
    return {
      id: backendTender.id,
      title: backendTender.title,
      publishDate: backendTender.publish_date,
      department: backendTender.department,
      budget: backendTender.budget,
      bids: [], // Bids will be loaded separately
      analysisStatus: backendTender.analysis_status
    };
  }
};