import { api } from './api';
import type { SuspiciousSegment } from '../types';

export interface AnalysisStatus {
  tender_id: string;
  status: 'pending' | 'analyzing' | 'completed' | 'failed' | 'not_found';
  bid_count: number;
  analysis_count: number;
  last_updated: string;
  error_message?: string;
}

export interface AnalysisResult {
  id: string;
  tender_id: string;
  bid_pair: {
    bid1_id: string;
    bid1_company: string;
    bid2_id: string;
    bid2_company: string;
  };
  comparison_type: 'semantic' | 'structural' | 'numerical' | 'stylometry';
  overall_similarity: number;
  risk_level: 'low' | 'medium' | 'high' | 'critical';
  suspicious_segments: {
    segment_id: string;
    content: string;
    category: string;
    suspicion_level: string;
    similarity_score: number;
    bid1_location?: string;
    bid2_location?: string;
  }[];
  metadata: {
    processing_time: number;
    confidence_score: number;
    method_details: unknown;
  };
  created_at: string;
}

export interface AnalysisResults {
  tender_id: string;
  status: 'pending' | 'analyzing' | 'completed' | 'failed';
  summary: {
    total_bid_pairs: number;
    total_comparisons: number;
    high_risk_pairs: number;
    suspicious_segments_count: number;
    analysis_completion_time: string;
  };
  results: AnalysisResult[];
}

export interface AnalysisSummary {
  tender_id: string;
  status: string;
  summary: {
    total_bid_pairs: number;
    total_comparisons: number;
    high_risk_pairs: number;
    suspicious_segments_count: number;
    analysis_completion_time: string;
  };
  total_results: number;
  high_risk_findings: number;
}

export interface SuspiciousSegmentsResponse {
  tender_id: string;
  total_segments: number;
  filtered_segments: number;
  filters: {
    category?: string;
    risk_level?: string;
  };
  segments: {
    // Support both old and new API format
    id?: string;
    segment_id?: string;
    content: string;
    source_bid?: string;
    bid_company?: string;
    category: string;
    suspicion_level: string;
    position?: {
      x: number;
      y: number;
    };
    similarity?: number;
    similarity_score?: number;
    analysis_result_id?: string;
    bid_pair?: {
      bid1_id: string;
      bid1_company: string;
      bid2_id: string;
      bid2_company: string;
    };
    chapter?: string;
    // Comparison data
    comparison_content?: string;
    comparison_bid?: string;
    comparison_company?: string;
    comparison_chapter?: string;
  }[];
}

export interface AIInsight {
  group_id: string;
  segment_count: number;
  explanation: string;
  evidence_strength: number;
  recommended_action: string;
  key_findings: string[];
  risk_assessment: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  confidence_level: number;
}

export interface HealthStatus {
  status: 'healthy' | 'degraded' | 'unhealthy';
  services: {
    ocr_service: boolean;
    semantic_analyzer: boolean;
    stylometry_analyzer: boolean;
    structural_analyzer: boolean;
    numerical_analyzer: boolean;
    overall_status: string;
  };
  message: string;
  timestamp: string;
}

export interface ModelInfo {
  ocr_model: unknown;
  semantic_model: unknown;
  analysis_engines: {
    stylometry: string;
    structural: string;
    numerical: string;
  };
  privacy_status: string;
  ready_for_production: boolean;
  integration_notes: string;
}

export const analysisService = {
  async startAnalysis(tenderId: string): Promise<{
    message: string;
    tender_id: string;
    status: string;
    analysis_count: number;
    results_summary: {
      total_comparisons: number;
      processing_time: string;
      note: string;
    };
  }> {
    return api.post(`/analysis/start/${tenderId}`);
  },

  async getAnalysisStatus(tenderId: string): Promise<AnalysisStatus> {
    return api.get<AnalysisStatus>(`/analysis/status/${tenderId}`);
  },

  async getAnalysisResults(tenderId: string): Promise<AnalysisResults> {
    return api.get<AnalysisResults>(`/analysis/results/${tenderId}`);
  },

  async getAnalysisSummary(tenderId: string): Promise<AnalysisSummary> {
    return api.get<AnalysisSummary>(`/analysis/results/${tenderId}/summary`);
  },

  async getSuspiciousSegments(
    tenderId: string,
    category?: string,
    riskLevel?: string
  ): Promise<SuspiciousSegmentsResponse> {
    const params = new URLSearchParams();
    if (category) params.append('category', category);
    if (riskLevel) params.append('risk_level', riskLevel);
    
    const query = params.toString();
    const endpoint = `/analysis/results/${tenderId}/segments${query ? `?${query}` : ''}`;
    
    return api.get<SuspiciousSegmentsResponse>(endpoint);
  },

  async generateAIInsights(
    tenderId: string,
    segmentIds: string[]
  ): Promise<{
    tender_id: string;
    insight: AIInsight;
    note: string;
  }> {
    return api.post(`/analysis/insights/${tenderId}`, { segment_ids: segmentIds });
  },

  async getHealthStatus(): Promise<HealthStatus> {
    return api.get<HealthStatus>('/analysis/health');
  },

  async getModelInfo(): Promise<ModelInfo> {
    return api.get<ModelInfo>('/analysis/models/info');
  },

  async updateSegmentPosition(
    segmentId: string,
    position: { x: number; y: number }
  ): Promise<{
    message: string;
    segment_id: string;
    position: { x: number; y: number };
  }> {
    return api.put(`/analysis/segments/${segmentId}/position`, position);
  },

  // Convert backend suspicious segments to frontend format
  convertToFrontendSegments(
    backendSegments: SuspiciousSegmentsResponse,
    existingPositions?: Record<string, { x: number; y: number }>
  ): SuspiciousSegment[] {
    const segments = backendSegments.segments.map((segment, index) => ({
      id: segment.id || segment.segment_id,
      content: segment.content,
      sourceBid: segment.source_bid || segment.bid_pair?.bid1_id,
      bidCompany: segment.bid_company || segment.bid_pair?.bid1_company,
      category: segment.category as 'semantic' | 'structural' | 'numerical' | 'stylometry',
      suspicionLevel: segment.suspicion_level as 'low' | 'medium' | 'high' | 'critical',
      position: segment.position || existingPositions?.[segment.id || segment.segment_id] || {
        x: 100 + (index % 5) * 200,
        y: 150 + Math.floor(index / 5) * 200
      },
      similarSegments: [], // Will be populated below based on bid_pair relationships
      similarity: segment.similarity || segment.similarity_score,
      chapter: segment.chapter,
      // Comparison data
      comparisonContent: segment.comparison_content,
      comparisonBid: segment.comparison_bid,
      comparisonCompany: segment.comparison_company,
      comparisonChapter: segment.comparison_chapter
    }));

    // Build similarity relationships based on bid pairs and similar content
    const segmentGroups = new Map<string, string[]>();
    for (const segment of segments) {
      const analysisId = (segment as unknown as { analysis_result_id?: string }).analysis_result_id;
      if (analysisId) {
        if (!segmentGroups.has(analysisId)) {
          segmentGroups.set(analysisId, []);
        }
        segmentGroups.get(analysisId)?.push(segment.id);
      }
    }

    // Update similarSegments based on groups
    for (const segment of segments) {
      const analysisId = (backendSegments.segments.find(s => (s.id || s.segment_id) === segment.id) as unknown as { analysis_result_id?: string })?.analysis_result_id;
      if (analysisId && segmentGroups.has(analysisId)) {
        segment.similarSegments = segmentGroups.get(analysisId)?.filter(id => id !== segment.id) || [];
      }
    }

    return segments;
  }
};