import { api } from "./api";
import type { Bid } from "../types";

export interface BidResponse {
	id: string;
	tender_id: string;
	company_name: string;
	file_name: string;
	file_path: string;
	total_price: number;
	upload_date: string;
	processed: boolean;
}

export interface BidFileInfo {
	bid_id: string;
	file_name: string;
	file_path: string;
	company_name: string;
	total_price: number;
	upload_date: string;
	file_info: {
		file_name: string;
		file_size: number;
		created_at: number;
		modified_at: number;
		file_type: string;
		exists?: boolean;
	};
}

export interface BidExtractedContent {
	bid_id: string;
	content: {
		text: string;
		layout?: unknown;
		metadata?: unknown;
	};
	summary: {
		has_text: boolean;
		has_layout: boolean;
		has_metadata: boolean;
		text_length: number;
		page_count: number;
	};
}

export const bidService = {
	async uploadBidFiles(
		tenderId: string,
		files: File[],
		companies: string[],
		prices: number[],
	): Promise<BidResponse[]> {
		const formData = new FormData();

		formData.append("tender_id", tenderId);

		for (const company of companies) {
			formData.append("companies", company);
		}
		for (const price of prices) {
			formData.append("prices", price.toString());
		}
		for (const file of files) {
			formData.append("files", file);
		}

		// Debug log
		console.log("Uploading with:", {
			tenderId,
			companies,
			prices,
			fileCount: files.length,
			fileNames: files.map((f) => f.name),
		});

		// Log FormData contents
		for (const [key, value] of formData.entries()) {
			console.log(
				"FormData entry:",
				key,
				value instanceof File ? `File: ${value.name}` : value,
			);
		}

		return api.postFormData<BidResponse[]>("/bids/upload", formData);
	},

	async getBid(bidId: string): Promise<BidResponse> {
		return api.get<BidResponse>(`/bids/${bidId}`);
	},

	async deleteBid(bidId: string): Promise<{ message: string }> {
		return api.delete<{ message: string }>(`/bids/${bidId}`);
	},

	async getTenderBids(tenderId: string): Promise<BidResponse[]> {
		return api.get<BidResponse[]>(`/bids/tender/${tenderId}`);
	},

	async getBidFileInfo(bidId: string): Promise<BidFileInfo> {
		return api.get<BidFileInfo>(`/bids/${bidId}/file-info`);
	},

	async reprocessBid(bidId: string): Promise<{
		message: string;
		content_summary: {
			text_length: number;
			page_count: number;
			language: string;
		};
	}> {
		return api.post(`/bids/${bidId}/reprocess`);
	},

	async getBidExtractedContent(bidId: string): Promise<BidExtractedContent> {
		return api.get<BidExtractedContent>(`/bids/${bidId}/extracted-content`);
	},

	async getBidDocumentBlob(bidId: string): Promise<Blob> {
		const response = await fetch(
			`http://localhost:8000/api/v1/bids/${bidId}/document`,
			{
				method: "GET",
				headers: {
					Accept: "application/pdf,application/octet-stream",
				},
			},
		);

		if (!response.ok) {
			throw new Error(`Failed to fetch document: ${response.statusText}`);
		}

		return response.blob();
	},

	getBidDocumentUrl(bidId: string): string {
		return `http://localhost:8000/api/v1/bids/${bidId}/document`;
	},

	// Convert backend response to frontend Bid type
	convertToFrontendBid(backendBid: BidResponse): Bid {
		return {
			id: backendBid.id,
			companyName: backendBid.company_name,
			fileName: backendBid.file_name,
			uploadDate: backendBid.upload_date,
			totalPrice: backendBid.total_price,
			analysisResults: [], // Analysis results will be loaded separately
		};
	},
};
