import { createFileRoute } from '@tanstack/react-router'
import { useState, useMemo } from 'react'
import { MainLayout } from '../components/layout/MainLayout'
import { Dashboard } from '../components/dashboard/Dashboard'
import { useTenders, useTenderBids } from '../hooks'
import { backupMockTender } from '../data/mockData'

export const Route = createFileRoute('/dashboard')({
  component: DashboardPage,
})

function DashboardPage() {
  const [activeTender, setActiveTender] = useState<string | null>('tender-2025-001')
  const { data: tenders, isLoading: tendersLoading, error } = useTenders()
  const { data: bids, isLoading: bidsLoading } = useTenderBids(activeTender || '')

  // Use mock data as fallback if API fails
  const tendersData = tenders || [backupMockTender]
  
  // Combine tender data with bids
  const activeProjectWithBids = useMemo(() => {
    if (!activeTender) return null
    
    const tender = tendersData.find(t => t.id === activeTender)
    if (!tender) return null
    
    return {
      ...tender,
      bids: bids || tender.bids || []
    }
  }, [activeTender, tendersData, bids])

  const isLoading = tendersLoading || bidsLoading

  return (
    <MainLayout 
      tenders={tendersData}
      activeTender={activeTender}
      onTenderSelect={setActiveTender}
      isLoading={tendersLoading}
      error={error}
    >
      <Dashboard 
        activeProject={activeProjectWithBids}
        isLoading={isLoading}
      />
    </MainLayout>
  )
}