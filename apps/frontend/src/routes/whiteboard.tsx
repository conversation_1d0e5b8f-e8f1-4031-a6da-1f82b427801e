import { createFileRoute } from '@tanstack/react-router'
import { useState } from 'react'
import { MainLayout } from '../components/layout/MainLayout'
import { DetectiveWhiteboard } from '../components/whiteboard/DetectiveWhiteboard'
import { useTenders, useSuspiciousSegments } from '../hooks'
import { backupMockTender } from '../data/mockData'

export const Route = createFileRoute('/whiteboard')({
  component: WhiteboardPage,
})

function WhiteboardPage() {
  const [activeTender, setActiveTender] = useState<string | null>('tender-2025-001')
  const { data: tenders, isLoading: tendersLoading, error: tendersError } = useTenders()
  const { data: suspiciousSegments, isLoading: segmentsLoading, error: segmentsError } = useSuspiciousSegments(activeTender || '')
  
  
  // Use mock data as fallback if API fails
  const tendersData = tenders || [backupMockTender]

  return (
    <MainLayout 
      tenders={tendersData}
      activeTender={activeTender}
      onTenderSelect={setActiveTender}
      isLoading={tendersLoading}
      error={tendersError}
    >
      <DetectiveWhiteboard 
        suspiciousSegments={suspiciousSegments || []}
        tenderId={activeTender || ''}
        isLoading={segmentsLoading}
      />
    </MainLayout>
  )
}