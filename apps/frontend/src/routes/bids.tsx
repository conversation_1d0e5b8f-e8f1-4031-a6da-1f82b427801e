import { createFileRoute } from '@tanstack/react-router'
import { useState } from 'react'
import { MainLayout } from '../components/layout/MainLayout'
import { BidManagement } from '../components/bid/BidManagement'
import { useTenders } from '../hooks'
import { backupMockTender } from '../data/mockData'

export const Route = createFileRoute('/bids')({
  component: BidsPage,
})

function BidsPage() {
  const [activeTender, setActiveTender] = useState<string | null>('tender-2025-001')
  const { data: tenders, isLoading, error } = useTenders()
  
  // Use mock data as fallback if API fails
  const tendersData = tenders || [backupMockTender]
  const currentTender = tendersData.find(t => t.id === activeTender)

  return (
    <MainLayout 
      tenders={tendersData}
      activeTender={activeTender}
      onTenderSelect={setActiveTender}
      isLoading={isLoading}
      error={error}
    >
      {currentTender ? (
        <BidManagement tender={currentTender} />
      ) : (
        <div className="h-full flex items-center justify-center bg-white">
          <div className="text-center">
            <h2 className="text-xl font-semibold text-slate-900 mb-2">Select Tender</h2>
            <p className="text-slate-600">Please select a tender from the left sidebar to manage</p>
          </div>
        </div>
      )}
    </MainLayout>
  )
}