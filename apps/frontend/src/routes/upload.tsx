import { createFileRoute } from '@tanstack/react-router'
import { useState } from 'react'
import { MainLayout } from '../components/layout/MainLayout'
import { TenderUploadForm } from '../components/upload/TenderUploadForm'
import { BidUploadForm } from '../components/upload/BidUploadForm'
import { UploadProgress } from '../components/upload/UploadProgress'
import { useTenders } from '../hooks'
import { backupMockTender } from '../data/mockData'

export const Route = createFileRoute('/upload')({
  component: UploadPage,
})

function UploadPage() {
  const [activeTender, setActiveTender] = useState<string | null>(null)
  const [activeStep, setActiveStep] = useState<'tender' | 'bids' | 'progress'>('tender')
  const [, setNewTenderId] = useState<string | null>(null)
  
  const { data: tenders, isLoading, error } = useTenders()
  
  // Use mock data as fallback if API fails
  const tendersData = tenders || [backupMockTender]

  const handleTenderCreated = (tenderId: string) => {
    setNewTenderId(tenderId)
    setActiveTender(tenderId)
    setActiveStep('bids')
  }

  const handleBidsUploaded = () => {
    setActiveStep('progress')
  }

  const handleSelectExistingTender = (tenderId: string) => {
    setActiveTender(tenderId)
    setActiveStep('bids')
  }

  const resetFlow = () => {
    setActiveTender(null)
    setNewTenderId(null)
    setActiveStep('tender')
  }

  return (
    <MainLayout 
      tenders={tendersData}
      activeTender={activeTender}
      onTenderSelect={setActiveTender}
      isLoading={isLoading}
      error={error}
    >
      <div className="h-full bg-slate-50 overflow-y-auto">
        {/* Header */}
        <div className="bg-white border-b border-slate-200 px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-slate-900">Upload Tender & Bids</h1>
              <p className="text-slate-600 mt-1">
                Create a new tender or add bids to an existing one
              </p>
            </div>
            
            {/* Progress Steps */}
            <div className="flex items-center space-x-4">
              <div className={`flex items-center space-x-2 px-3 py-1 rounded-lg ${
                activeStep === 'tender' ? 'bg-blue-100 text-blue-700' : 
                activeStep === 'bids' || activeStep === 'progress' ? 'bg-green-100 text-green-700' : 
                'bg-slate-100 text-slate-500'
              }`}>
                <span className="text-sm font-medium">1. Tender Info</span>
              </div>
              
              <div className={`flex items-center space-x-2 px-3 py-1 rounded-lg ${
                activeStep === 'bids' ? 'bg-blue-100 text-blue-700' : 
                activeStep === 'progress' ? 'bg-green-100 text-green-700' :
                'bg-slate-100 text-slate-500'
              }`}>
                <span className="text-sm font-medium">2. Upload Bids</span>
              </div>
              
              <div className={`flex items-center space-x-2 px-3 py-1 rounded-lg ${
                activeStep === 'progress' ? 'bg-blue-100 text-blue-700' : 'bg-slate-100 text-slate-500'
              }`}>
                <span className="text-sm font-medium">3. Analysis</span>
              </div>
            </div>
          </div>
        </div>

        <div className="p-6">
          {activeStep === 'tender' && (
            <TenderUploadForm 
              onTenderCreated={handleTenderCreated}
              onExistingTenderSelected={handleSelectExistingTender}
              existingTenders={tendersData}
            />
          )}

          {activeStep === 'bids' && activeTender && (
            <BidUploadForm
              tenderId={activeTender}
              onBidsUploaded={handleBidsUploaded}
              onBack={() => setActiveStep('tender')}
              tenderData={tendersData.find(t => t.id === activeTender)}
            />
          )}

          {activeStep === 'progress' && activeTender && (
            <UploadProgress
              tenderId={activeTender}
              onComplete={() => {/* Navigate to whiteboard or analysis */}}
              onStartOver={resetFlow}
              tenderData={tendersData.find(t => t.id === activeTender)}
            />
          )}
        </div>
      </div>
    </MainLayout>
  )
}