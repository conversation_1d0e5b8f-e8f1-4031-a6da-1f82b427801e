import { createFileRoute } from '@tanstack/react-router'
import { useState } from 'react'
import { ComparisonDetailPage } from '../components/comparison/ComparisonDetailPage'

interface ComparisonParams {
  doc1: string
  doc2: string
}

export const Route = createFileRoute('/comparison/$doc1/$doc2')({
  component: ComparisonPage,
})

function ComparisonPage() {
  const { doc1, doc2 } = Route.useParams()
  
  return (
    <div className="h-screen bg-slate-50">
      <ComparisonDetailPage 
        document1Id={doc1}
        document2Id={doc2}
      />
    </div>
  )
}