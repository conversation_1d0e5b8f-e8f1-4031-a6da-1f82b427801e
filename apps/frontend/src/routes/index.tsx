import { createFileRoute } from '@tanstack/react-router'
import { useState } from 'react'
import { MainLayout } from '../components/layout/MainLayout'
import { useTenders } from '../hooks'
import { backupMockTender } from '../data/mockData'

export const Route = createFileRoute('/')({
  component: App,
})

function App() {
  const [activeTender, setActiveTender] = useState<string | null>('tender-2025-001')
  const { data: tenders, isLoading, error } = useTenders()

  // Use mock data as fallback if API fails
  const tendersData = tenders || [backupMockTender]

  return (
    <MainLayout 
      tenders={tendersData}
      activeTender={activeTender}
      onTenderSelect={setActiveTender}
      isLoading={isLoading}
      error={error}
    >
      <div className="h-full flex items-center justify-center bg-white">
        <div className="text-center space-y-6 max-w-lg mx-auto p-8">
          <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl mx-auto flex items-center justify-center">
            <span className="text-white font-bold text-2xl">TF</span>
          </div>
          
          <div>
            <h1 className="text-3xl font-bold text-slate-900 mb-2">TraceFast</h1>
            <p className="text-slate-600 text-lg">Select a tender from the sidebar to analyze bid documents</p>
          </div>
          
          {activeTender && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
              <h3 className="font-semibold text-blue-900 mb-2">Ready to Start Analysis</h3>
              <p className="text-blue-700 text-sm">
                Selected: {tendersData.find(t => t.id === activeTender)?.title}
              </p>
              <p className="text-blue-600 text-xs mt-1">
                {tendersData.find(t => t.id === activeTender)?.bids.length} bid documents ready for analysis
              </p>
            </div>
          )}
          
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div className="bg-slate-50 p-4 rounded-lg">
              <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mb-3">
                🔍
              </div>
              <h4 className="font-medium text-slate-900 mb-1">Smart Comparison</h4>
              <p className="text-slate-600 text-xs">Multi-dimensional document analysis</p>
            </div>
            
            <div className="bg-slate-50 p-4 rounded-lg">
              <div className="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center mb-3">
                🎯
              </div>
              <h4 className="font-medium text-slate-900 mb-1">Suspicion Detection</h4>
              <p className="text-slate-600 text-xs">Automatic suspicious segment flagging</p>
            </div>
          </div>
          
          {activeTender && (
            <div className="mt-6 space-y-3">
              <a 
                href="/dashboard"
                className="inline-flex items-center px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors w-full justify-center"
              >
                Open Dashboard →
              </a>
              {tendersData.find(t => t.id === activeTender)?.analysisStatus === 'completed' && (
                <a 
                  href="/whiteboard"
                  className="inline-flex items-center px-6 py-3 border border-blue-600 text-blue-600 font-medium rounded-lg hover:bg-blue-50 transition-colors w-full justify-center"
                >
                  Advanced Analysis View
                </a>
              )}
            </div>
          )}
        </div>
      </div>
    </MainLayout>
  )
}
