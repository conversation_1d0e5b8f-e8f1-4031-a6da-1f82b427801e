import type { ReactNode } from 'react'
import { Header } from './Header'
import { TenderSidebar } from './TenderSidebar'
import { usePersistentSidebar } from '../../hooks/usePersistentSidebar'
import type { Tender } from '../../types'

interface MainLayoutProps {
  children: ReactNode
  tenders: Tender[]
  activeTender: string | null
  onTenderSelect: (tenderId: string) => void
  isLoading?: boolean
  error?: Error | null
}

export function MainLayout({ children, tenders, activeTender, onTenderSelect, isLoading, error }: MainLayoutProps) {
  const { leftSidebarExpanded, toggleLeftSidebar } = usePersistentSidebar()
  const currentTender = tenders.find(t => t.id === activeTender)

  return (
    <div className="h-screen flex flex-col bg-slate-100">
      <Header 
        currentTender={currentTender} 
        onToggleSidebar={toggleLeftSidebar}
        isSidebarCollapsed={!leftSidebarExpanded}
      />
      
      <div className="flex-1 flex overflow-hidden">
        <TenderSidebar 
          tenders={tenders}
          activeTender={activeTender}
          onTenderSelect={onTenderSelect}
          isCollapsed={!leftSidebarExpanded}
          isLoading={isLoading}
          error={error}
        />
        
        <main className="flex-1 overflow-hidden">
          {children}
        </main>
      </div>
    </div>
  )
}