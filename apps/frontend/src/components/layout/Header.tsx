import { <PERSON>, Settings, User, Menu, PanelLeftClose } from 'lucide-react'
import type { Tender } from '../../types'

interface HeaderProps {
  currentTender?: Tender
  onToggleSidebar?: () => void
  isSidebarCollapsed?: boolean
}

export function Header({ currentTender, onToggleSidebar, isSidebarCollapsed }: HeaderProps) {
  return (
    <header className="bg-slate-900 text-white border-b border-slate-700 h-16 flex items-center px-6">
      <div className="flex items-center space-x-4">
        {/* Sidebar Toggle */}
        {onToggleSidebar && (
          <button 
            type="button"
            onClick={onToggleSidebar}
            className="p-2 hover:bg-slate-800 rounded-lg transition-colors"
            title={isSidebarCollapsed ? 'Show sidebar' : 'Hide sidebar'}
          >
            {isSidebarCollapsed ? <Menu className="w-5 h-5" /> : <PanelLeftClose className="w-5 h-5" />}
          </button>
        )}
        
        {/* Logo and Brand */}
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
            <span className="text-white font-bold text-sm">TF</span>
          </div>
          <div>
            <h1 className="text-xl font-bold">TraceFast</h1>
            <p className="text-xs text-slate-400">Public Procurement Analysis Platform</p>
          </div>
        </div>
        
        {/* Current Tender Info */}
        {currentTender && (
          <>
            <div className="w-px h-8 bg-slate-600" />
            <div className="flex-1">
              <h2 className="text-sm font-medium truncate max-w-md">
                {currentTender.title}
              </h2>
              <p className="text-xs text-slate-400">
                {currentTender.department} • {currentTender.bids.length} bids
              </p>
            </div>
          </>
        )}
      </div>

      {/* Navigation */}
      <div className="ml-auto flex items-center space-x-1 mr-6">
        <a 
          href="/"
          className="px-3 py-2 text-sm text-slate-300 hover:text-white hover:bg-slate-800 rounded-lg transition-colors"
        >
          Home
        </a>
        <a 
          href="/upload"
          className="px-3 py-2 text-sm text-slate-300 hover:text-white hover:bg-slate-800 rounded-lg transition-colors"
        >
          Upload
        </a>
        <a 
          href="/dashboard"
          className="px-3 py-2 text-sm text-slate-300 hover:text-white hover:bg-slate-800 rounded-lg transition-colors"
        >
          Dashboard
        </a>
      </div>

      {/* Right Side Actions */}
      <div className="flex items-center space-x-4">
        <button type="button" className="p-2 hover:bg-slate-800 rounded-lg transition-colors">
          <Search className="w-5 h-5" />
        </button>
        <button type="button" className="p-2 hover:bg-slate-800 rounded-lg transition-colors">
          <Settings className="w-5 h-5" />
        </button>
        <button type="button" className="p-2 hover:bg-slate-800 rounded-lg transition-colors">
          <User className="w-5 h-5" />
        </button>
      </div>
    </header>
  )
}