import { ChevronRight, FileText, Calendar, DollarSign, AlertTriangle, CheckCircle, Clock, Plus, BarChart3, Upload } from 'lucide-react'
import { Link, useLocation } from '@tanstack/react-router'
import type { Tender } from '../../types'

interface TenderSidebarProps {
  tenders: Tender[]
  activeTender: string | null
  onTenderSelect: (tenderId: string) => void
  isCollapsed?: boolean
  isLoading?: boolean
  error?: Error | null
}

export function TenderSidebar({ tenders, activeTender, onTenderSelect, isCollapsed = false, isLoading, error }: TenderSidebarProps) {
  const location = useLocation()
  const getStatusIcon = (status: Tender['analysisStatus']) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-500" />
      case 'analyzing':
        return <Clock className="w-4 h-4 text-yellow-500 animate-pulse" />
      case 'pending':
        return <AlertTriangle className="w-4 h-4 text-gray-400" />
    }
  }

  const getStatusText = (status: Tender['analysisStatus']) => {
    switch (status) {
      case 'completed':
        return 'Analysis Complete'
      case 'analyzing':
        return 'Analyzing...'
      case 'pending':
        return 'Pending Analysis'
    }
  }

  const formatBudget = (budget: number) => {
    return `MOP ${budget.toLocaleString()}`
  }

  if (isCollapsed) {
    return (
      <aside className="w-0 overflow-hidden transition-all duration-300 ease-in-out">
      </aside>
    )
  }

  return (
    <aside className="w-80 bg-slate-50 border-r border-slate-200 h-full overflow-y-auto transition-all duration-300 ease-in-out"> 
      <div className="w-full p-4 border-b border-slate-200">
        <Link to="/upload" className="block w-full px-4 py-3 text-center bg-blue-500 text-white rounded-lg text-sm font-medium hover:bg-blue-600 transition-colors cursor-pointer">
          Add New Tender Project
        </Link>
      </div>
      
      <div className="p-4 border-b border-slate-200">
        <h2 className="text-lg font-semibold text-slate-900">Tender Projects</h2>
        <p className="text-sm text-slate-600">Select a government tender to analyze</p>
      </div>
      
      <div className="p-2">
        {isLoading && (
          <div className="flex items-center justify-center p-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600" />
          </div>
        )}
        
        {error && (
          <div className="mx-2 p-4 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-sm text-red-600">Failed to load tenders</p>
            <p className="text-xs text-red-500 mt-1">Using backup data</p>
          </div>
        )}
        
        {tenders.map((tender) => (
          <button
            type="button"
            key={tender.id}
            onClick={() => onTenderSelect(tender.id)}
            className={`w-full p-4 rounded-lg text-left transition-all duration-200 mb-2 ${
              activeTender === tender.id
                ? 'bg-blue-50 border-2 border-blue-200 shadow-sm'
                : 'bg-white border border-slate-200 hover:border-slate-300 hover:shadow-sm'
            }`}
          >
            <div className="flex items-start justify-between">
              <div className="flex-1 min-w-0">
                <h3 className={`font-medium text-sm mb-2 line-clamp-2 ${
                  activeTender === tender.id ? 'text-blue-900' : 'text-slate-900'
                }`}>
                  {tender.title}
                </h3>
                
                <div className="space-y-1">
                  <div className="flex items-center text-xs text-slate-600">
                    <Calendar className="w-3 h-3 mr-1" />
                    {tender.publishDate}
                  </div>
                  
                  <div className="flex items-center text-xs text-slate-600">
                    <DollarSign className="w-3 h-3 mr-1" />
                    Budget: {formatBudget(tender.budget)}
                  </div>
                  
                  <div className="flex items-center text-xs text-slate-600">
                    <FileText className="w-3 h-3 mr-1" />
                    {tender.bids.length} bid documents
                  </div>
                </div>
                
                <div className="flex items-center justify-between mt-3">
                  <div className="flex items-center space-x-2">
                    {getStatusIcon(tender.analysisStatus)}
                    <span className="text-xs text-slate-600">
                      {getStatusText(tender.analysisStatus)}
                    </span>
                  </div>
                  
                  {activeTender === tender.id && (
                    <ChevronRight className="w-4 h-4 text-blue-500" />
                  )}
                </div>
              </div>
            </div>
          </button>
        ))}
      </div> 
    </aside>
  )
}