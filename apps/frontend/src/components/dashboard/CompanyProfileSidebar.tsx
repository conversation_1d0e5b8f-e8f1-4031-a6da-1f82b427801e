interface CompanyData {
  name: string
  fileName: string
  totalPrice: number
  uploadDate: string
}

interface CompanyProfileSidebarProps {
  companies: CompanyData[]
  selectedPair: [string, string] | null
  onClose: () => void
}

export function CompanyProfileSidebar({ companies, selectedPair, onClose }: CompanyProfileSidebarProps) {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const getCompanyColor = (index: number): string => {
    const colors = [
      'bg-blue-100 border-blue-300',
      'bg-green-100 border-green-300',
      'bg-orange-100 border-orange-300',
      'bg-purple-100 border-purple-300',
      'bg-red-100 border-red-300',
      'bg-yellow-100 border-yellow-300',
      'bg-pink-100 border-pink-300',
      'bg-indigo-100 border-indigo-300'
    ]
    return colors[index % colors.length]
  }

  const isHighlighted = (companyName: string): boolean => {
    if (!selectedPair) return false
    // This would need to be enhanced to match company names with IDs
    // For now, we'll use a simple approach
    return false
  }

  return (
    <div className="w-80 bg-white border-l border-slate-200 flex flex-col h-full">
      {/* Header */}
      <div className="p-4 border-b border-slate-200 flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold text-slate-900">Company Profiles</h3>
          <p className="text-sm text-slate-600">{companies.length} bidding companies</p>
        </div>
        <button
          type="button"
          onClick={onClose}
          className="p-2 hover:bg-slate-100 rounded-lg transition-colors"
        >
          <svg className="w-5 h-5 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <title>Close</title>
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      {/* Company List */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {companies.map((company, index) => (
          <div
            key={company.name}
            className={`
              p-4 rounded-lg border-2 transition-all duration-200
              ${getCompanyColor(index)}
              ${isHighlighted(company.name) ? 'ring-2 ring-blue-500 ring-offset-2' : ''}
              hover:shadow-md cursor-pointer
            `}
          >
            {/* Company Name */}
            <div className="flex items-center justify-between mb-3">
              <h4 className="font-semibold text-slate-900 truncate">
                {company.name}
              </h4>
              <div className="w-3 h-3 rounded-full bg-green-500 flex-shrink-0" />
            </div>

            {/* Price Information */}
            <div className="space-y-2 mb-3">
              <div className="flex items-center justify-between">
                <span className="text-sm text-slate-600">Bid Amount</span>
                <span className="text-sm font-medium text-slate-900">
                  ${company.totalPrice?.toLocaleString() || 'N/A'}
                </span>
              </div>
              
              {/* Price relative to average */}
              <div className="flex items-center justify-between">
                <span className="text-sm text-slate-600">vs. Average</span>
                <span className={`text-sm font-medium ${
                  (company.totalPrice || 0) > companies.reduce((sum, c) => sum + (c.totalPrice || 0), 0) / companies.length
                    ? 'text-red-600' : 'text-green-600'
                }`}>
                  {company.totalPrice ? (`${((company.totalPrice / (companies.reduce((sum, c) => sum + (c.totalPrice || 0), 0) / companies.length) - 1) * 100).toFixed(1)}%`) : 'N/A'}
                </span>
              </div>
            </div>

            {/* Document Information */}
            <div className="space-y-2 mb-3">
              <div className="flex items-start justify-between">
                <span className="text-sm text-slate-600">Document</span>
                <span className="text-sm text-slate-900 truncate max-w-32 text-right">
                  {company.fileName}
                </span>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm text-slate-600">Uploaded</span>
                <span className="text-sm text-slate-900">
                  {formatDate(company.uploadDate)}
                </span>
              </div>
            </div>

            {/* Quick Actions */}
            <div className="flex items-center space-x-2 pt-3 border-t border-slate-200">
              <button type="button" className="flex-1 px-3 py-2 text-xs font-medium text-blue-600 bg-blue-50 hover:bg-blue-100 rounded transition-colors">
                View PDF
              </button>
              <button type="button" className="flex-1 px-3 py-2 text-xs font-medium text-slate-600 bg-slate-50 hover:bg-slate-100 rounded transition-colors">
                Analysis
              </button>
            </div>

            {/* Risk Indicators (Mock Data) */}
            <div className="mt-3 pt-3 border-t border-slate-200">
              <div className="text-xs text-slate-600 mb-2">Risk Indicators</div>
              <div className="grid grid-cols-2 gap-2">
                <div className="flex items-center justify-between">
                  <span className="text-xs text-slate-500">Semantic</span>
                  <div className="flex items-center space-x-1">
                    <div className="w-2 h-2 rounded-full bg-yellow-400" />
                    <span className="text-xs text-slate-600">Med</span>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-xs text-slate-500">Layout</span>
                  <div className="flex items-center space-x-1">
                    <div className="w-2 h-2 rounded-full bg-red-400" />
                    <span className="text-xs text-slate-600">High</span>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-xs text-slate-500">Pricing</span>
                  <div className="flex items-center space-x-1">
                    <div className="w-2 h-2 rounded-full bg-green-400" />
                    <span className="text-xs text-slate-600">Low</span>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-xs text-slate-500">Style</span>
                  <div className="flex items-center space-x-1">
                    <div className="w-2 h-2 rounded-full bg-orange-400" />
                    <span className="text-xs text-slate-600">Med</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Footer Actions */}
      <div className="p-4 border-t border-slate-200 space-y-2">
        <button type="button" className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium">
          Generate Report
        </button>
        <button type="button" className="w-full px-4 py-2 border border-slate-300 text-slate-700 rounded-lg hover:bg-slate-50 transition-colors text-sm font-medium">
          Export Analysis
        </button>
      </div>
    </div>
  )
}