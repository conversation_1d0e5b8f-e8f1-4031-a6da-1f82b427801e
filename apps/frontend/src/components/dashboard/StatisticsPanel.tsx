import { useMemo } from 'react'
import type { Tender } from '../../types'

interface StatisticsPanelProps {
  project: Tender
}

export function StatisticsPanel({ project }: StatisticsPanelProps) {
  const statistics = useMemo(() => {
    const bidCount = project.bids?.length || 0
    const totalPrice = project.bids?.reduce((sum, bid) => sum + (bid.totalPrice || 0), 0) || 0
    const averagePrice = bidCount > 0 ? totalPrice / bidCount : 0
    const prices = project.bids?.map(bid => bid.totalPrice || 0) || [0]
    const priceRange = {
      min: bidCount > 0 ? Math.min(...prices) : 0,
      max: bidCount > 0 ? Math.max(...prices) : 0
    }
    const priceVariation = averagePrice > 0 ? ((priceRange.max - priceRange.min) / averagePrice) * 100 : 0
    
    // Mock analysis statistics
    const totalComparisons = (bidCount * (bidCount - 1)) / 2
    const suspiciousComparisons = Math.floor(totalComparisons * 0.3) // 30% mock rate
    const criticalFindings = Math.floor(suspiciousComparisons * 0.2) // 20% of suspicious are critical
    
    return {
      bidCount,
      averagePrice,
      priceRange,
      priceVariation,
      totalComparisons,
      suspiciousComparisons,
      criticalFindings,
      analysisProgress: project.analysisStatus === 'completed' ? 100 : 
                       project.analysisStatus === 'analyzing' ? 75 : 0
    }
  }, [project])

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {/* Bid Statistics */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-slate-600">Total Bids</p>
            <p className="text-3xl font-bold text-slate-900 mt-2">{statistics.bidCount}</p>
          </div>
          <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
            <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <title>Documents</title>
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
        </div>
        <div className="mt-4 text-xs text-slate-600">
          Average: ${statistics.averagePrice?.toLocaleString() || 'N/A'}
        </div>
      </div>

      {/* Price Analysis */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-slate-600">Price Variation</p>
            <p className="text-3xl font-bold text-slate-900 mt-2">
              {statistics.priceVariation.toFixed(1)}%
            </p>
          </div>
          <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
            <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <title>Companies</title>
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
            </svg>
          </div>
        </div>
        <div className="mt-4 text-xs text-slate-600">
          Range: ${statistics.priceRange?.min?.toLocaleString() || 'N/A'} - ${statistics.priceRange?.max?.toLocaleString() || 'N/A'}
        </div>
      </div>

      {/* Analysis Progress */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-slate-600">Analysis Progress</p>
            <p className="text-3xl font-bold text-slate-900 mt-2">{statistics.analysisProgress}%</p>
          </div>
          <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
            <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <title>Analysis</title>
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
          </div>
        </div>
        <div className="mt-4">
          <div className="flex items-center justify-between text-xs text-slate-600 mb-2">
            <span>{statistics.totalComparisons} comparisons</span>
            <span>{project.analysisStatus}</span>
          </div>
          <div className="w-full bg-slate-200 rounded-full h-2">
            <div 
              className={`h-2 rounded-full transition-all duration-300 ${
                statistics.analysisProgress === 100 ? 'bg-green-500' : 'bg-purple-500'
              }`}
              style={{ width: `${statistics.analysisProgress}%` }}
            />
          </div>
        </div>
      </div>

      {/* Risk Assessment */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-slate-600">Suspicious Findings</p>
            <p className="text-3xl font-bold text-slate-900 mt-2">{statistics.suspiciousComparisons}</p>
          </div>
          <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${
            statistics.criticalFindings > 0 ? 'bg-red-100' : 'bg-gray-100'
          }`}>
            <svg className={`w-6 h-6 ${
              statistics.criticalFindings > 0 ? 'text-red-600' : 'text-gray-600'
            }`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <title>Alert</title>
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
        </div>
        <div className="mt-4 text-xs text-slate-600">
          {statistics.criticalFindings} critical • {statistics.suspiciousComparisons - statistics.criticalFindings} moderate
        </div>
      </div>

      {/* Additional Analysis Metrics */}
      <div className="md:col-span-2 lg:col-span-4 bg-white rounded-lg shadow-sm border p-6">
        <h3 className="text-lg font-semibold text-slate-900 mb-4">Analysis Breakdown</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">
              {Math.floor(statistics.totalComparisons * 0.4)}
            </div>
            <div className="text-sm text-slate-600 mt-1">Semantic Analysis</div>
            <div className="w-full bg-slate-200 rounded-full h-2 mt-2">
              <div className="bg-blue-500 h-2 rounded-full" style={{ width: '85%' }} />
            </div>
          </div>
          
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">
              {Math.floor(statistics.totalComparisons * 0.3)}
            </div>
            <div className="text-sm text-slate-600 mt-1">Structural Analysis</div>
            <div className="w-full bg-slate-200 rounded-full h-2 mt-2">
              <div className="bg-green-500 h-2 rounded-full" style={{ width: '72%' }} />
            </div>
          </div>
          
          <div className="text-center">
            <div className="text-2xl font-bold text-orange-600">
              {Math.floor(statistics.totalComparisons * 0.25)}
            </div>
            <div className="text-sm text-slate-600 mt-1">Numerical Analysis</div>
            <div className="w-full bg-slate-200 rounded-full h-2 mt-2">
              <div className="bg-orange-500 h-2 rounded-full" style={{ width: '68%' }} />
            </div>
          </div>
          
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600">
              {Math.floor(statistics.totalComparisons * 0.35)}
            </div>
            <div className="text-sm text-slate-600 mt-1">Stylometry Analysis</div>
            <div className="w-full bg-slate-200 rounded-full h-2 mt-2">
              <div className="bg-purple-500 h-2 rounded-full" style={{ width: '79%' }} />
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}