import { useState, useEffect } from "react";
import { X, List, Lightbulb, Focus, Eye } from "lucide-react";
import type { SuspiciousSegment, AnalysisCategory } from "../../types";
import { CategorySection } from "./CategorySection";
import { AIInsightsPanel } from "./AIInsightsPanel";
import { DocumentViewer } from "./DocumentViewer";
import { usePersistentSidebar } from "../../hooks/usePersistentSidebar";
import { Button } from "../ui/button";

interface UnifiedSidebarProps {
	categories: AnalysisCategory[];
	selectedSegments: SuspiciousSegment[];
	showAIInsights: boolean;
	onSegmentFocus: (segmentId: string) => void;
	onCloseAIInsights: () => void;
	activeTab?: TabType;
	onTabChange?: (tab: TabType) => void;
	shouldExpand?: boolean;
}

export type TabType = "categories" | "insights" | "focused";

export function UnifiedSidebar({
	categories,
	selectedSegments,
	showAIInsights,
	onSegmentFocus,
	onCloseAIInsights,
	activeTab: externalActiveTab,
	onTabChange,
	shouldExpand,
}: UnifiedSidebarProps) {
	const [internalActiveTab, setInternalActiveTab] =
		useState<TabType>("categories");
	const [showDocumentViewer, setShowDocumentViewer] = useState(false);
	const { rightSidebarExpanded, setRightSidebar } = usePersistentSidebar();

	const activeTab = externalActiveTab || internalActiveTab;
	const setActiveTab = onTabChange || setInternalActiveTab;

	// Auto-expand when shouldExpand changes from false to true
	useEffect(() => {
		if (shouldExpand) {
			setRightSidebar(true);
		}
	}, [shouldExpand, setRightSidebar]);

	// Get the first selected segment for focused view
	const focusedSegment =
		selectedSegments.length === 1 ? selectedSegments[0] : null;

	if (!rightSidebarExpanded) {
		return (
			<div className="absolute top-4 right-4 z-10">
				<button
					type="button"
					onClick={() => setRightSidebar(true)}
					className="bg-white shadow-lg rounded-lg p-3 border border-slate-200 hover:bg-slate-50 transition-colors"
					title="Show sidebar"
				>
					<List className="w-5 h-5 text-slate-600" />
				</button>
			</div>
		);
	}

	return (
		<div className="absolute top-0 right-0 bottom-0 w-80 bg-white shadow-lg border border-slate-200 z-10 flex flex-col">
			{/* Header with tabs */}
			<div className="border-b border-slate-200">
				<div className="flex items-center justify-between p-3">
					<div className="flex space-x-1">
						<button
							type="button"
							onClick={() => setActiveTab("categories")}
							className={`px-3 py-1.5 rounded-md text-sm font-medium transition-colors ${
								activeTab === "categories"
									? "bg-blue-100 text-blue-700"
									: "text-slate-600 hover:text-slate-900 hover:bg-slate-100"
							}`}
						>
							<List className="w-4 h-4 inline mr-1" />
							Categories
						</button>
						<button
							type="button"
							onClick={() => setActiveTab("focused")}
							disabled={!focusedSegment}
							className={`px-3 py-1.5 rounded-md text-sm font-medium transition-colors ${
								activeTab === "focused" && focusedSegment
									? "bg-green-100 text-green-700"
									: focusedSegment
										? "text-slate-600 hover:text-slate-900 hover:bg-slate-100"
										: "text-slate-400 cursor-not-allowed"
							}`}
						>
							<Focus className="w-4 h-4 inline mr-1" />
							Focused
						</button>
						<button
							type="button"
							onClick={() => setActiveTab("insights")}
							className={`px-3 py-1.5 rounded-md text-sm font-medium transition-colors relative ${
								activeTab === "insights" || showAIInsights
									? "bg-purple-100 text-purple-700"
									: "text-slate-600 hover:text-slate-900 hover:bg-slate-100"
							}`}
						>
							<Lightbulb className="w-4 h-4 inline mr-1" />
							AI Insights
							{selectedSegments.length > 0 && (
								<span className="absolute -top-1 -right-1 bg-purple-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
									{selectedSegments.length}
								</span>
							)}
						</button>
					</div>
					<button
						type="button"
						onClick={() => setRightSidebar(false)}
						className="p-1 hover:bg-slate-100 rounded-md transition-colors"
						title="Hide sidebar"
					>
						<X className="w-4 h-4 text-slate-400" />
					</button>
				</div>
			</div>

			{/* Content */}
			<div className="flex-1 overflow-hidden">
				{activeTab === "categories" && (
					<div className="h-full overflow-y-auto">
						<div className="p-3 border-b border-slate-200">
							<h3 className="text-sm font-semibold text-slate-900">
								Analysis Categories
							</h3>
							<p className="text-xs text-slate-600 mt-1">
								Click segments to focus on canvas
							</p>
						</div>
						<div className="space-y-1">
							{categories.map((category) => (
								<CategorySection
									key={category.type}
									category={category}
									onSegmentDragStart={() => {}}
									onSegmentDragEnd={() => {}}
									onSegmentFocus={onSegmentFocus}
								/>
							))}
						</div>
					</div>
				)}

				{activeTab === "focused" && focusedSegment && (
					<div className="h-full overflow-y-auto">
						<div className="p-4">
							{/* Header with comparison info */}
							<div className="mb-4">
								<h3 className="text-sm font-semibold text-slate-900 mb-2">
									Document Comparison
								</h3>
								<div className="bg-slate-50 rounded-lg p-3">
									<div className="flex items-center justify-between mb-2">
										<span className="text-xs font-medium text-slate-700">
											Analysis Type
										</span>
										<span className="text-xs text-slate-600 capitalize">
											{focusedSegment.category}
										</span>
									</div>
									<div className="flex items-center justify-between mb-2">
										<span className="text-xs font-medium text-slate-700">
											Suspicion Level
										</span>
										<span
											className={`text-xs px-2 py-1 rounded-full ${
												focusedSegment.suspicionLevel === "critical"
													? "bg-red-100 text-red-700"
													: focusedSegment.suspicionLevel === "high"
														? "bg-orange-100 text-orange-700"
														: focusedSegment.suspicionLevel === "medium"
															? "bg-yellow-100 text-yellow-700"
															: "bg-gray-100 text-gray-700"
											}`}
										>
											{focusedSegment.suspicionLevel}
										</span>
									</div>
									<div className="flex items-center justify-between">
										<span className="text-xs font-medium text-slate-700">
											Similarity Score
										</span>
										<span className="text-xs text-slate-600 font-medium">
											{Math.round(focusedSegment.similarity * 100)}%
										</span>
									</div>
								</div>
							</div>

							{/* Document comparison sections */}
							<div className="space-y-4">
								{/* Primary document snippet */}
								<div>
									<h4 className="text-xs font-semibold text-slate-900 mb-2 flex items-center">
										<div className="w-3 h-3 bg-blue-500 rounded-full mr-2" />
										{focusedSegment.bidCompany}
									</h4>
									<div className="bg-blue-50 border-l-4 border-blue-500 p-3 rounded-r-md">
										<p className="text-xs text-slate-800 leading-relaxed whitespace-pre-wrap">
											{focusedSegment.content}
										</p>
									</div>
									<div className="mt-2 text-xs text-slate-500">
										Chapter: {focusedSegment.chapter || "Unknown"}
									</div>
								</div>

								{/* Comparison document snippet */}
								{focusedSegment.comparisonContent ? (
									<div>
										<h4 className="text-xs font-semibold text-slate-900 mb-2 flex items-center">
											<div className="w-3 h-3 bg-orange-500 rounded-full mr-2" />
											{focusedSegment.comparisonCompany}
											<span className="ml-2 text-xs text-slate-500">
												({Math.round(focusedSegment.similarity * 100)}% match)
											</span>
										</h4>
										<div className="bg-orange-50 border-l-4 border-orange-500 p-3 rounded-r-md">
											<p className="text-xs text-slate-800 leading-relaxed whitespace-pre-wrap">
												{focusedSegment.comparisonContent}
											</p>
										</div>
										<div className="mt-2 text-xs text-slate-500">
											Chapter: {focusedSegment.comparisonChapter || "Unknown"}
										</div>
									</div>
								) : (
									<div className="bg-gray-50 border border-gray-200 p-3 rounded-lg">
										<p className="text-xs text-gray-600 text-center">
											No similar segments found
										</p>
									</div>
								)}
							</div>

							{/* Analysis insights */}
							<div className="mt-6 p-3 bg-slate-100 rounded-lg">
								<h4 className="text-xs font-semibold text-slate-900 mb-2">
									Analysis Insights
								</h4>
								<div className="text-xs text-slate-700 space-y-1">
									<p>
										• <strong>Match Type:</strong>{" "}
										{focusedSegment.category === "semantic"
											? "Content similarity"
											: focusedSegment.category === "structural"
												? "Document structure"
												: focusedSegment.category === "numerical"
													? "Numerical patterns"
													: focusedSegment.category === "stylometry"
														? "Writing style"
														: "Unknown"}
									</p>
									<p>
										• <strong>Similarity:</strong>{" "}
										{Math.round(focusedSegment.similarity * 100)}% match
										detected
									</p>
									{focusedSegment.comparisonContent && (
										<p>
											• <strong>Comparison found:</strong> Similar content
											between {focusedSegment.bidCompany} and{" "}
											{focusedSegment.comparisonCompany}
										</p>
									)}
								</div>
							</div>

							{/* View Documents Button */}
							<div className="mt-4">
								<Button
									onClick={() => setShowDocumentViewer(true)}
									className="w-full text-sm"
									variant="outline"
								>
									<Eye className="w-4 h-4 mr-2" />
									View Documents Side by Side
								</Button>
							</div>
						</div>
					</div>
				)}

				{activeTab === "insights" && (
					<div className="h-full">
						{selectedSegments.length > 0 || showAIInsights ? (
							<AIInsightsPanel
								selectedSegments={selectedSegments}
								onClose={onCloseAIInsights}
							/>
						) : (
							<div className="h-full flex items-center justify-center p-6 text-center">
								<div>
									<Lightbulb className="w-12 h-12 text-slate-300 mx-auto mb-3" />
									<h3 className="text-sm font-medium text-slate-700 mb-1">
										No Insights Available
									</h3>
									<p className="text-xs text-slate-500">
										Select one or more segments to generate AI insights
									</p>
								</div>
							</div>
						)}
					</div>
				)}
			</div>

			{/* Document Viewer Dialog */}
			{focusedSegment && (
				<DocumentViewer
					isOpen={showDocumentViewer}
					onClose={() => setShowDocumentViewer(false)}
					segment={focusedSegment}
				/>
			)}
		</div>
	);
}
