import { <PERSON>, FileText, TrendingUp, AlertTriangle, Download } from 'lucide-react'
import type { SuspiciousSegment, AIInsight } from '../../types'

interface AIInsightsPanelProps {
  selectedSegments: SuspiciousSegment[]
  onClose: () => void
}

// Mock AI insights based on selected segments
const generateAIInsight = (segments: SuspiciousSegment[]): AIInsight => {
  const categories = segments.map(s => s.category)
  const companies = segments.map(s => s.bidCompany)
  const avgSimilarity = segments.reduce((sum, s) => sum + s.similarity, 0) / segments.length

  let explanation = ''
  let evidenceStrength = 0
  let recommendedAction = ''

  if (segments.length >= 2) {
    const hasStructural = categories.includes('structural')
    const hasSemantic = categories.includes('semantic')
    const uniqueCompanies = [...new Set(companies)]

    if (hasStructural && hasSemantic) {
      explanation = `Analysis reveals significant structural and semantic similarities between ${uniqueCompanies.length} companies' bid documents (average similarity: ${Math.round(avgSimilarity * 100)}%). This may indicate:

1. **Shared Document Templates**: Multiple companies may be using identical bid document templates or formats
2. **Content Plagiarism**: Evidence of direct copy-paste or minor rewording of content
3. **Common Authorship**: Documents may have been written by the same person or team
4. **Bid Rigging Suspicion**: Companies may have pre-coordinated or shared information

Recommend further investigation of relationships between these companies, including:
- Company directors or shareholder relationships
- Historical bidding collaboration records
- Similarities in office addresses or contact information`

      evidenceStrength = avgSimilarity > 0.9 ? 95 : avgSimilarity > 0.8 ? 85 : 70
      recommendedAction = 'Recommend immediate in-depth investigation and suspension of tender decision process'
    } else if (categories.includes('numerical')) {
      explanation = `Suspicious coincidences found in bid prices or numerical specifications. ${uniqueCompanies.length} companies show suspiciously similar pricing strategies, which may indicate price coordination or bid rigging behavior.`
      evidenceStrength = 75
      recommendedAction = 'Recommend investigating potential price coordination between bidding companies'
    }
  }

  return {
    groupId: `group-${Date.now()}`,
    segments,
    explanation,
    evidenceStrength,
    recommendedAction
  }
}

export function AIInsightsPanel({ selectedSegments }: AIInsightsPanelProps) {
  if (selectedSegments.length === 0) {
    return (
      <div className="h-full flex flex-col">
        <div className="text-center flex-1 flex items-center justify-center p-6">
          <div>
            <Brain className="w-12 h-12 text-slate-300 mx-auto mb-3" />
            <h4 className="text-sm font-medium text-slate-900 mb-2">Select Suspicious Segments</h4>
            <p className="text-slate-600 text-xs">
              Select 2 or more suspicious segments for AI analysis of potential relationships and risks
            </p>
          </div>
        </div>
      </div>
    )
  }

  const insight = generateAIInsight(selectedSegments)

  return (
    <div className="h-full flex flex-col">
      {/* Evidence Strength */}
      <div className="p-4 border-b border-slate-200">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium text-slate-700">Evidence Strength</span>
          <span className="text-sm font-bold text-purple-600">
            {insight.evidenceStrength}%
          </span>
        </div>
        <div className="w-full bg-slate-200 rounded-full h-2">
          <div 
            className="bg-gradient-to-r from-purple-500 to-purple-600 h-2 rounded-full transition-all duration-500"
            style={{ width: `${insight.evidenceStrength}%` }}
          />
        </div>
      </div>

      {/* Selected Segments Summary */}
      <div className="p-4 border-b border-slate-200">
        <div className="flex items-center space-x-2 mb-2">
          <FileText className="w-4 h-4 text-slate-600" />
          <span className="text-sm font-medium text-slate-900">
            Analysis Segments ({selectedSegments.length})
          </span>
        </div>
        <div className="space-y-1">
          {selectedSegments.slice(0, 3).map((segment, index) => (
            <div key={segment.id} className="text-xs text-slate-600 truncate">
              {index + 1}. {segment.bidCompany} - {segment.content.substring(0, 30)}...
            </div>
          ))}
          {selectedSegments.length > 3 && (
            <div className="text-xs text-slate-500">
              +{selectedSegments.length - 3} more segments
            </div>
          )}
        </div>
      </div>

      {/* Analysis Content */}
      <div className="flex-1 overflow-y-auto p-4">
        <div className="space-y-4">
          {/* Risk Assessment */}
          <div className="bg-red-50 border border-red-200 rounded-lg p-3">
            <div className="flex items-center space-x-2 mb-2">
              <AlertTriangle className="w-4 h-4 text-red-600" />
              <span className="text-sm font-medium text-red-900">Risk Assessment</span>
            </div>
            <p className="text-xs text-red-800">
              {insight.evidenceStrength >= 80 ? 'High Risk' : 
               insight.evidenceStrength >= 60 ? 'Medium Risk' : 'Low Risk'}
              - Multiple suspicious indicators found, recommend further investigation
            </p>
          </div>

          {/* AI Analysis */}
          <div>
            <h4 className="text-sm font-semibold text-slate-900 mb-2 flex items-center">
              <TrendingUp className="w-3 h-3 mr-1 text-blue-600" />
              In-depth Analysis
            </h4>
            <div className="text-xs text-slate-700 leading-relaxed whitespace-pre-line bg-slate-50 p-3 rounded-lg">
              {insight.explanation}
            </div>
          </div>

          {/* Recommended Actions */}
          <div>
            <h4 className="text-sm font-semibold text-slate-900 mb-2">Recommended Actions</h4>
            <div className="bg-amber-50 border border-amber-200 rounded-lg p-3">
              <p className="text-xs text-amber-800">
                {insight.recommendedAction}
              </p>
            </div>
          </div>

          {/* Statistical Breakdown */}
          <div>
            <h4 className="text-sm font-semibold text-slate-900 mb-2">Statistical Analysis</h4>
            <div className="grid grid-cols-2 gap-2 text-xs">
              <div className="bg-slate-50 p-2 rounded-lg">
                <div className="text-slate-600 mb-1">Companies</div>
                <div className="font-medium text-slate-900">
                  {[...new Set(selectedSegments.map(s => s.bidCompany))].length}
                </div>
              </div>
              <div className="bg-slate-50 p-2 rounded-lg">
                <div className="text-slate-600 mb-1">Avg Similarity</div>
                <div className="font-medium text-slate-900">
                  {Math.round(selectedSegments.reduce((sum, s) => sum + s.similarity, 0) / selectedSegments.length * 100)}%
                </div>
              </div>
              <div className="bg-slate-50 p-2 rounded-lg">
                <div className="text-slate-600 mb-1">Categories</div>
                <div className="font-medium text-slate-900">
                  {[...new Set(selectedSegments.map(s => s.category))].length}
                </div>
              </div>
              <div className="bg-slate-50 p-2 rounded-lg">
                <div className="text-slate-600 mb-1">High Risk</div>
                <div className="font-medium text-slate-900">
                  {selectedSegments.filter(s => s.suspicionLevel === 'critical' || s.suspicionLevel === 'high').length}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="p-4 border-t border-slate-200 flex-shrink-0">
        <div className="space-y-2">
          <button type="button" className="w-full flex items-center justify-center space-x-2 px-3 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors text-xs">
            <Download className="w-3 h-3" />
            <span>Export Analysis Report</span>
          </button>
          
          <button type="button" className="w-full flex items-center justify-center space-x-2 px-3 py-2 border border-slate-300 text-slate-700 rounded-lg hover:bg-slate-50 transition-colors text-xs">
            <AlertTriangle className="w-3 h-3" />
            <span>Flag for Investigation</span>
          </button>
        </div>
      </div>
    </div>
  )
}