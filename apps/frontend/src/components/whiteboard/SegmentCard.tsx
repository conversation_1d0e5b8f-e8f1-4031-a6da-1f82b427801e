import { useState, useRef, useEffect } from 'react'
import { Alert<PERSON>riangle, Building2, FileText } from 'lucide-react'
import type { SuspiciousSegment, Position } from '../../types'

interface SegmentCardProps {
  segment: SuspiciousSegment
  isSelected: boolean
  isDragging: boolean
  onMove: (segmentId: string, position: Position) => void
  onSelect: (segmentId: string, isMultiSelect: boolean) => void
  className?: string
  style?: React.CSSProperties
}

export function SegmentCard({ 
  segment, 
  isSelected, 
  isDragging, 
  onMove, 
  onSelect,
  className = '',
  style 
}: SegmentCardProps) {
  const [dragStart, setDragStart] = useState<Position | null>(null)
  const cardRef = useRef<HTMLDivElement>(null)

  const handleMouseDown = (e: React.MouseEvent) => {
    if (e.button !== 0) return // Only left click
    
    const rect = cardRef.current?.getBoundingClientRect()
    if (!rect) return

    const startX = e.clientX - segment.position.x
    const startY = e.clientY - segment.position.y

    setDragStart({
      x: startX,
      y: startY
    })
    
    onSelect(segment.id, e.metaKey || e.ctrlKey)
    e.preventDefault()
    e.stopPropagation()
  }

  useEffect(() => {
    if (!dragStart) return

    const handleMouseMove = (e: MouseEvent) => {
      const container = cardRef.current?.parentElement
      if (!container) return

      const containerRect = container.getBoundingClientRect()
      
      const newPosition: Position = {
        x: Math.max(0, Math.min(
          container.scrollWidth - 300, // Card width
          e.clientX - dragStart.x
        )),
        y: Math.max(0, Math.min(
          container.scrollHeight - 160, // Card height
          e.clientY - dragStart.y
        ))
      }

      onMove(segment.id, newPosition)
    }

    const handleMouseUp = () => {
      setDragStart(null)
    }

    document.addEventListener('mousemove', handleMouseMove)
    document.addEventListener('mouseup', handleMouseUp)

    return () => {
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseup', handleMouseUp)
    }
  }, [dragStart, segment.id, onMove])

  const getSuspicionIcon = () => {
    switch (segment.suspicionLevel) {
      case 'critical':
        return <AlertTriangle className="w-4 h-4 text-red-600" />
      case 'high':
        return <AlertTriangle className="w-4 h-4 text-orange-600" />
      case 'medium':
        return <AlertTriangle className="w-4 h-4 text-yellow-600" />
      case 'low':
        return <AlertTriangle className="w-4 h-4 text-gray-600" />
    }
  }

  const getCategoryBadge = () => {
    const labels = {
      semantic: 'Semantic',
      structural: 'Structural',
      numerical: 'Numerical',
      stylometry: 'Style'
    }
    
    const colors = {
      semantic: 'bg-blue-100 text-blue-700',
      structural: 'bg-red-100 text-red-700',
      numerical: 'bg-green-100 text-green-700',
      stylometry: 'bg-purple-100 text-purple-700'
    }

    return (
      <span className={`px-2 py-1 rounded-full text-xs font-medium ${colors[segment.category]}`}>
        {labels[segment.category]}
      </span>
    )
  }

  return (
    <div
      ref={cardRef}
      style={style}
      className={`
        w-72 bg-white border-2 rounded-lg shadow-sm cursor-move select-none
        transition-all duration-200 hover:shadow-md
        ${isSelected ? 'ring-2 ring-blue-500 ring-offset-2' : ''}
        ${isDragging ? 'shadow-lg scale-105' : ''}
        ${className}
      `}
      onMouseDown={handleMouseDown}
    >
      {/* Header */}
      <div className="p-3 border-b border-gray-200 bg-gray-50 rounded-t-lg">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            {getSuspicionIcon()}
            <span className="text-sm font-medium text-gray-700">
              Similarity: {Math.round(segment.similarity * 100)}%
            </span>
          </div>
          {getCategoryBadge()}
        </div>
      </div>

      {/* Content */}
      <div className="p-3">
        <div className="mb-3">
          <div className="flex items-center space-x-2 text-xs text-gray-600 mb-2">
            <Building2 className="w-3 h-3" />
            <span className="truncate">{segment.bidCompany}</span>
            <FileText className="w-3 h-3" />
            <span>Bid segment</span>
          </div>
        </div>

        <div className="text-sm text-gray-800 leading-relaxed">
          {segment.content.length > 120 
            ? `${segment.content.substring(0, 120)}...` 
            : segment.content
          }
        </div>

        {segment.similarSegments.length > 0 && (
          <div className="mt-3 pt-2 border-t border-gray-200">
            <div className="text-xs text-gray-600">
              Similar to {segment.similarSegments.length} segments
            </div>
          </div>
        )}
      </div>
    </div>
  )
}