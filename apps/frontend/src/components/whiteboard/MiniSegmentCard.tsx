import { Eye, Building2 } from 'lucide-react'
import type { SuspiciousSegment } from '../../types'

interface MiniSegmentCardProps {
  segment: SuspiciousSegment
  onDragStart: () => void
  onDragEnd: () => void
  onFocus?: (segmentId: string) => void
}

export function MiniSegmentCard({ segment, onFocus }: MiniSegmentCardProps) {
  const getSuspicionColor = (level: SuspiciousSegment['suspicionLevel']) => {
    switch (level) {
      case 'critical':
        return 'border-l-red-500 bg-red-50'
      case 'high':
        return 'border-l-orange-500 bg-orange-50'
      case 'medium':
        return 'border-l-yellow-500 bg-yellow-50'
      case 'low':
        return 'border-l-gray-500 bg-gray-50'
    }
  }

  const handleFocus = () => {
    if (onFocus) {
      onFocus(segment.id)
    }
  }

  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault()
      handleFocus()
    }
  }

  return (
    <button
      type="button"
      className={`
        p-3 m-2 border-l-4 bg-white border border-slate-200 rounded-r-lg
        cursor-pointer transition-all duration-200 hover:shadow-sm hover:scale-[1.02] w-full text-left
        ${getSuspicionColor(segment.suspicionLevel)}
      `}
      onClick={handleFocus}
      onKeyDown={handleKeyDown}
      aria-label={`Focus on segment from ${segment.bidCompany} with ${Math.round(segment.similarity * 100)}% similarity`}
    >
      <div className="flex items-start space-x-2">
        <Eye className="w-4 h-4 text-slate-400 mt-1 flex-shrink-0" />
        
        <div className="flex-1 min-w-0">
          <div className="flex items-center space-x-2 mb-2">
            <Building2 className="w-3 h-3 text-slate-500" />
            <span className="text-xs text-slate-600 truncate">
              {segment.bidCompany}
            </span>
            <span className="text-xs font-medium text-slate-700">
              {Math.round(segment.similarity * 100)}%
            </span>
          </div>
          
          <p className="text-sm text-slate-800 line-clamp-2 leading-tight">
            {segment.content.length > 80 
              ? `${segment.content.substring(0, 80)}...` 
              : segment.content
            }
          </p>

          {segment.similarSegments.length > 0 && (
            <div className="mt-2 text-xs text-slate-500">
              Similar to {segment.similarSegments.length} segments
            </div>
          )}
        </div>
      </div>
    </button>
  )
}