import { Link, RotateCcw, Download, Brain, Group, X } from 'lucide-react'

interface WhiteboardControlsProps {
  selectedCount: number
  onGroupCreate: () => void
  onAIAnalysis: () => void
  onClearSelection: () => void
}

export function WhiteboardControls({ selectedCount, onGroupCreate, onAIAnalysis, onClearSelection }: WhiteboardControlsProps) {
  return (
    <div className="bg-white shadow-lg rounded-lg border border-slate-200 px-4 py-3 max-w-4xl">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <h2 className="text-lg font-semibold text-slate-900">Detective Board</h2>
          
          {selectedCount > 0 && (
            <div className="flex items-center space-x-2 bg-blue-50 border border-blue-200 rounded-lg px-3 py-1">
              <span className="text-sm font-medium text-blue-800">
                {selectedCount} segments selected
              </span>
              <button
                type="button"
                onClick={onClearSelection}
                className="p-1 hover:bg-blue-100 rounded transition-colors"
              >
                <X className="w-4 h-4 text-blue-600" />
              </button>
            </div>
          )}
        </div>

        <div className="flex items-center space-x-2">
          {/* Selection Actions */}
          {selectedCount >= 2 && (
            <button
              type="button"
              onClick={onGroupCreate}
              className="flex items-center space-x-2 px-3 py-2 bg-green-600 text-white rounded-lg text-sm font-medium hover:bg-green-700 transition-colors"
            >
              <Group className="w-4 h-4" />
              <span>Create Group</span>
            </button>
          )}

          {selectedCount >= 1 && (
            <button 
              type="button"
              onClick={onAIAnalysis}
              className="flex items-center space-x-2 px-3 py-2 bg-purple-600 text-white rounded-lg text-sm font-medium hover:bg-purple-700 transition-colors"
            >
              <Brain className="w-4 h-4" />
              <span>AI Analysis</span>
            </button>
          )}

          {/* Divider */}
          <div className="w-px h-6 bg-slate-300" />

          {/* General Actions */}
          <button type="button" className="p-2 text-slate-600 hover:bg-slate-100 rounded-lg transition-colors" title="Show connections">
            <Link className="w-5 h-5" />
          </button>

          <button type="button" className="p-2 text-slate-600 hover:bg-slate-100 rounded-lg transition-colors" title="Reset layout">
            <RotateCcw className="w-5 h-5" />
          </button>

          <button type="button" className="p-2 text-slate-600 hover:bg-slate-100 rounded-lg transition-colors" title="Export report">
            <Download className="w-5 h-5" />
          </button>
        </div>
      </div>

      {/* Status Bar */}
      <div className="flex items-center justify-between mt-2 text-sm text-slate-600">
        <div className="flex items-center space-x-4">
          <span>Analysis Status: Complete</span>
          <span>•</span>
          <span>Suspicious findings: 9</span>
          <span>•</span>
          <span>High risk: 3</span>
        </div>
        
        <div className="text-xs text-slate-500">
          Tip: Drag segments to canvas, Ctrl/Cmd+click for multi-select
        </div>
      </div>
    </div>
  )
}