import { Handle, Position } from '@xyflow/react'
import type { AnalysisType } from '../../types'

interface GroupNodeData extends Record<string, unknown> {
  label: string
  category: AnalysisType
  nodeCount: number
  averageSimilarity: number
}

function GroupNode({ data }: { data: GroupNodeData }) {
  const getCategoryColor = (category: AnalysisType) => {
    switch (category) {
      case 'semantic':
        return 'border-blue-300 bg-blue-50/50'
      case 'structural':
        return 'border-red-300 bg-red-50/50'
      case 'numerical':
        return 'border-green-300 bg-green-50/50'
      case 'stylometry':
        return 'border-purple-300 bg-purple-50/50'
      default:
        return 'border-gray-300 bg-gray-50/50'
    }
  }

  const getCategoryIcon = (category: AnalysisType) => {
    switch (category) {
      case 'semantic':
        return '🔤'
      case 'structural':
        return '📋'
      case 'numerical':
        return '🔢'
      case 'stylometry':
        return '✍️'
      default:
        return '📄'
    }
  }

  return (
    <div className={`
      w-full h-full 
      border-2 border-dashed rounded-lg 
      ${getCategoryColor(data.category)}
      relative overflow-visible
    `}>
      {/* Group Header */}
      <div className="absolute -top-14 -left-3 px-3 py-1 bg-white border border-slate-200 rounded-md shadow-sm z-10">
        <div className="flex items-center space-x-2">
          <span className="text-lg">{getCategoryIcon(data.category)}</span>
          <div className="text-xs">
            <div className="font-medium text-slate-800">{data.label}</div>
            <div className="text-slate-500">
              {data.nodeCount} nodes • {Math.round(data.averageSimilarity * 100)}% avg similarity
            </div>
          </div>
        </div>
      </div>

      {/* Group Content Area - now minimal since children will be positioned inside */}
      <div className="w-full h-full p-2 pt-8 opacity-20">
        <div className="w-full h-full border border-dashed border-slate-300 rounded-md" />
      </div>

      {/* Handles for connections */}
      <Handle type="source" position={Position.Top} style={{ opacity: 0 }} />
      <Handle type="target" position={Position.Bottom} style={{ opacity: 0 }} />
    </div>
  )
}

export { GroupNode, type GroupNodeData }