import { ChevronDown, ChevronRight } from 'lucide-react'
import { useState } from 'react'
import type { AnalysisCategory } from '../../types'
import { MiniSegmentCard } from './MiniSegmentCard'

interface CategorySectionProps {
  category: AnalysisCategory
  onSegmentDragStart: (segmentId: string) => void
  onSegmentDragEnd: () => void
  onSegmentFocus?: (segmentId: string) => void
}

export function CategorySection({ category, onSegmentDragStart, onSegmentDragEnd, onSegmentFocus }: CategorySectionProps) {
  const [isExpanded, setIsExpanded] = useState(true)

  const getCategoryIcon = (type: string) => {
    switch (type) {
      case 'semantic':
        return '🔤'
      case 'structural':
        return '📋'
      case 'numerical':
        return '🔢'
      case 'stylometry':
        return '✍️'
      default:
        return '📄'
    }
  }

  return (
    <div className="border-b border-slate-200">
      <button
        type="button"
        onClick={() => setIsExpanded(!isExpanded)}
        className="w-full p-4 flex items-center justify-between hover:bg-slate-50 transition-colors"
      >
        <div className="flex items-center space-x-3">
          <span className="text-lg">{getCategoryIcon(category.type)}</span>
          <div className="text-left">
            <h4 className="font-medium text-slate-900">{category.name}</h4>
            <p className="text-xs text-slate-600">{category.description}</p>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <span className="bg-slate-200 text-slate-700 text-xs px-2 py-1 rounded-full">
            {category.segments.length}
          </span>
          {isExpanded ? (
            <ChevronDown className="w-4 h-4 text-slate-400" />
          ) : (
            <ChevronRight className="w-4 h-4 text-slate-400" />
          )}
        </div>
      </button>

      {isExpanded && (
        <div className="pb-2">
          {category.segments.length === 0 ? (
            <div className="px-4 py-6 text-center text-sm text-slate-500">
              No suspicious findings of this type
            </div>
          ) : (
            <div className="space-y-2 px-2">
              {category.segments.map((segment) => (
                <MiniSegmentCard
                  key={segment.id}
                  segment={segment}
                  onDragStart={() => onSegmentDragStart(segment.id)}
                  onDragEnd={onSegmentDragEnd}
                  onFocus={onSegmentFocus}
                />
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  )
}