import { useState, useCallback, useMemo, useEffect } from 'react'
import { useUpdateSegmentPosition } from '../../hooks/useAnalysis'
import { 
  ReactFlow, 
  MiniMap, 
  Controls, 
  Background, 
  useNodesState, 
  useEdgesState,
  ReactFlowProvider,
  type Node,
  type Edge,
  type NodeChange,
  ConnectionMode,
  BackgroundVariant
} from '@xyflow/react'
import '@xyflow/react/dist/style.css'
import type { SuspiciousSegment, AnalysisCategory } from '../../types'
import { WhiteboardControls } from './WhiteboardControls'
import { UnifiedSidebar, type TabType } from './UnifiedSidebar'
import { GroupNode, type GroupNodeData } from './GroupNode'

interface DetectiveWhiteboardProps {
  suspiciousSegments: SuspiciousSegment[]
  tenderId: string
  isLoading?: boolean
}

interface SegmentNodeData {
  segment: SuspiciousSegment
  isSelected: boolean
  onSelect: (segmentId: string, isMultiSelect: boolean) => void
  comparedSegments?: SuspiciousSegment[] // Add compared segments data
}

function SegmentNode({ data }: { data: SegmentNodeData }) {
  const getSuspicionColor = (level: SuspiciousSegment['suspicionLevel']) => {
    switch (level) {
      case 'critical':
        return 'border-red-500 bg-red-50'
      case 'high':
        return 'border-orange-500 bg-orange-50'
      case 'medium':
        return 'border-yellow-500 bg-yellow-50'
      case 'low':
        return 'border-gray-500 bg-gray-50'
    }
  }

  const getProblemDescription = (category: string, similarity: number) => {
    const similarityPercent = Math.round(similarity * 100)
    switch (category) {
      case 'semantic':
        return `Content Copied (${similarityPercent}%)`
      case 'structural':
        return `Same Format (${similarityPercent}%)`
      case 'numerical':
        return `Price Coordination (${similarityPercent}%)`
      case 'stylometry':
        return `Same Author (${similarityPercent}%)`
      default:
        return `Suspicious Match (${similarityPercent}%)`
    }
  }

  // Find the most similar segment for comparison
  const mostSimilarSegment = data.comparedSegments?.find(seg => 
    data.segment.similarSegments.includes(seg.id)
  ) || data.comparedSegments?.[0]

  return (
    <button 
      type="button"
      className={`text-left rounded-lg border-2 shadow-sm cursor-pointer transition-all duration-200 w-64 max-w-64 ${getSuspicionColor(data.segment.suspicionLevel)} ${data.isSelected ? 'ring-2 ring-blue-500 shadow-lg' : 'hover:shadow-md'}`}
      onClick={(e) => data.onSelect(data.segment.id, e.ctrlKey || e.metaKey)}
      style={{ minHeight: mostSimilarSegment ? '380px' : '240px' }} // Increased height for better readability
    >
      {/* Header */}
      <div className="p-3 pb-2 flex justify-between items-start">
        <span className="text-xs font-medium text-slate-700 truncate flex-1">
          {getProblemDescription(data.segment.category, data.segment.similarity)}
        </span>
        <span className={`text-xs px-2 py-1 rounded-full ${getSuspicionColor(data.segment.suspicionLevel)} border ml-2 flex-shrink-0`}>
          {data.segment.suspicionLevel}
        </span>
      </div>

      {/* Scrollable content area */}
      <div className="px-3 pb-3 max-h-80 overflow-y-auto">
        {/* Original segment content */}
        <div className="mb-3">
          <div className="text-xs text-slate-600 mb-1 flex items-center">
            <span className="w-2 h-2 bg-blue-500 rounded-full mr-1" />
            <span className="font-medium truncate">{data.segment.bidCompany}</span>
          </div>
          <div className="text-xs text-slate-800 leading-tight break-words bg-blue-50 p-2 rounded border-l-2 border-blue-500 max-h-320 overflow-y-auto">
            {data.segment.content}
          </div>
        </div>

        {/* Comparison segment if available */}
        {mostSimilarSegment && (
          <div className="mb-3">
            <div className="text-xs text-slate-600 mb-1 flex items-center">
              <span className="w-2 h-2 bg-orange-500 rounded-full mr-1" />
              <span className="font-medium truncate">{mostSimilarSegment.bidCompany}</span>
            </div>
            <div className="text-xs text-slate-800 leading-tight break-words bg-orange-50 p-2 rounded border-l-2 border-orange-500 max-h-20 overflow-y-auto">
              {mostSimilarSegment.content}
            </div>
          </div>
        )}
        
        {/* Footer info */}
        <div className="text-xs text-slate-500 space-y-1 mt-2 pt-2 border-t border-slate-200">
          <div className="flex justify-between">
            <span>Similarity:</span>
            <span className="font-medium">{Math.round(data.segment.similarity * 100)}%</span>
          </div>
          {data.segment.similarSegments.length > 0 && (
            <div className="text-xs text-slate-400">
              ↔ {data.segment.similarSegments.length} similar segment{data.segment.similarSegments.length !== 1 ? 's' : ''}
            </div>
          )}
        </div>
      </div>
    </button>
  )
}

const ANALYSIS_CATEGORIES: AnalysisCategory[] = [
  {
    type: 'semantic',
    name: 'Semantic Similarity',
    description: 'Semantic similarity analysis of text content',
    color: 'bg-blue-100 border-blue-300',
    segments: []
  },
  {
    type: 'structural',
    name: 'Structural Similarity',
    description: 'Document structure and format similarity',
    color: 'bg-red-100 border-red-300',
    segments: []
  },
  {
    type: 'numerical',
    name: 'Numerical Analysis',
    description: 'Suspicious coincidences in prices and values',
    color: 'bg-green-100 border-green-300',
    segments: []
  },
  {
    type: 'stylometry',
    name: 'Writing Style',
    description: 'Text style and authorship analysis',
    color: 'bg-purple-100 border-purple-300',
    segments: []
  }
]

const nodeTypes = {
  segmentNode: SegmentNode,
  group: GroupNode,
}

function WhiteboardContent({ suspiciousSegments, isLoading }: DetectiveWhiteboardProps) {
  // Use suspiciousSegments directly instead of storing in state
  const segments = suspiciousSegments
  const [selectedSegments, setSelectedSegments] = useState<string[]>([])
  const [showAIInsights, setShowAIInsights] = useState(false)
  const [sidebarTab, setSidebarTab] = useState<TabType>('categories')
  const [triggerSidebarExpansion, setTriggerSidebarExpansion] = useState(false)
  
  // React Query mutation for updating segment positions
  const updateSegmentPosition = useUpdateSegmentPosition()


  const handleSegmentSelect = useCallback((segmentId: string, isMultiSelect: boolean) => {
    if (isMultiSelect) {
      setSelectedSegments(prev => 
        prev.includes(segmentId) 
          ? prev.filter(id => id !== segmentId)
          : [...prev, segmentId]
      )
    } else {
      setSelectedSegments([segmentId])
      // Auto-switch to focused tab when a single node is selected
      setSidebarTab('focused')
    }
    // Trigger sidebar expansion when any selection is made
    setTriggerSidebarExpansion(true)
  }, [])

  // Convert segments to React Flow nodes with collision prevention and grouping
  const initialNodes: Node[] = useMemo(() => {
    // If no segments, return empty array
    if (segments.length === 0) {
      return []
    }
    
    const segmentNodeWidth = 256 // w-64 = 16rem = 256px
    const segmentNodeHeight = 380 // Increased height for better readability with comparison snippets
    const padding = 20 // Increased padding between nodes within group
    const groupPadding = 60 // Increased padding between groups
    
    // Group segments by category
    const groupedSegments = new Map<string, SuspiciousSegment[]>()
    for (const segment of segments) {
      const key = segment.category
      if (!groupedSegments.has(key)) {
        groupedSegments.set(key, [])
      }
      groupedSegments.get(key)?.push(segment)
    }
    
    const nodes: Node[] = []
    const groupArray = Array.from(groupedSegments.entries())
    
    // Calculate grid layout for groups - 2x2 grid for overview
    const groupsPerRow = 2 // 2x2 grid layout for better overview
    let currentX = 120 // More margin from edge
    let currentY = 120 // More margin from top
    
    // IMPORTANT: Create parent nodes first, then children (React Flow requirement)
    groupArray.forEach(([category, groupSegments], groupIndex) => {
      if (groupSegments.length === 0) return
      
      // Calculate group size based on content with proper margins
      const nodesPerRow = Math.max(1, Math.min(4, Math.ceil(Math.sqrt(groupSegments.length)))) // Allow up to 4 nodes per row
      const rows = Math.ceil(groupSegments.length / nodesPerRow)
      
      // Better calculation: account for left/right margins + node widths + spacing between nodes
      const calculatedWidth = nodesPerRow * segmentNodeWidth + (nodesPerRow - 1) * padding + padding * 5 // More padding for margins
      const calculatedHeight = rows * segmentNodeHeight + (rows - 1) * padding + padding * 5 + 120 // More padding + header space
      
      const actualGroupWidth = Math.max(1000, calculatedWidth) // Increased minimum width for better spacing
      const actualGroupHeight = Math.max(500, calculatedHeight) // Increased minimum height to fit taller nodes
      
      const groupId = `group-${category}`
      const avgSimilarity = groupSegments.reduce((sum, seg) => sum + seg.similarity, 0) / groupSegments.length
      
      // Create parent group node with explicit dimensions
      nodes.push({
        id: groupId,
        type: 'group',
        position: { x: currentX, y: currentY },
        data: {
          label: ANALYSIS_CATEGORIES.find(cat => cat.type === category)?.name || category,
          category: category as AnalysisType,
          nodeCount: groupSegments.length,
          averageSimilarity: avgSimilarity
        } as GroupNodeData,
        style: {
          width: actualGroupWidth,
          height: actualGroupHeight,
          zIndex: 0,
        },
        draggable: true,
        selectable: false,
      })
      
      // Move to next group position for layout - 2x2 grid
      if ((groupIndex + 1) % groupsPerRow === 0) {
        currentX = 150 // Reset to left margin
        currentY += actualGroupHeight + groupPadding
      } else {
        currentX += actualGroupWidth + groupPadding
      }
    })
    
    // Now create child nodes (must come after parent nodes)
    let groupLayoutX = 150
    let groupLayoutY = 150
    
    groupArray.forEach(([category, groupSegments], groupIndex) => {
      if (groupSegments.length === 0) return
      
      const nodesPerRow = Math.max(1, Math.min(4, Math.ceil(Math.sqrt(groupSegments.length))))
      const rows = Math.ceil(groupSegments.length / nodesPerRow)
      
      // Use same calculation as parent creation
      const calculatedWidth = nodesPerRow * segmentNodeWidth + (nodesPerRow - 1) * padding + padding * 5
      const calculatedHeight = rows * segmentNodeHeight + (rows - 1) * padding + padding * 5 + 120
      
      const actualGroupWidth = Math.max(1200, calculatedWidth)
      const actualGroupHeight = Math.max(600, calculatedHeight)
      
      const groupId = `group-${category}`
      
      // Position child nodes relative to parent (0,0 is top-left of parent)
      groupSegments.forEach((segment, index) => {
        const row = Math.floor(index / nodesPerRow)
        const col = index % nodesPerRow
        
        // Use saved position if available, otherwise calculate default position
        let relativeX: number
        let relativeY: number
        if (segment.position && (segment.position.x !== 0 || segment.position.y !== 0)) {
          // Use saved position (adjust if needed for parent coordinates)
          relativeX = segment.position.x
          relativeY = segment.position.y
        } else {
          // Calculate default position relative to parent node with better margins
          const leftMargin = padding * 3 // More generous left margin for breathing room
          const topMargin = padding * 4 // More header space + margin for group header
          
          relativeX = leftMargin + (col * (segmentNodeWidth + padding))
          relativeY = topMargin + (row * (segmentNodeHeight + padding))
        }
        
        nodes.push({
          id: segment.id,
          type: 'segmentNode',
          position: { x: relativeX, y: relativeY }, // Relative to parent
          data: {
            segment,
            isSelected: selectedSegments.includes(segment.id),
            onSelect: handleSegmentSelect,
            comparedSegments: groupSegments, // Pass all segments in the group for comparison
          },
          parentId: groupId,
          extent: 'parent', // Constrain to parent bounds
          draggable: true,
        })
      })
      
      // Update layout position tracking - 2x2 grid
      if ((groupIndex + 1) % groupsPerRow === 0) {
        groupLayoutX = 150
        groupLayoutY += actualGroupHeight + groupPadding
      } else {
        groupLayoutX += actualGroupWidth + groupPadding
      }
    })
    
    return nodes
  }, [segments, handleSegmentSelect, selectedSegments])

  // Convert segment connections to React Flow edges
  const initialEdges: Edge[] = useMemo(() => {
    const edges: Edge[] = []
    for (const segment of segments) {
      for (const similarId of segment.similarSegments) {
        // Only create edge once (from lower ID to higher ID to avoid duplicates)
        if (segment.id < similarId) {
          edges.push({
            id: `${segment.id}-${similarId}`,
            source: segment.id,
            target: similarId,
            type: 'straight',
            style: {
              stroke: '#64748b',
              strokeWidth: Math.max(1, segment.similarity * 3),
              strokeDasharray: '5,5',
              opacity: 0.6,
            },
            animated: segment.similarity > 0.7,
          })
        }
      }
    }
    return edges
  }, [segments])

  const [nodes, setNodes, onNodesChange] = useNodesState<Node>([])
  const [edges, , onEdgesChange] = useEdgesState(initialEdges)

  // Handle node position changes and save to backend
  const handleNodesChange = useCallback((changes: NodeChange[]) => {
    onNodesChange(changes)
    
    // Find position changes and debounce API calls
    const positionChanges = changes.filter(change => 
      change.type === 'position' && change.dragging === false
    )
    
    if (positionChanges.length > 0) {
      // Debounce position saves
      setTimeout(() => {
        for (const change of positionChanges) {
          if (change.position && change.id && !change.id.startsWith('group-')) {
            // Save individual node position using React Query mutation
            updateSegmentPosition.mutate({
              segmentId: change.id,
              position: {
                x: change.position.x,
                y: change.position.y
              }
            })
          }
        }
      }, 500) // 500ms debounce
    }
  }, [onNodesChange, updateSegmentPosition])
  
  // Update nodes when initialNodes changes
  useEffect(() => {
    setNodes(initialNodes)
  }, [initialNodes, setNodes])
  
  // Reset trigger after expansion
  useEffect(() => {
    if (triggerSidebarExpansion) {
      const timer = setTimeout(() => {
        setTriggerSidebarExpansion(false)
      }, 100)
      return () => clearTimeout(timer)
    }
  }, [triggerSidebarExpansion])
  

  const categorizeSegments = useCallback(() => {
    return ANALYSIS_CATEGORIES.map(category => ({
      ...category,
      segments: segments.filter(segment => segment.category === category.type)
    }))
  }, [segments])

  const handleGroupCreate = useCallback(() => {
    if (selectedSegments.length < 2) return
    
    console.log('Creating group from segments:', selectedSegments)
    setSelectedSegments([])
  }, [selectedSegments])

  const handleAIAnalysis = useCallback(() => {
    if (selectedSegments.length < 1) return
    setShowAIInsights(true)
  }, [selectedSegments])

  const handleFocusSegment = useCallback((segmentId: string) => {
    const node = nodes.find(n => n.id === segmentId)
    if (node) {
      // Center the view on the node
      setNodes(prevNodes => 
        prevNodes.map(n => ({
          ...n,
          selected: n.id === segmentId
        }))
      )
      setSelectedSegments([segmentId])
      setSidebarTab('focused')
    }
  }, [nodes, setNodes])

  // Update node selection when selectedSegments changes
  useEffect(() => {
    setNodes(prevNodes => 
      prevNodes.map(node => ({
        ...node,
        data: {
          ...node.data,
          isSelected: selectedSegments.includes(node.id),
          onSelect: handleSegmentSelect,
        }
      }))
    )
  }, [selectedSegments, handleSegmentSelect, setNodes])

  const categories = categorizeSegments()

  return (
    <div className="h-full bg-slate-50 relative overflow-hidden">
      {/* React Flow Canvas - Full Screen */}
      <div className="absolute inset-0">
          <ReactFlow
            nodes={nodes}
            edges={edges}
            onNodesChange={handleNodesChange}
            onEdgesChange={onEdgesChange}
            nodeTypes={nodeTypes}
            connectionMode={ConnectionMode.Loose}
            fitView
            attributionPosition="bottom-left"
            snapToGrid={true}
            snapGrid={[20, 20]}
            nodesDraggable={true}
            nodesConnectable={false}
            elementsSelectable={true}
            panOnScroll={true}
            fitViewOptions={{
              includeHiddenNodes: false,
              minZoom: 0.5,
              maxZoom: 1.5
            }}
          >
            <Background variant={BackgroundVariant.Dots} gap={20} size={1} />
            <Controls position="center-left" />
            <MiniMap 
              position="bottom-left"
              style={{
                height: 120,
                width: 200,
              }}
              zoomable
              pannable
              nodeColor={(node) => {
                const nodeData = node.data as unknown as SegmentNodeData
                const segment = nodeData?.segment
                if (!segment) return '#e2e8f0'
                
                switch (segment.suspicionLevel) {
                  case 'critical': return '#ef4444'
                  case 'high': return '#f97316' 
                  case 'medium': return '#eab308'
                  case 'low': return '#6b7280'
                  default: return '#e2e8f0'
                }
              }}
            />
          </ReactFlow>

          {/* Loading State */}
          {isLoading && (
            <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-80">
              <div className="flex flex-col items-center space-y-4">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600" />
                <p className="text-slate-600">Loading suspicious segments...</p>
              </div>
            </div>
          )}

          {/* Empty State */}
          {!isLoading && segments.length === 0 && (
            <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
              <div className="text-center space-y-4">
                <div className="w-16 h-16 bg-slate-200 rounded-full mx-auto flex items-center justify-center">
                  🔍
                </div>
                <div>
                  <h3 className="text-lg font-medium text-slate-900 mb-2">Start Analysis</h3>
                  <p className="text-slate-600">No suspicious segments found yet</p>
                </div>
              </div>
            </div>
          )}
        </div>

      {/* Floating Detective Board Header */}
      {/* <div className="absolute top-4 left-4 z-10">
        <WhiteboardControls
          selectedCount={selectedSegments.length}
          onGroupCreate={handleGroupCreate}
          onAIAnalysis={handleAIAnalysis}
          onClearSelection={() => setSelectedSegments([])}
        />
      </div> */}

      {/* Unified Sidebar */}
      <UnifiedSidebar
        categories={categories}
        selectedSegments={segments.filter(s => selectedSegments.includes(s.id))}
        showAIInsights={showAIInsights}
        onSegmentFocus={handleFocusSegment}
        onCloseAIInsights={() => setShowAIInsights(false)}
        activeTab={sidebarTab}
        onTabChange={setSidebarTab}
        shouldExpand={triggerSidebarExpansion}
      />
    </div>
  )
}

export function DetectiveWhiteboard(props: DetectiveWhiteboardProps) {
  return (
    <ReactFlowProvider>
      <WhiteboardContent {...props} />
    </ReactFlowProvider>
  )
}