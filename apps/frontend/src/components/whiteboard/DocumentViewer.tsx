import { useState, useRef, useEffect } from "react";
import { X, <PERSON>, Unlink, <PERSON>ert<PERSON>riangle, FileX, RefreshCw } from "lucide-react";
import { Dialog, DialogContent } from "../ui/dialog";
import { Button } from "../ui/button";
import type { SuspiciousSegment } from "../../types";
import { bidService } from "../../services/bidService";

interface DocumentViewerProps {
	// Legacy props for whiteboard
	isOpen?: boolean;
	onClose?: () => void;
	segment?: SuspiciousSegment;
	// New props for comparison page
	documents?: {
		id: string;
		name: string;
		path: string;
		highlightChunk?: string;
	}[];
	mode?: 'dialog' | 'side-by-side';
	selectedFinding?: {
		id: string;
		findingType: string;
		riskLevel: string;
		summary: string;
		evidence: {
			text1: string;
			text2: string;
			score: number;
		};
	};
}

interface DocumentContent {
	content: string;
	documentUrl?: string;
	isLoading: boolean;
	error: string | null;
	type: "text" | "pdf";
}

export function DocumentViewer({
	isOpen = false,
	onClose = () => {},
	segment,
	documents = [],
	mode = 'dialog',
	selectedFinding,
}: DocumentViewerProps) {
	const [syncScrolling, setSyncScrolling] = useState(true);
	const [primaryDoc, setPrimaryDoc] = useState<DocumentContent>({
		content: "",
		isLoading: true,
		error: null,
		type: "text",
	});
	const [comparisonDoc, setComparisonDoc] = useState<DocumentContent>({
		content: "",
		isLoading: true,
		error: null,
		type: "text",
	});

	const primaryScrollRef = useRef<HTMLDivElement>(null);
	const comparisonScrollRef = useRef<HTMLDivElement>(null);
	const isScrollingSynced = useRef(false);

	// Load document contents when dialog opens
	useEffect(() => {
		if (isOpen && segment) {
			loadDocumentContent(segment.sourceBid, setPrimaryDoc);
			if (segment.comparisonBid) {
				loadDocumentContent(segment.comparisonBid, setComparisonDoc);
			} else {
				setComparisonDoc({
					content: "",
					isLoading: false,
					error: "No comparison document available",
					type: "text",
				});
			}
		}
	}, [isOpen, segment]);

	const loadDocumentContent = async (
		bidId: string,
		setter: React.Dispatch<React.SetStateAction<DocumentContent>>,
	) => {
		setter((prev) => ({ ...prev, isLoading: true, error: null }));

		try {
			// First check file info to determine if it's a PDF
			const fileInfo = await bidService.getBidFileInfo(bidId);

			console.log("File info for bid:", bidId, fileInfo);

			// Check if file exists - if we got file_info back, the file exists
			if (!fileInfo.file_info || !fileInfo.file_info.file_size) {
				setter({
					content: "",
					isLoading: false,
					error: `File not found: ${fileInfo.file_name}`,
					type: "text",
				});
				return;
			}

			// Check if it's a PDF file - using correct API field names
			const isPdf =
				fileInfo.file_name.toLowerCase().endsWith(".pdf") ||
				fileInfo.file_info.file_type === "application/pdf";

			if (isPdf) {
				// For PDF files, provide the document URL for embedding
				const documentUrl = bidService.getBidDocumentUrl(bidId);
				console.log("Loading PDF from URL:", documentUrl);

				// Test if the URL is accessible
				try {
					const testResponse = await fetch(documentUrl, { method: "HEAD" });
					console.log(
						"Document URL test response:",
						testResponse.status,
						testResponse.statusText,
					);
					if (!testResponse.ok) {
						console.error(
							"Document URL not accessible:",
							testResponse.status,
							testResponse.statusText,
						);
					}
				} catch (error) {
					console.error("Error testing document URL:", error);
				}

				setter({
					content: "",
					documentUrl,
					isLoading: false,
					error: null,
					type: "pdf",
				});
			} else {
				// For non-PDF files, get extracted text content
				const extractedContent = await bidService.getBidExtractedContent(bidId);

				console.log("Extracted content for bid:", bidId, extractedContent);

				if (
					!extractedContent.content.text ||
					extractedContent.content.text.trim() === ""
				) {
					setter({
						content: "",
						isLoading: false,
						error: "Document content could not be extracted or is empty",
						type: "text",
					});
					return;
				}

				setter({
					content: extractedContent.content.text,
					isLoading: false,
					error: null,
					type: "text",
				});
			}
		} catch (error) {
			console.error("Error loading document content:", error);
			setter({
				content: "",
				isLoading: false,
				error:
					error instanceof Error ? error.message : "Failed to load document",
				type: "text",
			});
		}
	};

	const handleScroll = (source: "primary" | "comparison") => {
		if (!syncScrolling || isScrollingSynced.current) return;

		// Skip scroll sync for PDF documents as iframe scrolling is complex
		const sourceDoc = source === "primary" ? primaryDoc : comparisonDoc;
		const targetDoc = source === "primary" ? comparisonDoc : primaryDoc;

		if (sourceDoc.type === "pdf" || targetDoc.type === "pdf") {
			return;
		}

		isScrollingSynced.current = true;

		const sourceRef =
			source === "primary" ? primaryScrollRef : comparisonScrollRef;
		const targetRef =
			source === "primary" ? comparisonScrollRef : primaryScrollRef;

		if (sourceRef.current && targetRef.current) {
			const sourceElement = sourceRef.current;
			const targetElement = targetRef.current;

			const scrollPercentage =
				sourceElement.scrollTop /
				(sourceElement.scrollHeight - sourceElement.clientHeight);
			const targetScrollTop =
				scrollPercentage *
				(targetElement.scrollHeight - targetElement.clientHeight);

			targetElement.scrollTop = targetScrollTop;
		}

		// Reset sync flag after a short delay
		setTimeout(() => {
			isScrollingSynced.current = false;
		}, 100);
	};

	const retryLoadDocument = (
		bidId: string,
		setter: React.Dispatch<React.SetStateAction<DocumentContent>>,
	) => {
		loadDocumentContent(bidId, setter);
	};

	const renderDocumentPanel = (
		title: string,
		company: string,
		content: DocumentContent,
		onScroll: () => void,
		scrollRef: React.RefObject<HTMLDivElement | null>,
		onRetry?: () => void,
		color: "blue" | "orange" = "blue",
	) => {
		const colorClasses = {
			blue: "border-blue-500 bg-blue-50",
			orange: "border-orange-500 bg-orange-50",
		};

		return (
			<div className="flex-1 flex flex-col">
				<div
					className={`border-l-4 ${colorClasses[color]} p-2 border-b border-gray-200`}
				>
					<h3 className="text-sm font-semibold text-gray-900">{title}</h3>
					<p className="text-xs text-gray-600">{company}</p>
				</div>

				<div className="flex-1 relative">
					{content.isLoading ? (
						<div className="h-full flex items-center justify-center">
							<div className="flex items-center space-x-2 text-gray-500">
								<RefreshCw className="w-5 h-5 animate-spin" />
								<span>Loading document...</span>
							</div>
						</div>
					) : content.error ? (
						<div className="h-full flex items-center justify-center p-6">
							<div className="text-center">
								<div className="flex justify-center mb-3">
									{content.error.includes("not found") ? (
										<FileX className="w-12 h-12 text-red-400" />
									) : (
										<AlertTriangle className="w-12 h-12 text-yellow-400" />
									)}
								</div>
								<h4 className="text-sm font-medium text-gray-900 mb-2">
									{content.error.includes("not found")
										? "File Not Found"
										: "Failed to Load"}
								</h4>
								<p className="text-xs text-gray-600 mb-4">{content.error}</p>
								{onRetry && (
									<Button
										variant="outline"
										size="sm"
										onClick={onRetry}
										className="text-xs"
									>
										<RefreshCw className="w-3 h-3 mr-1" />
										Retry
									</Button>
								)}
							</div>
						</div>
					) : content.type === "pdf" && content.documentUrl ? (
						<div className="h-full">
							<iframe
								src={content.documentUrl}
								className="w-full h-full border-none"
								title={`${title} - PDF Document`}
								onLoad={(e) => {
									console.log("PDF loaded successfully:", content.documentUrl);
									console.log("Iframe element:", e.target);
								}}
								onError={(e) => {
									console.error("Failed to load PDF:", content.documentUrl);
									console.error("Error event:", e);
									if (onRetry) {
										onRetry();
									}
								}}
							/>
						</div>
					) : (
						<div
							ref={scrollRef}
							onScroll={onScroll}
							className="h-full overflow-y-auto p-6 bg-white"
						>
							<div className="prose prose-sm max-w-none text-gray-800 leading-relaxed">
								<pre className="whitespace-pre-wrap font-sans text-sm">
									{content.content}
								</pre>
							</div>
						</div>
					)}
				</div>
			</div>
		);
	};

	// Side-by-side mode for comparison page
	if (mode === 'side-by-side') {
		return (
			<div className="h-full flex flex-col bg-white">
				{/* Header */}
				<div className="flex items-center justify-between px-4 py-3 border-b border-slate-200 bg-slate-50">
					<div className="text-sm font-semibold text-slate-900">
						Document Comparison
						{selectedFinding && (
							<span className="text-xs font-normal text-slate-600 ml-2">
								{Math.round(selectedFinding.evidence.score * 100)}% similarity
							</span>
						)}
					</div>
					<div className="flex items-center space-x-2">
						<Button
							variant="outline"
							size="sm"
							onClick={() => setSyncScrolling(!syncScrolling)}
							className={`text-xs px-3 py-1 h-8 ${syncScrolling ? "bg-green-50 border-green-200 text-green-700" : ""}`}
						>
							{syncScrolling ? (
								<Link className="w-3 h-3 mr-1" />
							) : (
								<Unlink className="w-3 h-3 mr-1" />
							)}
							{syncScrolling ? "Sync" : "No Sync"}
						</Button>
					</div>
				</div>

				{/* Document panels */}
				<div className="flex flex-1 overflow-hidden">
					{documents.length >= 2 && (
						<>
							{/* Document 1 */}
							<div className="flex-1 flex flex-col border-r border-slate-200">
								<div className="px-4 py-2 bg-blue-50 border-b border-blue-200">
									<h3 className="text-sm font-semibold text-blue-900">{documents[0].name}</h3>
									<p className="text-xs text-blue-700">{documents[0].path}</p>
								</div>
								<div className="flex-1 p-4 overflow-auto bg-white" ref={primaryScrollRef} onScroll={() => handleScroll("primary")}>
									{selectedFinding ? (
										<div className="space-y-4">
											<div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
												<h4 className="text-sm font-medium text-blue-900 mb-2">Highlighted Section</h4>
												<div className="text-sm text-blue-800 font-mono leading-relaxed bg-blue-100 p-3 rounded">
													{selectedFinding.evidence.text1}
												</div>
											</div>
											<div className="text-xs text-slate-500">
												Click to navigate to this section in the full document
											</div>
										</div>
									) : (
										<div className="text-center py-8 text-slate-500">
											<p>Select a finding to view document details</p>
										</div>
									)}
								</div>
							</div>

							{/* Document 2 */}
							<div className="flex-1 flex flex-col">
								<div className="px-4 py-2 bg-orange-50 border-b border-orange-200">
									<h3 className="text-sm font-semibold text-orange-900">{documents[1].name}</h3>
									<p className="text-xs text-orange-700">{documents[1].path}</p>
								</div>
								<div className="flex-1 p-4 overflow-auto bg-white" ref={comparisonScrollRef} onScroll={() => handleScroll("comparison")}>
									{selectedFinding ? (
										<div className="space-y-4">
											<div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
												<h4 className="text-sm font-medium text-orange-900 mb-2">Highlighted Section</h4>
												<div className="text-sm text-orange-800 font-mono leading-relaxed bg-orange-100 p-3 rounded">
													{selectedFinding.evidence.text2}
												</div>
											</div>
											<div className="text-xs text-slate-500">
												Click to navigate to this section in the full document
											</div>
										</div>
									) : (
										<div className="text-center py-8 text-slate-500">
											<p>Select a finding to view document details</p>
										</div>
									)}
								</div>
							</div>
						</>
					)}
				</div>
			</div>
		)
	}

	// Legacy dialog mode for whiteboard
	if (!segment) return null;

	return (
		<Dialog open={isOpen} onOpenChange={onClose}>
			<DialogContent
				className="max-w-[98vw] w-[98vw] h-[98vh] p-0 sm:max-w-[98vw] flex flex-col overflow-hidden gap-0"
				showCloseButton={false}
			>
				{/* Custom compact header */}
				<div className="flex items-center justify-between px-3 py-2 border-b border-gray-200 bg-white">
					<div className="text-sm font-semibold">
						Document Comparison - {Math.round(segment.similarity * 100)}% Match
						<span className="text-xs font-normal text-gray-500 ml-2">
							({segment.category} | {segment.suspicionLevel})
						</span>
					</div>
					<div className="flex items-center space-x-1">
						{primaryDoc.type === "pdf" || comparisonDoc.type === "pdf" ? (
							<div className="text-xs text-gray-500 px-2 py-1">
								Sync unavailable for PDFs
							</div>
						) : (
							<Button
								variant="outline"
								size="sm"
								onClick={() => setSyncScrolling(!syncScrolling)}
								className={`text-xs px-2 py-1 h-7 ${syncScrolling ? "bg-green-50 border-green-200 text-green-700" : ""}`}
							>
								{syncScrolling ? (
									<Link className="w-3 h-3 mr-1" />
								) : (
									<Unlink className="w-3 h-3 mr-1" />
								)}
								{syncScrolling ? "Sync On" : "Sync Off"}
							</Button>
						)}
						<Button
							variant="ghost"
							size="sm"
							onClick={onClose}
							className="h-7 w-7 p-0"
						>
							<X className="w-4 h-4" />
						</Button>
					</div>
				</div>

				<div className="flex flex-1">
					{/* Primary document */}
					{renderDocumentPanel(
						segment.bidCompany,
						"Primary Document",
						primaryDoc,
						() => handleScroll("primary"),
						primaryScrollRef,
						() => retryLoadDocument(segment.sourceBid, setPrimaryDoc),
						"blue",
					)}

					{/* Divider */}
					<div className="w-px bg-gray-300" />

					{/* Comparison document */}
					{renderDocumentPanel(
						segment.comparisonCompany || "Unknown",
						"Comparison Document",
						comparisonDoc,
						() => handleScroll("comparison"),
						comparisonScrollRef,
						segment.comparisonBid
							? () =>
									segment.comparisonBid && retryLoadDocument(segment.comparisonBid, setComparisonDoc)
							: undefined,
						"orange",
					)}
				</div>
			</DialogContent>
		</Dialog>
	);
}
