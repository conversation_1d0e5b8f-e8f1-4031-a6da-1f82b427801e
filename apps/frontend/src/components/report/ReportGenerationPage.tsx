import { useState, useEffect } from 'react'
import { Link } from '@tanstack/react-router'
import { BasicYooptaEditor } from './BasicYooptaEditor'
import { useTenders } from '../../hooks'
import { backupMockTender } from '../../data/mockData'

interface ReportGenerationPageProps {
  projectId: string
}

interface ReportData {
  id: string
  title: string
  project: {
    id: string
    name: string
    department: string
    budget: number
    date: string
  }
  executiveSummary: string
  keyFindings: {
    id: string
    title: string
    riskLevel: 'low' | 'medium' | 'high' | 'critical'
    summary: string
    evidence: string
    companies: string[]
    recommendation: string
  }[]
  statistics: {
    totalComparisons: number
    suspiciousFindings: number
    criticalFindings: number
    companiesInvolved: number
  }
  generatedAt: string
  status: 'draft' | 'final'
}

interface ComparisonCard {
  id: string
  title: string
  type: 'document_layout_embedding' | 'layout_ocr_comparison' | 'llm_service_price_extraction'
  riskLevel: 'low' | 'medium' | 'high' | 'critical'
  companies: string[]
  similarity: number
}

export function ReportGenerationPage({ projectId }: ReportGenerationPageProps) {
  const [reportData, setReportData] = useState<ReportData | null>(null)
  const [isGenerating, setIsGenerating] = useState(false)
  const [selectedFindings, setSelectedFindings] = useState<string[]>([])
  const [comparisonCards] = useState<ComparisonCard[]>([
    {
      id: 'card-1',
      title: 'Safety Protocol Specifications Similarity',
      type: 'document_layout_embedding',
      riskLevel: 'critical',
      companies: ['CleanPro Services', 'Environmental Solutions'],
      similarity: 0.92
    },
    {
      id: 'card-2',
      title: 'Document Layout Structure Analysis',
      type: 'layout_ocr_comparison',
      riskLevel: 'high',
      companies: ['Professional Cleaning', 'CleanPro Services'],
      similarity: 0.98
    },
    {
      id: 'card-3',
      title: 'Pricing Coordination Detection',
      type: 'llm_service_price_extraction',
      riskLevel: 'medium',
      companies: ['Environmental Solutions', 'Professional Cleaning'],
      similarity: 0.87
    }
  ])
  
  const { data: tenders } = useTenders()
  const tendersData = tenders || [backupMockTender]
  const project = tendersData.find(t => t.id === projectId)

  // Generate initial report data
  useEffect(() => {
    if (project && !reportData) {
      generateInitialReport()
    }
  }, [project, reportData])

  const generateInitialReport = async () => {
    setIsGenerating(true)
    
    // Simulate AI generation delay
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    const mockReportData: ReportData = {
      id: `report-${Date.now()}`,
      title: `Audit Report - ${project?.title}`,
      project: {
        id: project?.id || '',
        name: project?.title || '',
        department: project?.department || '',
        budget: project?.budget || 0,
        date: new Date().toISOString().split('T')[0]
      },
      executiveSummary: `This audit report presents findings from the analysis of ${project?.bids.length} bid documents submitted for the ${project?.title} tender. Our comprehensive analysis using advanced AI algorithms has identified several areas of concern that warrant further investigation.

The analysis employed three cutting-edge methodologies: Document Layout + Vector Similarity for intelligent content chunking and semantic comparison, Layout OCR Boundary Comparison for positional document structure analysis, and LLM Service & Price Extraction for automated pricing pattern detection and suspicious coordination identification.

Key concerns identified include potential document sharing between bidders, coordinated pricing strategies, suspicious positioning similarities that suggest template reuse, and AI-detected patterns in service descriptions that may indicate collusion or lack of independent bid preparation.`,
      keyFindings: [
        {
          id: 'finding-1',
          title: 'Critical Document Layout + Vector Similarity',
          riskLevel: 'critical',
          summary: 'Document layout chunking and vector embedding analysis revealed 92% similarity in safety protocol sections between multiple bidders.',
          evidence: 'AI-powered document layout analysis identified identical paragraph structures and technical specifications with matching technical errors in scaffold specifications across CleanPro Services and Environmental Solutions.',
          companies: ['CleanPro Services', 'Environmental Solutions'],
          recommendation: 'Immediate investigation required for potential document sharing or common source of technical specifications. Request clarification on independent preparation of bids and review original source documents.'
        },
        {
          id: 'finding-2',
          title: 'OCR Boundary Box Layout Coordination',
          riskLevel: 'high',
          summary: 'Layout OCR boundary comparison detected 98% positional similarity in document structure across multiple submissions.',
          evidence: 'Boundary box analysis revealed identical element positioning, margins, font choices, and section headers in pages 5-8, suggesting use of common templates or document sharing.',
          companies: ['Professional Cleaning', 'CleanPro Services', 'Environmental Solutions'],
          recommendation: 'Verify independent preparation of bid documents. Conduct detailed forensic analysis of document metadata and creation timestamps. Review compliance with tender requirements for original submissions.'
        },
        {
          id: 'finding-3',
          title: 'LLM-Detected Service & Price Coordination',
          riskLevel: 'medium',
          summary: 'Large Language Model analysis identified suspicious pricing coordination patterns and identical service descriptions.',
          evidence: 'LLM extraction detected three companies using identical 15% markup on materials, similar labor cost calculations ($2,500 ±$50 per day), and matching service description phrasing that indicates potential coordination.',
          companies: ['Environmental Solutions', 'Professional Cleaning', 'CleanPro Services'],
          recommendation: 'Review pricing methodologies and request detailed cost breakdowns. Conduct market analysis to verify pricing reasonableness. Investigate potential communication between bidders regarding pricing strategies.'
        }
      ],
      statistics: {
        totalComparisons: 21,
        suspiciousFindings: 8,
        criticalFindings: 2,
        companiesInvolved: project?.bids.length || 0
      },
      generatedAt: new Date().toISOString(),
      status: 'draft'
    }
    
    setReportData(mockReportData)
    setSelectedFindings(mockReportData.keyFindings.map(f => f.id))
    setIsGenerating(false)
  }

  const handleRegenerateReport = () => {
    setReportData(null)
    generateInitialReport()
  }

  const handleExportReport = (format: 'pdf' | 'word' | 'html') => {
    // Mock export functionality
    console.log(`Exporting report in ${format} format`)
    // In real implementation, this would call an API to generate the export
  }

  const handleSaveReport = (updatedData: Partial<ReportData>) => {
    if (reportData) {
      setReportData({ ...reportData, ...updatedData })
    }
  }

  if (!project) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-slate-900 mb-2">Project Not Found</h2>
          <p className="text-slate-600 mb-4">Could not find the specified project.</p>
          <Link 
            to="/dashboard"
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Back to Dashboard
          </Link>
        </div>
      </div>
    )
  }

  if (isGenerating || !reportData) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="w-16 h-16 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto" />
          <div>
            <h2 className="text-xl font-semibold text-slate-900 mb-2">Generating Audit Report</h2>
            <p className="text-slate-600">AI is analyzing findings and generating comprehensive report...</p>
          </div>
          <div className="max-w-md mx-auto bg-slate-100 rounded-full h-2">
            <div className="bg-blue-600 h-2 rounded-full animate-pulse" style={{ width: '75%' }} />
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="bg-white border-b border-slate-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Link 
              to="/dashboard"
              className="p-2 hover:bg-slate-100 rounded-lg transition-colors"
            >
              <svg className="w-5 h-5 text-slate-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <title>Back</title>
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </Link>
            
            <div>
              <h1 className="text-xl font-semibold text-slate-900">{reportData.title}</h1>
              <p className="text-sm text-slate-600 mt-1">
                {reportData.project.department} • Generated {new Date(reportData.generatedAt).toLocaleDateString()}
              </p>
            </div>
          </div>

          <div className="flex items-center space-x-3">
            
            <button
              type="button"
              onClick={handleRegenerateReport}
              className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
            >
              Regenerate with AI
            </button>
            
            <div className="relative group">
              <button type="button" className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                Export Report
              </button>
              <div className="absolute right-0 top-full mt-2 w-48 bg-white border border-slate-200 rounded-lg shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
                <div className="py-1">
                  <button 
                    type="button"
                    onClick={() => handleExportReport('pdf')}
                    className="w-full text-left px-4 py-2 text-sm text-slate-700 hover:bg-slate-50"
                  >
                    Export as PDF
                  </button>
                  <button 
                    type="button"
                    onClick={() => handleExportReport('word')}
                    className="w-full text-left px-4 py-2 text-sm text-slate-700 hover:bg-slate-50"
                  >
                    Export as Word
                  </button>
                  <button 
                    type="button"
                    onClick={() => handleExportReport('html')}
                    className="w-full text-left px-4 py-2 text-sm text-slate-700 hover:bg-slate-50"
                  >
                    Export as HTML
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 overflow-hidden">
        <BasicYooptaEditor 
          reportData={reportData}
          onSave={handleSaveReport}
          selectedFindings={selectedFindings}
          onFindingsChange={setSelectedFindings}
          comparisonCards={comparisonCards}
        />
      </div>
    </div>
  )
}