import { Upload, FileText, Building2, DollarSign, AlertTriangle, CheckCircle, Eye, BarChart3 } from 'lucide-react'
import type { Tender } from '../../types'
import { BidUploadForm } from '../upload/BidUploadForm'
import { useTenderBids, useStartAnalysis } from '../../hooks'

interface BidManagementProps {
  tender: Tender
}

export function BidManagement({ tender }: BidManagementProps) {
  const { data: bids, isLoading: bidsLoading } = useTenderBids(tender.id)
  const startAnalysisMutation = useStartAnalysis()
  
  // Use bids from API if available, otherwise use tender.bids as fallback
  const bidsList = bids || tender.bids
  
  const formatPrice = (price: number) => {
    return `$${(price / 10000).toLocaleString()}0K`
  }

  const getBidStatusColor = (price: number, budget: number) => {
    const ratio = price / budget
    if (ratio > 0.95) return 'text-red-600 bg-red-50'
    if (ratio > 0.85) return 'text-orange-600 bg-orange-50'
    return 'text-green-600 bg-green-50'
  }

  const getSimilarityLevel = (bidId: string) => {
    // Mock similarity scores for demo
    const similarities = {
      'bid-001': { level: 'high', score: 89, similar: ['bid-002'] },
      'bid-002': { level: 'high', score: 89, similar: ['bid-001'] },
      'bid-003': { level: 'low', score: 23, similar: [] },
      'bid-004': { level: 'medium', score: 67, similar: ['bid-001', 'bid-002'] }
    }
    return similarities[bidId as keyof typeof similarities] || { level: 'low', score: 0, similar: [] }
  }

  const getSimilarityColor = (level: string) => {
    switch (level) {
      case 'high': return 'text-red-600 bg-red-50 border-red-200'
      case 'medium': return 'text-orange-600 bg-orange-50 border-orange-200'
      case 'low': return 'text-green-600 bg-green-50 border-green-200'
      default: return 'text-gray-600 bg-gray-50 border-gray-200'
    }
  }

  return (
    <div className="h-full bg-slate-50 overflow-y-auto">
      {/* Header */}
      <div className="bg-white border-b border-slate-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-slate-900">{tender.title}</h1>
            <div className="flex items-center space-x-6 mt-2 text-sm text-slate-600">
              <div className="flex items-center space-x-2">
                <Building2 className="w-4 h-4" />
                <span>{tender.department}</span>
              </div>
              <div className="flex items-center space-x-2">
                <DollarSign className="w-4 h-4" />
                <span>Budget: {formatPrice(tender.budget)}</span>
              </div>
              <div className="flex items-center space-x-2">
                <FileText className="w-4 h-4" />
                <span>{bidsList.length} bids</span>
                {bidsLoading && <span className="text-blue-600">(loading...)</span>}
              </div>
            </div>
          </div>
          
          <div className="flex items-center space-x-3">
            <button 
              type="button"
              onClick={() => startAnalysisMutation.mutate(tender.id)}
              disabled={startAnalysisMutation.isPending || bidsList.length === 0}
              className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
            >
              <BarChart3 className="w-4 h-4" />
              <span>
                {startAnalysisMutation.isPending ? 'Starting Analysis...' : 'Start Analysis'}
              </span>
            </button>
          </div>
        </div>
      </div>

      <div className="p-6">
        {/* Upload Section */}
        {bidsList.length === 0 ? (
          <BidUploadForm 
            tenderId={tender.id} 
            onBidsUploaded={() => window.location.reload()} 
            onBack={() => {}} 
            tenderData={tender} 
          />
        ) : (
          <div className="mb-6">
            <div className="bg-white rounded-lg border border-slate-200 p-4">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-medium text-slate-900">Add Bid Documents</h3>
                  <p className="text-sm text-slate-600 mt-1">Upload more company bid documents for comparative analysis</p>
                </div>
                <button type="button" className="flex items-center space-x-2 px-4 py-2 border border-slate-300 rounded-lg hover:bg-slate-50 transition-colors">
                  <Upload className="w-4 h-4" />
                  <span>Upload Files</span>
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Bids List */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold text-slate-900">Bid Document Analysis</h2>
            <div className="text-sm text-slate-600">
              Total {bidsList.length} bids{bidsLoading ? ' (loading...)' : ', found 3 suspicious similarity groups'}
            </div>
          </div>

          <div className="grid gap-4">
            {bidsList.map((bid) => {
              const similarity = getSimilarityLevel(bid.id)
              const priceRatio = bid.totalPrice / tender.budget

              return (
                <div key={bid.id} className="bg-white rounded-lg border border-slate-200 p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <h3 className="text-lg font-semibold text-slate-900">{bid.companyName}</h3>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium border ${getSimilarityColor(similarity.level)}`}>
                          Similarity: {similarity.score}%
                        </span>
                      </div>
                      
                      <div className="grid grid-cols-3 gap-4 text-sm">
                        <div>
                          <span className="text-slate-600">File Name:</span>
                          <span className="font-medium">{bid.fileName}</span>
                        </div>
                        <div>
                          <span className="text-slate-600">Upload Date:</span>
                          <span className="font-medium">{bid.uploadDate}</span>
                        </div>
                        <div>
                          <span className="text-slate-600">Bid Amount:</span>
                          <span className={`font-medium ${getBidStatusColor(bid.totalPrice, tender.budget)}`}>
                            {formatPrice(bid.totalPrice)} ({Math.round(priceRatio * 100)}%)
                          </span>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      <button type="button" className="flex items-center space-x-2 px-3 py-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors">
                        <Eye className="w-4 h-4" />
                        <span>View</span>
                      </button>
                    </div>
                  </div>

                  {/* Similarity Warnings */}
                  {similarity.level !== 'low' && (
                    <div className="border-t border-slate-200 pt-4">
                      <div className="flex items-start space-x-3">
                        <AlertTriangle className="w-5 h-5 text-orange-500 mt-0.5" />
                        <div>
                          <h4 className="font-medium text-slate-900 mb-1">Suspicious Similarity Detected</h4>
                          <p className="text-sm text-slate-600 mb-2">
                            This bid shows high similarity with {similarity.similar.length} other bids, further investigation recommended.
                          </p>
                          <div className="flex items-center space-x-4 text-sm">
                            <span className="text-slate-600">
                              Similar Bids:
                              {similarity.similar.map(similarId => {
                                const similarBid = bidsList.find(b => b.id === similarId)
                                return similarBid?.companyName
                              }).join(', ')}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Analysis Categories */}
                  <div className="border-t border-slate-200 pt-4 mt-4">
                    <div className="grid grid-cols-4 gap-3 text-sm">
                      <div className="text-center">
                        <div className="text-xs text-slate-600 mb-1">Semantic</div>
                        <div className="font-medium text-red-600">92%</div>
                      </div>
                      <div className="text-center">
                        <div className="text-xs text-slate-600 mb-1">Structural</div>
                        <div className="font-medium text-red-600">98%</div>
                      </div>
                      <div className="text-center">
                        <div className="text-xs text-slate-600 mb-1">Numerical</div>
                        <div className="font-medium text-orange-600">85%</div>
                      </div>
                      <div className="text-center">
                        <div className="text-xs text-slate-600 mb-1">Stylometric</div>
                        <div className="font-medium text-orange-600">89%</div>
                      </div>
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        </div>
      </div>
    </div>
  )
}