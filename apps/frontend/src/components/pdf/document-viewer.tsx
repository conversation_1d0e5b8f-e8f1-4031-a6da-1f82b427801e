'use client';

import { Viewer } from '@react-pdf-viewer/core';
import '@react-pdf-viewer/core/lib/styles/index.css';
import { defaultLayoutPlugin } from '@react-pdf-viewer/default-layout';
import { pageNavigationPlugin } from '@react-pdf-viewer/page-navigation';
import '@react-pdf-viewer/core/lib/styles/index.css';
import '@react-pdf-viewer/default-layout/lib/styles/index.css';

import React, { useCallback, useEffect, useRef, useState } from 'react';
import { createHighlightPlugin } from './pdf-highlight-plugin';

// Import PDF.js configuration
import '../../lib/pdf-config';

// Cache for storing PDF blobs
const pdfCache = new Map<string, Blob>();
const blobUrlCache = new Map<string, string>();

export interface OCRHighlight {
  id: string;
  pageIndex: number;
  boundingBox: {
    left: number;
    top: number;
    width: number;
    height: number;
  };
  polygon?: Array<{ x: number; y: number }>;
  text: string;
  layoutType: string;
  confidence: number;
  chunkId?: string;
}

export interface EnhancedPDFViewerProps {
  url: string;
  documentId?: string;
  highlights?: OCRHighlight[];
  options?: PDFViewerOptions;
  onHighlightClick?: (highlight: OCRHighlight) => void;
  onHighlightHover?: (highlight: OCRHighlight | null) => void;
  enableInteractions?: boolean;
}

export type PDFViewerOptions = {
  initialPage?: number;
  timestamp?: number;
  selectedHighlightChunkId?: number | null;
  onWhitespaceClick?: () => void;
};



// Plugin is now imported from separate file

export function EnhancedPDFViewer({
  url,
  // documentId,
  highlights = [],
  options,
  onHighlightClick,
  onHighlightHover,
  enableInteractions = true,
}: EnhancedPDFViewerProps) {
  const [pdfUrl, setPdfUrl] = useState<string | null>(null);
  const currentPdfUrl = useRef<string | null>(null);
  const [isDocumentLoaded, setIsDocumentLoaded] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const handleHighlightClick = useCallback(
    (highlight: OCRHighlight) => {
      console.log('Highlight clicked:', highlight);
      onHighlightClick?.(highlight);
    },
    [onHighlightClick]
  );

  const handleHighlightHover = useCallback(
    (highlight: OCRHighlight | null) => {
      // setHoveredHighlight(highlight);
      onHighlightHover?.(highlight);
    },
    [onHighlightHover]
  );

  const pageNavigationPluginInstance = useRef(pageNavigationPlugin()).current;
  const defaultLayoutPluginInstance = useRef(defaultLayoutPlugin()).current;

  // Create highlight plugin that updates with highlights
  const highlightPluginInstance = React.useMemo(() => {
    console.log('🟠 Creating highlight plugin with:', {
      highlightsCount: highlights.length,
      selectedHighlightChunkId: options?.selectedHighlightChunkId,
      enableInteractions,
      highlightSample: highlights
        .slice(0, 3)
        .map((h) => ({ id: h.id, chunkId: h.chunkId, text: h.text.substring(0, 30) })),
    });

    return createHighlightPlugin({
      highlights,
      onHighlightClick: handleHighlightClick,
      onHighlightHover: handleHighlightHover,
      enableInteractions,
      selectedHighlightChunkId: options?.selectedHighlightChunkId,
    });
  }, [
    highlights,
    handleHighlightClick,
    handleHighlightHover,
    enableInteractions,
    options?.selectedHighlightChunkId,
  ]);

  // Load and cache PDF data
  useEffect(() => {
    const loadPDF = async () => {
      console.log('Enhanced PDFViewer: Loading PDF from URL:', url);
      setIsDocumentLoaded(false);
      setError(null);
      setIsLoading(true);

      if (blobUrlCache.has(url)) {
        const cachedUrl = blobUrlCache.get(url);
        if (cachedUrl) {
          setPdfUrl(cachedUrl);
          currentPdfUrl.current = cachedUrl;
          setIsLoading(false);
          return;
        }
      }

      try {
        let pdfBlob: Blob;

        if (pdfCache.has(url)) {
          const cachedBlob = pdfCache.get(url);
          if (cachedBlob) {
            pdfBlob = cachedBlob;
          } else {
            throw new Error('Cached blob is null');
          }
        } else {
          const response = await fetch(url, {
            method: 'GET',
            headers: {
              Accept: 'application/pdf',
            },
          });

          if (!response.ok) {
            throw new Error(`Failed to fetch PDF: ${response.status} ${response.statusText}`);
          }

          pdfBlob = await response.blob();

          if (!pdfBlob.type.includes('pdf') && pdfBlob.type !== 'application/octet-stream') {
            console.warn('Response is not a PDF, got:', pdfBlob.type);
          }

          pdfCache.set(url, pdfBlob);
        }

        const blobUrl = URL.createObjectURL(pdfBlob);
        blobUrlCache.set(url, blobUrl);
        setPdfUrl(blobUrl);
        currentPdfUrl.current = blobUrl;
        setIsLoading(false);
      } catch (error) {
        console.error('Error loading PDF:', error);
        setError(error instanceof Error ? error.message : 'Failed to load PDF');
        setIsLoading(false);
      }
    };

    loadPDF();

    return () => {
      const urlToCleanup = currentPdfUrl.current;
      if (urlToCleanup) {
        const usageCount = Array.from(blobUrlCache.values()).filter(
          (v) => v === urlToCleanup
        ).length;
        if (usageCount <= 1) {
          URL.revokeObjectURL(urlToCleanup);
          blobUrlCache.delete(url);
        }
      }
    };
  }, [url]);

  // Handle page navigation after document is loaded
  useEffect(() => {
    if (isDocumentLoaded && options?.initialPage) {
      const targetPage = options.initialPage - 1; // Convert to 0-based
      console.log(
        '🔵 Enhanced PDF - Navigating to page:',
        options.initialPage,
        '(0-based:',
        targetPage,
        ')'
      );
      pageNavigationPluginInstance.jumpToPage(targetPage);
    }
  }, [isDocumentLoaded, options?.initialPage, options?.timestamp, pageNavigationPluginInstance]);

  const handleDocumentLoad = () => {
    setIsDocumentLoaded(true);
  };

  if (error) {
    return (
      <div className="flex h-full items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="mb-2 text-red-600">Failed to load PDF</div>
          <div className="text-sm text-gray-500">{error}</div>
          <div className="mt-2 text-xs text-gray-400">URL: {url}</div>
        </div>
      </div>
    );
  }

  if (isLoading || !pdfUrl) {
    return (
      <div className="flex h-full items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="mx-auto mb-2 h-8 w-8 animate-spin rounded-full border-b-2 border-blue-600" />
          <div className="text-gray-600">Loading PDF...</div>
          {highlights.length > 0 && (
            <div className="mt-1 text-sm text-gray-500">
              {highlights.length} highlights available
            </div>
          )}
        </div>
      </div>
    );
  }

  const handleContainerClick = (e: React.MouseEvent) => {
    console.log('🔴 Enhanced PDF container clicked');

    // Check if click was on the actual PDF content area (not toolbar or controls)
    const target = e.target as HTMLElement;
    const isOnPDFArea =
      target.closest('.rpv-core__page') || target.classList.contains('rpv-core__page');

    console.log('🔴 Click details:', {
      target: target.tagName,
      targetClasses: target.className,
      isOnPDFArea,
      hasWhitespaceHandler: !!options?.onWhitespaceClick,
    });

    if (isOnPDFArea && options?.onWhitespaceClick) {
      // Only call whitespace click if clicking on page but not on text, highlights, or other content
      const clickedOnInteractiveElement =
        target.closest('.rpv-core__text-layer') ||
        target.closest('button') ||
        target.closest('a') ||
        target.closest('[data-highlight-id]') || // Our highlight elements
        target.hasAttribute('data-highlight-id');

      console.log('🔴 Interactive element check:', {
        clickedOnInteractiveElement: !!clickedOnInteractiveElement,
        textLayer: !!target.closest('.rpv-core__text-layer'),
        button: !!target.closest('button'),
        link: !!target.closest('a'),
        highlightElement:
          !!target.closest('[data-highlight-id]') || target.hasAttribute('data-highlight-id'),
      });

      if (!clickedOnInteractiveElement) {
        console.log('🔴 Calling whitespace click handler');
        options.onWhitespaceClick();
      } else {
        console.log('🔴 Not calling whitespace click - clicked on interactive element');
      }
    } else {
      console.log('🔴 Not calling whitespace click - not on PDF area or no handler');
    }
  };

  return (
    <div className="relative h-full overflow-y-auto" onClick={handleContainerClick}>
      {/* PDF Viewer */}
      <div className="h-full">
        <Viewer
          fileUrl={pdfUrl}
          defaultScale={1}
          enableSmoothScroll
          plugins={[
            defaultLayoutPluginInstance,
            pageNavigationPluginInstance,
            highlightPluginInstance,
          ]}
          onDocumentLoad={handleDocumentLoad}
        />
      </div>
    </div>
  );
}
