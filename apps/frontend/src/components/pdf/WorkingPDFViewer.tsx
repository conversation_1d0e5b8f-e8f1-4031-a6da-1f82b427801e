// Working PDF viewer using @react-pdf-viewer - replacement for EnhancedDocumentViewer

import React, { useState, useCallback, useMemo, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Loader2, <PERSON><PERSON><PERSON><PERSON>gle, Eye, <PERSON>Off, Filter, Search } from 'lucide-react';
import { EnhancedPDFViewer, type OCRHighlight } from './document-viewer';
import { Button } from '../ui/button';
import { documentService } from '../../services/documentService';

// Import PDF.js configuration
import '../../lib/pdf-config';
import type { 
  DocumentChunk, 
  ChunkInteractionEvent, 
  ChunkFilterOptions,
  DocumentComparisonResult 
} from '../../types/document';

interface WorkingPDFViewerProps {
  documentId: string;
  documentUrl?: string;
  comparisonDocumentId?: string;
  comparisonResult?: DocumentComparisonResult;
  mode?: 'single' | 'comparison';
  showControls?: boolean;
  showChunks?: boolean;
  showOcrOverlay?: boolean;
  initialChunkFilters?: Partial<ChunkFilterOptions>;
  onChunkSelect?: (chunkIds: string[]) => void;
  onChunkCompare?: (chunkId: string) => void;
  className?: string;
}

// Convert DocumentChunk to OCRHighlight format
function convertChunksToHighlights(chunks: DocumentChunk[]): OCRHighlight[] {
  if (chunks.length === 0) return [];

  // Calculate page dimensions from the actual bounding box data
  const calculatePageDimensions = (chunks: DocumentChunk[]) => {
    let maxX = 0;
    let maxY = 0;

    chunks.forEach(chunk => {
      const bbox = chunk.bounding_box;
      // Check if coordinates are normalized (0-1) or in pixels
      const isNormalized = bbox.x1 <= 1 && bbox.y1 <= 1;

      if (!isNormalized) {
        maxX = Math.max(maxX, bbox.x2 || (bbox.x1 + bbox.width));
        maxY = Math.max(maxY, bbox.y2 || (bbox.y1 + bbox.height));
      }
    });

    // If we found pixel coordinates, use them; otherwise use standard PDF dimensions
    if (maxX > 1 && maxY > 1) {
      // Add some padding to account for content near edges
      return { width: maxX * 1.1, height: maxY * 1.1 };
    } else {
      // Standard PDF dimensions
      return { width: 612, height: 792 };
    }
  };

  const pageDimensions = calculatePageDimensions(chunks);
  console.log('🔵 Calculated page dimensions:', pageDimensions);

  return chunks.map((chunk) => {
    const bbox = chunk.bounding_box;

    // Check if coordinates are already normalized (0-1) or in pixels
    const isNormalized = bbox.x1 <= 1 && bbox.y1 <= 1 && bbox.width <= 1 && bbox.height <= 1;

    let normalizedBbox;

    if (isNormalized) {
      // Already normalized coordinates
      normalizedBbox = {
        left: bbox.x1,
        top: bbox.y1,
        width: bbox.width,
        height: bbox.height,
      };
    } else {
      // Pixel coordinates - normalize using calculated dimensions
      normalizedBbox = {
        left: bbox.x1 / pageDimensions.width,
        top: bbox.y1 / pageDimensions.height,
        width: bbox.width / pageDimensions.width,
        height: bbox.height / pageDimensions.height,
      };
    }

    console.log(`🔵 Converting chunk ${chunk.id}:`, {
      original: bbox,
      isNormalized,
      normalized: normalizedBbox,
      pageNumber: chunk.page_number,
      text: chunk.content.substring(0, 30),
    });

    return {
      id: chunk.id,
      pageIndex: chunk.page_number - 1, // Convert to 0-based indexing
      boundingBox: normalizedBbox,
      text: chunk.content || '',
      layoutType: chunk.chunk_type.toUpperCase(),
      confidence: chunk.confidence || 0.8,
      chunkId: chunk.id,
    };
  });
}

export function WorkingPDFViewer({
  documentId,
  documentUrl,
  comparisonDocumentId,
  comparisonResult,
  mode = 'single',
  showControls = true,
  showChunks = true,
  showOcrOverlay = true,
  initialChunkFilters = {},
  onChunkSelect,
  onChunkCompare,
  className = ''
}: WorkingPDFViewerProps) {
  const [chunkFilters, setChunkFilters] = useState<ChunkFilterOptions>({
    chunkTypes: new Set(['text', 'title', 'table', 'image', 'list']),
    similarityThreshold: 0.5,
    pageRange: null,
    searchText: '',
    showOnlyHighlighted: false,
    ...initialChunkFilters
  });

  const [selectedChunks, setSelectedChunks] = useState<string[]>([]);
  const [chunksVisible, setChunksVisible] = useState(showChunks);
  const [hoveredHighlight, setHoveredHighlight] = useState<OCRHighlight | null>(null);

  // Fetch document chunks
  const {
    data: chunksData,
    isLoading: chunksLoading,
    error: chunksError
  } = useQuery({
    queryKey: ['document-chunks', documentId],
    queryFn: () => documentService.getDocumentChunks(documentId),
    enabled: !!documentId && showOcrOverlay,
  });

  // Extract chunks array from the response
  const chunks = chunksData?.chunks || [];

  // Convert chunks to highlights format
  const highlights = useMemo(() => {
    if (!chunks || !showOcrOverlay || !chunksVisible) {
      // Add a test highlight to see if the system works
      const testHighlight: OCRHighlight = {
        id: 'test-highlight',
        pageIndex: 0,
        boundingBox: {
          left: 0.1,   // 10% from left
          top: 0.1,    // 10% from top
          width: 0.3,  // 30% width
          height: 0.1, // 10% height
        },
        text: 'Test highlight to verify the system works',
        layoutType: 'TEST',
        confidence: 0.9,
        chunkId: 'test-chunk',
      };
      return [testHighlight];
    }

    const convertedHighlights = convertChunksToHighlights(chunks);
    console.log('🟢 Converted highlights:', convertedHighlights.length, convertedHighlights.slice(0, 2));
    return convertedHighlights;
  }, [chunks, showOcrOverlay, chunksVisible]);

  // Filter highlights based on current filters
  const filteredHighlights = useMemo(() => {
    return highlights.filter(highlight => {
      // Filter by chunk type
      if (!chunkFilters.chunkTypes.has(highlight.layoutType.toLowerCase())) {
        return false;
      }

      // Filter by search text
      if (chunkFilters.searchText && 
          !highlight.text.toLowerCase().includes(chunkFilters.searchText.toLowerCase())) {
        return false;
      }

      // Filter by confidence threshold
      if (highlight.confidence < chunkFilters.similarityThreshold) {
        return false;
      }

      return true;
    });
  }, [highlights, chunkFilters]);

  const handleHighlightClick = useCallback((highlight: OCRHighlight) => {
    console.log('Highlight clicked:', highlight);
    
    // Toggle selection
    const chunkId = highlight.chunkId || highlight.id;
    const newSelection = selectedChunks.includes(chunkId)
      ? selectedChunks.filter(id => id !== chunkId)
      : [...selectedChunks, chunkId];
    
    setSelectedChunks(newSelection);
    onChunkSelect?.(newSelection);
  }, [selectedChunks, onChunkSelect]);

  const handleHighlightHover = useCallback((highlight: OCRHighlight | null) => {
    setHoveredHighlight(highlight);
  }, []);

  const toggleChunksVisibility = useCallback(() => {
    setChunksVisible(!chunksVisible);
  }, [chunksVisible]);

  // Get the PDF URL
  const pdfUrl = documentUrl || documentService.getDocumentUrl(documentId);

  if (chunksError) {
    return (
      <div className={`flex items-center justify-center h-96 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300 ${className}`}>
        <div className="text-center">
          <AlertTriangle className="w-12 h-12 mx-auto mb-4 text-red-500" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Failed to Load Document</h3>
          <p className="text-sm text-gray-600">
            {chunksError instanceof Error ? chunksError.message : 'An unexpected error occurred'}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={`working-pdf-viewer flex flex-col h-full ${className}`}>
      {/* Controls */}
      {showControls && (
        <div className="flex items-center justify-between p-3 bg-gray-50 border-b border-gray-200">
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={toggleChunksVisibility}
              className="h-8"
            >
              {chunksVisible ? <EyeOff className="w-4 h-4 mr-1" /> : <Eye className="w-4 h-4 mr-1" />}
              {chunksVisible ? 'Hide' : 'Show'} Highlights
            </Button>
            
            {chunksVisible && (
              <div className="text-sm text-gray-600">
                {filteredHighlights.length} of {highlights.length} highlights
              </div>
            )}
          </div>

          <div className="flex items-center space-x-2">
            {chunksLoading && (
              <div className="flex items-center text-sm text-gray-600">
                <Loader2 className="w-4 h-4 mr-1 animate-spin" />
                Loading highlights...
              </div>
            )}
          </div>
        </div>
      )}

      {/* PDF Viewer */}
      <div className="flex-1 overflow-hidden">
        <EnhancedPDFViewer
          url={pdfUrl}
          highlights={filteredHighlights}
          onHighlightClick={handleHighlightClick}
          onHighlightHover={handleHighlightHover}
          enableInteractions={true}
          options={{
            selectedHighlightChunkId: selectedChunks.length === 1 ? Number.parseInt(selectedChunks[0]) : null,
          }}
        />
      </div>

      {/* Hover tooltip */}
      {hoveredHighlight && (
        <div className="absolute top-4 right-4 bg-white border border-gray-200 rounded-lg shadow-lg p-3 max-w-sm z-50">
          <div className="text-sm font-medium text-gray-900 mb-1">
            {hoveredHighlight.layoutType}
          </div>
          <div className="text-xs text-gray-600 mb-2">
            Confidence: {Math.round(hoveredHighlight.confidence * 100)}%
          </div>
          <div className="text-sm text-gray-700 max-h-32 overflow-y-auto">
            {hoveredHighlight.text.substring(0, 200)}
            {hoveredHighlight.text.length > 200 && '...'}
          </div>
        </div>
      )}
    </div>
  );
}
