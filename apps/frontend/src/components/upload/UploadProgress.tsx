import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>hart3, FileText, AlertTriangle, ArrowRight, RotateCcw, Eye } from 'lucide-react'
import { useState, useEffect, useCallback } from 'react'
import { useAnalysisStatus, useStartAnalysis } from '../../hooks'
import { useNavigate } from '@tanstack/react-router'
import type { Tender } from '../../types'

interface UploadProgressProps {
  tenderId: string
  onComplete?: () => void
  onStartOver: () => void
  tenderData?: Tender
}

export function UploadProgress({ tenderId, onStartOver, tenderData }: UploadProgressProps) {
  const [currentStep, setCurrentStep] = useState<'uploading' | 'processing' | 'analyzing' | 'completed'>('uploading')
  const [progress, setProgress] = useState(0)
  
  const { data: analysisStatus } = useAnalysisStatus(tenderId)
  const startAnalysisMutation = useStartAnalysis()
  const navigate = useNavigate()

  // Simulate upload progress
  useEffect(() => {
    const timer = setTimeout(() => {
      setCurrentStep('processing')
      setProgress(25)
    }, 1000)

    return () => clearTimeout(timer)
  }, [])

  // Update progress based on analysis status
  useEffect(() => {
    if (analysisStatus) {
      if (analysisStatus.status === 'analyzing') {
        setCurrentStep('analyzing')
        setProgress(75) // Fixed progress for analyzing step
      } else if (analysisStatus.status === 'completed') {
        setCurrentStep('completed')
        setProgress(100)
      }
    }
  }, [analysisStatus])

  // Auto-advance processing step
  useEffect(() => {
    if (currentStep === 'processing') {
      const timer = setTimeout(() => {
        setCurrentStep('analyzing')
        setProgress(50)
        
        // Auto-start analysis
        if (analysisStatus?.status === 'pending') {
          startAnalysisMutation.mutate(tenderId)
        }
      }, 2000)

      return () => clearTimeout(timer)
    }
  }, [currentStep, analysisStatus, startAnalysisMutation, tenderId])

  const handleViewResults = useCallback(() => {
    navigate({ to: '/whiteboard' })
  }, [navigate])

  const handleViewBids = useCallback(() => {
    navigate({ to: '/bids' })
  }, [navigate])

  const getStepStatus = (step: string) => {
    const stepOrder = ['uploading', 'processing', 'analyzing', 'completed']
    const currentIndex = stepOrder.indexOf(currentStep)
    const stepIndex = stepOrder.indexOf(step)
    
    if (stepIndex < currentIndex) return 'completed'
    if (stepIndex === currentIndex) return 'active'
    return 'pending'
  }

  const getStepIcon = (step: string, status: string) => {
    if (status === 'completed') return <CheckCircle className="w-5 h-5 text-green-600" />
    if (status === 'active') {
      switch (step) {
        case 'uploading': return <FileText className="w-5 h-5 text-blue-600" />
        case 'processing': return <Clock className="w-5 h-5 text-blue-600" />
        case 'analyzing': return <BarChart3 className="w-5 h-5 text-blue-600" />
        case 'completed': return <CheckCircle className="w-5 h-5 text-green-600" />
      }
    }
    return <Clock className="w-5 h-5 text-slate-400" />
  }


  return (
    <div className="max-w-4xl mx-auto">
      <div className="bg-white rounded-lg border border-slate-200">
        {/* Header */}
        <div className="border-b border-slate-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold text-slate-900">Analysis Progress</h3>
              {tenderData && (
                <p className="text-slate-600 mt-1">
                  Processing bid documents for: <span className="font-medium">{tenderData.title}</span>
                </p>
              )}
            </div>
            
            {currentStep === 'completed' && (
              <div className="flex items-center space-x-3">
                <button
                  type="button"
                  onClick={handleViewBids}
                  className="flex items-center space-x-2 px-4 py-2 border border-slate-300 text-slate-700 rounded-lg hover:bg-slate-50 transition-colors"
                >
                  <FileText className="w-4 h-4" />
                  <span>View Bids</span>
                </button>
                <button
                  type="button"
                  onClick={handleViewResults}
                  className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  <Eye className="w-4 h-4" />
                  <span>View Analysis</span>
                </button>
              </div>
            )}
          </div>
        </div>

        <div className="p-6 space-y-8">
          {/* Progress Steps */}
          <div className="relative">
            <div className="flex items-center justify-between">
              {['uploading', 'processing', 'analyzing', 'completed'].map((step, index) => {
                const status = getStepStatus(step)
                const stepNames = {
                  uploading: 'Upload Files',
                  processing: 'Process Documents',
                  analyzing: 'Analyze Content',
                  completed: 'Analysis Complete'
                }
                
                return (
                  <div key={step} className="flex flex-col items-center relative">
                    {/* Step Icon */}
                    <div className={`
                      w-12 h-12 rounded-full flex items-center justify-center border-2 transition-all
                      ${status === 'completed' ? 'bg-green-50 border-green-200' : 
                        status === 'active' ? 'bg-blue-50 border-blue-200' : 
                        'bg-slate-50 border-slate-200'}
                    `}>
                      {getStepIcon(step, status)}
                    </div>
                    
                    {/* Step Label */}
                    <div className="mt-2 text-center">
                      <div className={`text-sm font-medium ${
                        status === 'completed' ? 'text-green-700' :
                        status === 'active' ? 'text-blue-700' :
                        'text-slate-500'
                      }`}>
                        {stepNames[step as keyof typeof stepNames]}
                      </div>
                      {status === 'active' && (
                        <div className="text-xs text-slate-600 mt-1">
                          {step === 'uploading' && 'Uploading documents...'}
                          {step === 'processing' && 'Extracting text content...'}
                          {step === 'analyzing' && 'Running similarity analysis...'}
                          {step === 'completed' && 'Ready for review'}
                        </div>
                      )}
                    </div>
                    
                    {/* Connector Line */}
                    {index < 3 && (
                      <div className={`
                        absolute top-6 left-12 w-full h-0.5 transition-all
                        ${status === 'completed' ? 'bg-green-300' : 'bg-slate-200'}
                      `} style={{ width: 'calc(100% + 3rem)' }} />
                    )}
                  </div>
                )
              })}
            </div>
          </div>

          {/* Progress Bar */}
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span className="text-slate-600">Overall Progress</span>
              <span className="font-medium text-slate-900">{Math.round(progress)}%</span>
            </div>
            <div className="w-full bg-slate-200 rounded-full h-2">
              <div 
                className="bg-blue-600 h-2 rounded-full transition-all duration-500 ease-out"
                style={{ width: `${progress}%` }}
              />
            </div>
          </div>

          {/* Status Information */}
          {analysisStatus && (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-slate-50 rounded-lg p-4">
                <div className="text-sm text-slate-600 mb-1">Documents Uploaded</div>
                <div className="text-2xl font-bold text-slate-900">{analysisStatus.bid_count}</div>
              </div>
              
              <div className="bg-slate-50 rounded-lg p-4">
                <div className="text-sm text-slate-600 mb-1">Analysis Results</div>
                <div className="text-2xl font-bold text-slate-900">{analysisStatus.analysis_count}</div>
              </div>
              
              <div className="bg-slate-50 rounded-lg p-4">
                <div className="text-sm text-slate-600 mb-1">Status</div>
                <div className={`text-sm font-medium capitalize ${
                  analysisStatus.status === 'completed' ? 'text-green-600' :
                  analysisStatus.status === 'analyzing' ? 'text-blue-600' :
                  analysisStatus.status === 'pending' ? 'text-orange-600' :
                  'text-slate-600'
                }`}>
                  {analysisStatus.status}
                </div>
              </div>
            </div>
          )}

          {/* Current Step Details */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-start space-x-3">
              <div className="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0">
                {currentStep === 'completed' ? <CheckCircle /> : <Clock />}
              </div>
              <div className="text-sm text-blue-800">
                {currentStep === 'uploading' && (
                  <>
                    <h4 className="font-medium mb-1">Uploading Documents</h4>
                    <p>Your bid documents are being uploaded to our secure servers...</p>
                  </>
                )}
                {currentStep === 'processing' && (
                  <>
                    <h4 className="font-medium mb-1">Processing Documents</h4>
                    <p>Extracting text content and metadata from uploaded files using OCR technology...</p>
                  </>
                )}
                {currentStep === 'analyzing' && (
                  <>
                    <h4 className="font-medium mb-1">Analyzing Content</h4>
                    <p>Running multi-dimensional similarity analysis including semantic, structural, numerical, and stylometric comparisons...</p>
                  </>
                )}
                {currentStep === 'completed' && (
                  <>
                    <h4 className="font-medium mb-1">Analysis Complete!</h4>
                    <p>Your bid analysis is ready. View the results in the detective whiteboard to explore suspicious similarities and patterns.</p>
                  </>
                )}
              </div>
            </div>
          </div>

          {/* Error Handling */}
          {startAnalysisMutation.error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-start space-x-3">
                <AlertTriangle className="w-5 h-5 text-red-600 mt-0.5 flex-shrink-0" />
                <div className="text-sm text-red-800">
                  <h4 className="font-medium mb-1">Analysis Error</h4>
                  <p>Failed to start analysis: {startAnalysisMutation.error.message}</p>
                </div>
              </div>
            </div>
          )}

          {/* Action Buttons */}
          {currentStep !== 'completed' && (
            <div className="flex justify-center">
              <button
                type="button"
                onClick={onStartOver}
                className="flex items-center space-x-2 px-4 py-2 text-slate-600 hover:text-slate-900 hover:bg-slate-100 rounded-lg transition-colors"
              >
                <RotateCcw className="w-4 h-4" />
                <span>Start Over</span>
              </button>
            </div>
          )}

          {currentStep === 'completed' && (
            <div className="flex justify-center space-x-4">
              <button
                type="button"
                onClick={onStartOver}
                className="flex items-center space-x-2 px-4 py-2 border border-slate-300 text-slate-700 rounded-lg hover:bg-slate-50 transition-colors"
              >
                <RotateCcw className="w-4 h-4" />
                <span>Upload More Bids</span>
              </button>
              <button
                type="button"
                onClick={handleViewResults}
                className="flex items-center space-x-2 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                <span>View Analysis Results</span>
                <ArrowRight className="w-4 h-4" />
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}