import { Upload, FileText, AlertCircle, ChevronLeft, ChevronRight, Building2, DollarSign, X, CheckCircle } from 'lucide-react'
import { useState, useCallback } from 'react'
import { documentService } from '../../services/documentService'
import type { Tender } from '../../types'

interface BidUploadFormProps {
  tenderId: string
  onBidsUploaded: () => void
  onBack: () => void
  tenderData?: Tender
}

interface UploadedFileData {
  file: File
  company: string
  price: string
  id: string
}

export function BidUploadForm({ tenderId, onBidsUploaded, onBack, tenderData }: BidUploadFormProps) {
  const [isDragOver, setIsDragOver] = useState(false)
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFileData[]>([])
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [isUploading, setIsUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState<{ current: number; total: number }>({ current: 0, total: 0 })

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(true)
  }, [])

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)
  }, [])

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)
    
    const files = Array.from(e.dataTransfer.files)
    const validFiles = files.filter(file => 
      file.type === 'application/pdf' || 
      file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    )
    
    const newFileData: UploadedFileData[] = validFiles.map(file => ({
      file,
      company: '',
      price: '',
      id: `${file.name}-${Date.now()}-${Math.random()}`
    }))
    
    setUploadedFiles(prev => [...prev, ...newFileData])
  }, [])

  const handleFileSelect = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || [])
    const newFileData: UploadedFileData[] = files.map(file => ({
      file,
      company: '',
      price: '',
      id: `${file.name}-${Date.now()}-${Math.random()}`
    }))
    
    setUploadedFiles(prev => [...prev, ...newFileData])
  }, [])

  const removeFile = useCallback((id: string) => {
    setUploadedFiles(prev => prev.filter(f => f.id !== id))
    setErrors(prev => {
      const newErrors = { ...prev }
      delete newErrors[`${id}-company`]
      delete newErrors[`${id}-price`]
      return newErrors
    })
  }, [])

  const updateFileData = useCallback((id: string, field: 'company' | 'price', value: string) => {
    setUploadedFiles(prev => 
      prev.map(f => f.id === id ? { ...f, [field]: value } : f)
    )
    
    // Clear error when user starts typing
    const errorKey = `${id}-${field}`
    if (errors[errorKey]) {
      setErrors(prev => {
        const newErrors = { ...prev }
        delete newErrors[errorKey]
        return newErrors
      })
    }
  }, [errors])

  const validateFiles = useCallback(() => {
    const newErrors: Record<string, string> = {}
    
    for (const fileData of uploadedFiles) {
      if (!fileData.company.trim()) {
        newErrors[`${fileData.id}-company`] = 'Company name is required'
      }
      if (!fileData.price.trim()) {
        newErrors[`${fileData.id}-price`] = 'Bid price is required'
      } else if (Number.parseFloat(fileData.price) <= 0) {
        newErrors[`${fileData.id}-price`] = 'Price must be greater than 0'
      }
    }
    
    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }, [uploadedFiles])

  const handleUpload = useCallback(async () => {
    if (uploadedFiles.length === 0) return
    
    // Check minimum bid requirement
    if (uploadedFiles.length < 2) {
      setErrors({ 'minimum-bids': 'At least 2 bid documents are required for analysis' })
      return
    }
    
    if (!validateFiles()) return
    
    setIsUploading(true)
    setUploadProgress({ current: 0, total: uploadedFiles.length })
    
    // Clear any previous upload errors
    if (errors['upload-error']) {
      const newErrors = { ...errors }
      newErrors['upload-error'] = undefined
      setErrors(newErrors)
    }
    
    try {
      const uploadResults = []
      
      // Upload each document individually using the new API
      for (let i = 0; i < uploadedFiles.length; i++) {
        const fileData = uploadedFiles[i]
        setUploadProgress({ current: i + 1, total: uploadedFiles.length })
        
        const result = await documentService.uploadDocument(
          tenderId, // Using tenderId as project_id for now
          fileData.company.trim(),
          fileData.file,
          true // process immediately
        )
        
        uploadResults.push(result)
      }
      
      console.log('All documents uploaded successfully:', uploadResults)
      onBidsUploaded()
    } catch (error) {
      console.error('Upload failed:', error)
      let errorMessage = 'Upload failed: Unknown error'
      
      if (error.message) {
        errorMessage = `Upload failed: ${error.message}`
      }
      
      // More specific error messages
      if (error.message?.includes('Internal Server Error')) {
        errorMessage = 'Upload failed: Server error. Please check that the backend services are running correctly.'
      } else if (error.message?.includes('Network Error')) {
        errorMessage = 'Upload failed: Network error. Please check your connection.'
      } else if (error.message?.includes('404')) {
        errorMessage = 'Upload failed: Project not found. Please try creating a new tender first.'
      }
      
      setErrors({ 'upload-error': errorMessage })
    } finally {
      setIsUploading(false)
      setUploadProgress({ current: 0, total: 0 })
    }
  }, [tenderId, uploadedFiles, validateFiles, onBidsUploaded, errors])

  const formatPrice = (price: number) => {
    return `$${(price / 10000).toLocaleString()}0K`
  }

  const totalEstimatedValue = uploadedFiles.reduce((sum, f) => {
    const price = Number.parseFloat(f.price) || 0
    return sum + price
  }, 0)

  return (
    <div className="max-w-4xl mx-auto">
      <div className="bg-white rounded-lg border border-slate-200">
        {/* Header */}
        <div className="border-b border-slate-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold text-slate-900">Upload Bid Documents</h3>
              {tenderData && (
                <div className="flex items-center space-x-4 mt-2 text-sm text-slate-600">
                  <span className="font-medium">{tenderData.title}</span>
                  <span>•</span>
                  <div className="flex items-center space-x-1">
                    <Building2 className="w-3 h-3" />
                    <span>{tenderData.department}</span>
                  </div>
                  <span>•</span>
                  <div className="flex items-center space-x-1">
                    <DollarSign className="w-3 h-3" />
                    <span>Budget: {formatPrice(tenderData.budget)}</span>
                  </div>
                </div>
              )}
            </div>
            <button
              type="button"
              onClick={onBack}
              className="flex items-center space-x-2 px-3 py-2 text-slate-600 hover:text-slate-900 hover:bg-slate-100 rounded-lg transition-colors"
            >
              <ChevronLeft className="w-4 h-4" />
              <span>Back</span>
            </button>
          </div>
        </div>

        <div className="p-6 space-y-6">
          {/* Upload Area */}
          <div
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
            className={`
              border-2 border-dashed rounded-lg p-8 text-center transition-colors
              ${isDragOver 
                ? 'border-blue-400 bg-blue-50' 
                : 'border-slate-300 hover:border-slate-400'
              }
            `}
          >
            <Upload className={`w-12 h-12 mx-auto mb-4 ${isDragOver ? 'text-blue-500' : 'text-slate-400'}`} />
            
            <div className="space-y-2">
              <h4 className="text-lg font-medium text-slate-900">
                Drag files here or click to upload
              </h4>
              <p className="text-slate-600">
                Supports PDF and Word documents, maximum 50MB per file
              </p>
            </div>
            
            <input
              type="file"
              multiple
              accept=".pdf,.docx,.doc"
              onChange={handleFileSelect}
              className="hidden"
              id="file-upload"
            />
            
            <label
              htmlFor="file-upload"
              className="mt-4 inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors cursor-pointer"
            >
              Choose Files
            </label>
          </div>

          {/* Instructions */}
          <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-start space-x-3">
              <AlertCircle className="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0" />
              <div className="text-sm text-blue-800">
                <h4 className="font-medium mb-1">Upload Instructions</h4>
                <ul className="space-y-1 text-blue-700">
                  <li>• <strong>Minimum 2 bid documents required</strong> for comparative analysis</li>
                  <li>• Upload all bid documents for this tender at once</li>
                  <li>• Enter company name and bid price for each document</li>
                  <li>• System will automatically perform multi-dimensional similarity analysis</li>
                  <li>• Results can be viewed in the detective whiteboard after analysis</li>
                </ul>
              </div>
            </div>
          </div>

          {/* Uploaded Files List */}
          {uploadedFiles.length > 0 && (
            <div>
              <div className="flex items-center justify-between mb-4">
                <h4 className="font-medium text-slate-900">
                  Selected Files ({uploadedFiles.length})
                  {uploadedFiles.length < 2 && (
                    <span className="ml-2 text-sm text-amber-600 font-normal">
                      (Need {2 - uploadedFiles.length} more for analysis)
                    </span>
                  )}
                </h4>
                {totalEstimatedValue > 0 && (
                  <div className="text-sm text-slate-600">
                    Total Estimated Value: <span className="font-medium">{formatPrice(totalEstimatedValue)}</span>
                  </div>
                )}
              </div>
              
              {/* Minimum files warning */}
              {uploadedFiles.length === 1 && (
                <div className="mb-4 p-3 bg-amber-50 border border-amber-200 rounded-lg">
                  <div className="flex items-center space-x-2">
                    <AlertCircle className="w-4 h-4 text-amber-600 flex-shrink-0" />
                    <p className="text-sm text-amber-800">
                      <strong>1 more bid document needed</strong> - Analysis requires at least 2 bids for comparison.
                    </p>
                  </div>
                </div>
              )}
              
              <div className="space-y-4">
                {uploadedFiles.map((fileData) => (
                  <div key={fileData.id} className="p-4 bg-slate-50 rounded-lg border border-slate-200">
                    <div className="flex items-start space-x-4">
                      <FileText className="w-5 h-5 text-slate-500 mt-1 flex-shrink-0" />
                      <div className="flex-1 space-y-3">
                        <div className="flex items-center justify-between">
                          <div>
                            <div className="font-medium text-slate-900">{fileData.file.name}</div>
                            <div className="text-sm text-slate-600">
                              {(fileData.file.size / 1024 / 1024).toFixed(2)} MB
                            </div>
                          </div>
                          <button
                            type="button"
                            onClick={() => removeFile(fileData.id)}
                            className="p-1 text-slate-400 hover:text-red-600 transition-colors"
                            title="Remove file"
                          >
                            <X className="w-4 h-4" />
                          </button>
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                          <div>
                            <label htmlFor={`company-${fileData.id}`} className="block text-sm font-medium text-slate-700 mb-1">
                              Company Name *
                            </label>
                            <input
                              id={`company-${fileData.id}`}
                              type="text"
                              value={fileData.company}
                              onChange={(e) => updateFileData(fileData.id, 'company', e.target.value)}
                              placeholder="Enter company name"
                              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                                errors[`${fileData.id}-company`] ? 'border-red-300' : 'border-slate-300'
                              }`}
                            />
                            {errors[`${fileData.id}-company`] && (
                              <p className="mt-1 text-sm text-red-600">{errors[`${fileData.id}-company`]}</p>
                            )}
                          </div>
                          <div>
                            <label htmlFor={`price-${fileData.id}`} className="block text-sm font-medium text-slate-700 mb-1">
                              Bid Price (MOP) *
                            </label>
                            <input
                              id={`price-${fileData.id}`}
                              type="number"
                              value={fileData.price}
                              onChange={(e) => updateFileData(fileData.id, 'price', e.target.value)}
                              placeholder="Enter bid amount"
                              min="0"
                              step="1000"
                              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                                errors[`${fileData.id}-price`] ? 'border-red-300' : 'border-slate-300'
                              }`}
                            />
                            {errors[`${fileData.id}-price`] && (
                              <p className="mt-1 text-sm text-red-600">{errors[`${fileData.id}-price`]}</p>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* Upload Summary */}
              {uploadedFiles.length > 0 && (
                <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <CheckCircle className="w-5 h-5 text-green-600 flex-shrink-0" />
                    <div className="text-sm text-green-800">
                      <p className="font-medium">Ready for Analysis</p>
                      <p>
                        {uploadedFiles.length} bid document{uploadedFiles.length !== 1 ? 's' : ''} prepared for upload and analysis.
                        {tenderData && totalEstimatedValue > 0 && (
                          <span className="ml-1">
                            Average bid: {formatPrice(totalEstimatedValue / uploadedFiles.length)}
                            {totalEstimatedValue > tenderData.budget && (
                              <span className="text-green-700 font-medium"> (Above budget)</span>
                            )}
                          </span>
                        )}
                      </p>
                    </div>
                  </div>
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex justify-end pt-4 border-t border-slate-200">
                <button
                  type="button"
                  onClick={handleUpload}
                  disabled={
                    uploadedFiles.length < 2 || 
                    isUploading ||
                    Object.keys(errors).filter(key => key !== 'minimum-bids' && key !== 'upload-error').length > 0
                  }
                  className="flex items-center space-x-2 px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:bg-slate-400 disabled:cursor-not-allowed transition-colors"
                >
                  <Upload className="w-4 h-4" />
                  <span>
                    {isUploading
                      ? `Uploading ${uploadProgress.current}/${uploadProgress.total}...`
                      : uploadedFiles.length < 2
                      ? `Need ${2 - uploadedFiles.length} more files`
                      : `Upload & Start Analysis (${uploadedFiles.length} files)`
                    }
                  </span>
                  <ChevronRight className="w-4 h-4" />
                </button>
              </div>
              
              {/* Error Messages */}
              {errors['minimum-bids'] && (
                <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                  <p className="text-sm text-red-600">
                    {errors['minimum-bids']}
                  </p>
                </div>
              )}
              
              {errors['upload-error'] && (
                <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                  <p className="text-sm text-red-600">
                    {errors['upload-error']}
                  </p>
                </div>
              )}
            </div>
          )}

          {uploadedFiles.length === 0 && (
            <div className="text-center py-8 text-slate-500">
              <FileText className="w-12 h-12 mx-auto mb-3 text-slate-300" />
              <p>No files selected yet. Drag files above or click "Choose Files" to get started.</p>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}