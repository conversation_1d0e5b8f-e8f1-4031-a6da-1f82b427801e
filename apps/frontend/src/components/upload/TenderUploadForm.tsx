import { useState, useCallback } from 'react'
import { Building2, DollarSign, Calendar, FileText, Plus, ChevronRight, Search } from 'lucide-react'
import { useCreateTender } from '../../hooks'
import type { Tender } from '../../types'

interface TenderUploadFormProps {
  onTenderCreated: (tenderId: string) => void
  onExistingTenderSelected: (tenderId: string) => void
  existingTenders: Tender[]
}

interface TenderFormData {
  title: string
  department: string
  budget: string
  publishDate: string
  description: string
  evaluationCriteria: string
  submissionDeadline: string
}

export function TenderUploadForm({ 
  onTenderCreated, 
  onExistingTenderSelected, 
  existingTenders 
}: TenderUploadFormProps) {
  const [activeTab, setActiveTab] = useState<'new' | 'existing'>('new')
  const [searchTender, setSearchTender] = useState('')
  const [formData, setFormData] = useState<TenderFormData>({
    title: '',
    department: '',
    budget: '',
    publishDate: new Date().toISOString().split('T')[0],
    description: '',
    evaluationCriteria: '',
    submissionDeadline: ''
  })
  const [errors, setErrors] = useState<Partial<TenderFormData>>({})

  const createTenderMutation = useCreateTender()

  const handleInputChange = useCallback((field: keyof TenderFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }))
    }
  }, [errors])

  const validateForm = useCallback((): boolean => {
    const newErrors: Partial<TenderFormData> = {}
    
    if (!formData.title.trim()) newErrors.title = 'Title is required'
    if (!formData.department.trim()) newErrors.department = 'Department is required'
    if (!formData.budget.trim()) newErrors.budget = 'Budget is required'
    if (Number.parseFloat(formData.budget) <= 0) newErrors.budget = 'Budget must be greater than 0'
    if (!formData.publishDate) newErrors.publishDate = 'Publish date is required'

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }, [formData])

  const handleSubmit = useCallback(async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) return

    try {
      const tenderData = {
        title: formData.title.trim(),
        department: formData.department.trim(),
        budget: Number.parseFloat(formData.budget),
        publish_date: formData.publishDate,
        description: formData.description.trim() || undefined,
        evaluation_criteria: formData.evaluationCriteria.trim() || undefined,
        submission_deadline: formData.submissionDeadline || undefined
      }

      const result = await createTenderMutation.mutateAsync(tenderData)
      onTenderCreated(result.id)
    } catch (error) {
      console.error('Failed to create tender:', error)
    }
  }, [formData, validateForm, createTenderMutation, onTenderCreated])

  const filteredTenders = existingTenders.filter(tender =>
    tender.title.toLowerCase().includes(searchTender.toLowerCase()) ||
    tender.department.toLowerCase().includes(searchTender.toLowerCase())
  )

  const formatPrice = (price: number) => {
    return `$${(price / 10000).toLocaleString()}0K`
  }

  return (
    <div className="max-w-4xl mx-auto">
      {/* Tab Navigation */}
      <div className="bg-white rounded-lg border border-slate-200 overflow-hidden">
        <div className="border-b border-slate-200">
          <div className="flex">
            <button
              type="button"
              onClick={() => setActiveTab('new')}
              className={`flex-1 px-6 py-4 text-sm font-medium transition-colors ${
                activeTab === 'new'
                  ? 'bg-blue-50 text-blue-700 border-b-2 border-blue-600'
                  : 'text-slate-600 hover:text-slate-900 hover:bg-slate-50'
              }`}
            >
              <div className="flex items-center justify-center space-x-2">
                <Plus className="w-4 h-4" />
                <span>Create New Tender</span>
              </div>
            </button>
            <button
              type="button"
              onClick={() => setActiveTab('existing')}
              className={`flex-1 px-6 py-4 text-sm font-medium transition-colors ${
                activeTab === 'existing'
                  ? 'bg-blue-50 text-blue-700 border-b-2 border-blue-600'
                  : 'text-slate-600 hover:text-slate-900 hover:bg-slate-50'
              }`}
            >
              <div className="flex items-center justify-center space-x-2">
                <Search className="w-4 h-4" />
                <span>Use Existing Tender</span>
              </div>
            </button>
          </div>
        </div>

        <div className="p-6">
          {activeTab === 'new' ? (
            <form onSubmit={handleSubmit} className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold text-slate-900 mb-4">
                  Create New Tender
                </h3>
                <p className="text-slate-600 mb-6">
                  Fill in the tender information below to create a new tender for bid analysis.
                </p>
              </div>

              {/* Basic Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="md:col-span-2">
                  <label htmlFor="title" className="block text-sm font-medium text-slate-700 mb-2">
                    Tender Title *
                  </label>
                  <input
                    id="title"
                    type="text"
                    value={formData.title}
                    onChange={(e) => handleInputChange('title', e.target.value)}
                    placeholder="Enter tender title"
                    className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                      errors.title ? 'border-red-300' : 'border-slate-300'
                    }`}
                  />
                  {errors.title && (
                    <p className="mt-1 text-sm text-red-600">{errors.title}</p>
                  )}
                </div>

                <div>
                  <label htmlFor="department" className="block text-sm font-medium text-slate-700 mb-2">
                    <Building2 className="w-4 h-4 inline mr-1" />
                    Department *
                  </label>
                  <input
                    id="department"
                    type="text"
                    value={formData.department}
                    onChange={(e) => handleInputChange('department', e.target.value)}
                    placeholder="Enter issuing department"
                    className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                      errors.department ? 'border-red-300' : 'border-slate-300'
                    }`}
                  />
                  {errors.department && (
                    <p className="mt-1 text-sm text-red-600">{errors.department}</p>
                  )}
                </div>

                <div>
                  <label htmlFor="budget" className="block text-sm font-medium text-slate-700 mb-2">
                    <DollarSign className="w-4 h-4 inline mr-1" />
                    Budget (MOP) *
                  </label>
                  <input
                    id="budget"
                    type="number"
                    value={formData.budget}
                    onChange={(e) => handleInputChange('budget', e.target.value)}
                    placeholder="Enter budget amount"
                    min="0"
                    step="1000"
                    className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                      errors.budget ? 'border-red-300' : 'border-slate-300'
                    }`}
                  />
                  {errors.budget && (
                    <p className="mt-1 text-sm text-red-600">{errors.budget}</p>
                  )}
                </div>

                <div>
                  <label htmlFor="publishDate" className="block text-sm font-medium text-slate-700 mb-2">
                    <Calendar className="w-4 h-4 inline mr-1" />
                    Publish Date *
                  </label>
                  <input
                    id="publishDate"
                    type="date"
                    value={formData.publishDate}
                    onChange={(e) => handleInputChange('publishDate', e.target.value)}
                    className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                      errors.publishDate ? 'border-red-300' : 'border-slate-300'
                    }`}
                  />
                  {errors.publishDate && (
                    <p className="mt-1 text-sm text-red-600">{errors.publishDate}</p>
                  )}
                </div>

                <div>
                  <label htmlFor="submissionDeadline" className="block text-sm font-medium text-slate-700 mb-2">
                    <Calendar className="w-4 h-4 inline mr-1" />
                    Submission Deadline
                  </label>
                  <input
                    id="submissionDeadline"
                    type="date"
                    value={formData.submissionDeadline}
                    onChange={(e) => handleInputChange('submissionDeadline', e.target.value)}
                    className="w-full px-3 py-2 border border-slate-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>

              {/* Optional Information */}
              <div className="space-y-4">
                <div>
                  <label htmlFor="description" className="block text-sm font-medium text-slate-700 mb-2">
                    <FileText className="w-4 h-4 inline mr-1" />
                    Description
                  </label>
                  <textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                    placeholder="Enter tender description (optional)"
                    rows={3}
                    className="w-full px-3 py-2 border border-slate-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>

                <div>
                  <label htmlFor="evaluationCriteria" className="block text-sm font-medium text-slate-700 mb-2">
                    Evaluation Criteria
                  </label>
                  <textarea
                    id="evaluationCriteria"
                    value={formData.evaluationCriteria}
                    onChange={(e) => handleInputChange('evaluationCriteria', e.target.value)}
                    placeholder="Enter evaluation criteria (optional)"
                    rows={3}
                    className="w-full px-3 py-2 border border-slate-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>

              {/* Submit Button */}
              <div className="flex justify-end pt-4 border-t border-slate-200">
                <button
                  type="submit"
                  disabled={createTenderMutation.isPending}
                  className="flex items-center space-x-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-slate-400 disabled:cursor-not-allowed transition-colors"
                >
                  <span>
                    {createTenderMutation.isPending ? 'Creating...' : 'Create Tender'}
                  </span>
                  <ChevronRight className="w-4 h-4" />
                </button>
              </div>

              {createTenderMutation.error && (
                <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                  <p className="text-sm text-red-600">
                    Failed to create tender: {createTenderMutation.error.message}
                  </p>
                </div>
              )}
            </form>
          ) : (
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold text-slate-900 mb-4">
                  Select Existing Tender
                </h3>
                <p className="text-slate-600 mb-6">
                  Choose an existing tender to add more bid documents for analysis.
                </p>
              </div>

              {/* Search */}
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-400" />
                <input
                  type="text"
                  value={searchTender}
                  onChange={(e) => setSearchTender(e.target.value)}
                  placeholder="Search tenders by title or department..."
                  className="w-full pl-10 pr-4 py-2 border border-slate-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              {/* Tender List */}
              <div className="space-y-3 max-h-96 overflow-y-auto">
                {filteredTenders.length === 0 ? (
                  <div className="text-center py-8 text-slate-500">
                    {searchTender ? 'No tenders found matching your search.' : 'No existing tenders available.'}
                  </div>
                ) : (
                  filteredTenders.map((tender) => (
                    <button
                      key={tender.id}
                      type="button"
                      onClick={() => onExistingTenderSelected(tender.id)}
                      className="w-full p-4 border border-slate-200 rounded-lg hover:bg-slate-50 hover:border-slate-300 transition-colors text-left group"
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <h4 className="font-medium text-slate-900 group-hover:text-blue-600 transition-colors">
                            {tender.title}
                          </h4>
                          <div className="flex items-center space-x-4 mt-2 text-sm text-slate-600">
                            <div className="flex items-center space-x-1">
                              <Building2 className="w-3 h-3" />
                              <span>{tender.department}</span>
                            </div>
                            <div className="flex items-center space-x-1">
                              <DollarSign className="w-3 h-3" />
                              <span>{formatPrice(tender.budget)}</span>
                            </div>
                            <div className="flex items-center space-x-1">
                              <FileText className="w-3 h-3" />
                              <span>{tender.bids.length} bids</span>
                            </div>
                          </div>
                        </div>
                        <ChevronRight className="w-5 h-5 text-slate-400 group-hover:text-blue-600 transition-colors" />
                      </div>
                    </button>
                  ))
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}