interface AlgorithmTabsProps {
  activeAlgorithm: string
  onAlgorithmChange: (algorithm: string) => void
  findingCounts: {
    document_layout_embedding: number
    layout_ocr_comparison: number
    llm_service_price_extraction: number
  }
}

interface AlgorithmConfig {
  id: string
  name: string
  shortName: string
  description: string
  icon: string
  color: string
  activeColor: string
}

const algorithms: AlgorithmConfig[] = [
  {
    id: 'document_layout_embedding',
    name: 'Document Layout + Vector Similarity',
    shortName: 'Layout Embedding',
    description: 'Chunks documents by layout structure (titles, paragraphs) and compares using vector embeddings',
    icon: '📋',
    color: 'bg-blue-100 text-blue-700 border-blue-300',
    activeColor: 'bg-blue-600 text-white border-blue-600'
  },
  {
    id: 'layout_ocr_comparison',
    name: 'Layout OCR Boundary Comparison',
    shortName: 'OCR Layout',
    description: 'Compares document element positions and sizing through boundary box analysis',
    icon: '📐',
    color: 'bg-orange-100 text-orange-700 border-orange-300',
    activeColor: 'bg-orange-600 text-white border-orange-600'
  },
  {
    id: 'llm_service_price_extraction',
    name: 'LLM Service & Price Analysis',
    shortName: 'Service Analysis',
    description: 'Uses LLM to extract services and pricing, detecting suspicious pricing patterns',
    icon: '🔍',
    color: 'bg-green-100 text-green-700 border-green-300',
    activeColor: 'bg-green-600 text-white border-green-600'
  }
]

export function AlgorithmTabs({ activeAlgorithm, onAlgorithmChange, findingCounts }: AlgorithmTabsProps) {
  const getCount = (algorithmId: string): number => {
    return findingCounts[algorithmId as keyof typeof findingCounts] || 0
  }

  return (
    <div className="space-y-4">
      {/* Main Algorithm Selector */}
      <div className="flex space-x-1 p-1 bg-slate-100 rounded-lg">
        {algorithms.map((algorithm) => {
          const isActive = activeAlgorithm === algorithm.id
          const count = getCount(algorithm.id)
          
          return (
            <button
              type="button"
              key={algorithm.id}
              onClick={() => onAlgorithmChange(algorithm.id)}
              className={`
                flex-1 relative px-4 py-3 rounded-lg font-medium text-sm transition-all duration-200
                ${isActive 
                  ? 'bg-white shadow-sm text-slate-900 border border-slate-200' 
                  : 'text-slate-600 hover:text-slate-800 hover:bg-slate-50'
                }
              `}
            >
              <div className="flex items-center justify-center space-x-2">
                <span className="text-lg">{algorithm.icon}</span>
                <span>{algorithm.shortName}</span>
                {count > 0 && (
                  <span className={`
                    px-2 py-1 rounded-full text-xs font-bold
                    ${isActive ? 'bg-blue-100 text-blue-800' : 'bg-slate-200 text-slate-700'}
                  `}>
                    {count}
                  </span>
                )}
              </div>
              
              {/* Active indicator */}
              {isActive && (
                <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-blue-500 rounded-full" />
              )}
            </button>
          )
        })}
      </div>

      {/* Active Algorithm Description */}
      <div className="bg-white border border-slate-200 rounded-lg p-4">
        {algorithms.map((algorithm) => {
          if (algorithm.id !== activeAlgorithm) return null
          
          const count = getCount(algorithm.id)
          
          return (
            <div key={algorithm.id} className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className={`p-2 rounded-lg border ${algorithm.color}`}>
                  <span className="text-lg">{algorithm.icon}</span>
                </div>
                <div>
                  <h3 className="font-semibold text-slate-900">{algorithm.name}</h3>
                  <p className="text-sm text-slate-600">{algorithm.description}</p>
                </div>
              </div>
              
              <div className="text-right">
                <div className="text-2xl font-bold text-slate-900">{count}</div>
                <div className="text-xs text-slate-600">
                  {count === 1 ? 'finding' : 'findings'}
                </div>
              </div>
            </div>
          )
        })}
      </div>
    </div>
  )
}