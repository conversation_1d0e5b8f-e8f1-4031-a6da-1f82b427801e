import { useState, useEffect, useMemo, useRef } from 'react'
import { Link } from '@tanstack/react-router'
import { ComparisonCard } from './ComparisonCard'
import { AlgorithmTabs } from './AlgorithmTabs'
import { DocumentViewer } from '../whiteboard/DocumentViewer'
import { useTenders, useTenderBids } from '../../hooks'
import { backupMockTender } from '../../data/mockData'

interface ComparisonDetailPageProps {
  document1Id: string
  document2Id: string
}

interface BoundingBox {
  x: number
  y: number
  width: number
  height: number
  page?: number
}

interface FindingData {
  id: string
  findingType: 'document_layout_embedding' | 'layout_ocr_comparison' | 'llm_service_price_extraction'
  riskLevel: 'low' | 'medium' | 'high' | 'critical'
  summary: string
  evidence: {
    chunkId1: string
    chunkId2: string
    score: number
    highlightDetails: {
      text1: string
      text2: string
    }
    boundingBoxes?: {
      doc1: BoundingBox[]
      doc2: BoundingBox[]
    }
    extractedServices?: {
      doc1: Array<{ service: string; price: number; suspicious: boolean }>
      doc2: Array<{ service: string; price: number; suspicious: boolean }>
    }
  }
}

export function ComparisonDetailPage({ document1Id, document2Id }: ComparisonDetailPageProps) {
  const [activeAlgorithm, setActiveAlgorithm] = useState<string>('document_layout_embedding')
  const [selectedFinding, setSelectedFinding] = useState<FindingData | null>(null)
  const [showDocumentViewer, setShowDocumentViewer] = useState(false)
  const [selectedForReport, setSelectedForReport] = useState<string[]>([])
  
  // Refs for scroll sections
  const sectionRefs = useRef<{ [key: string]: HTMLElement | null }>({})
  const scrollContainerRef = useRef<HTMLDivElement>(null)
  
  const { data: tenders } = useTenders()
  const tendersData = tenders || [backupMockTender]
  
  // State to hold the tender ID once we determine it
  const [tenderId, setTenderId] = useState<string>('tender-2025-001')
  
  // Effect to determine tender ID by making API calls for individual bids
  useEffect(() => {
    const findTenderId = async () => {
      try {
        // Try to fetch the first document to get its tender_id
        const response = await fetch(`http://localhost:8000/api/v1/bids/${document1Id}`)
        if (response.ok) {
          const bidData = await response.json()
          if (bidData.tender_id) {
            setTenderId(bidData.tender_id)
            return
          }
        }
        
        // If first document fails, try the second
        const response2 = await fetch(`http://localhost:8000/api/v1/bids/${document2Id}`)
        if (response2.ok) {
          const bidData2 = await response2.json()
          if (bidData2.tender_id) {
            setTenderId(bidData2.tender_id)
          }
        }
      } catch (error) {
        console.error('Error finding tender ID:', error)
        // Keep default tender ID
      }
    }
    
    findTenderId()
  }, [document1Id, document2Id])
  
  // Load all bids for the determined tender
  const { data: bids } = useTenderBids(tenderId)
  
  // Find the documents from the bid data
  const documentData = useMemo(() => {
    if (!bids) return null
    
    const doc1 = bids.find(bid => bid.id === document1Id)
    const doc2 = bids.find(bid => bid.id === document2Id)
    const tender = tendersData.find(t => t.id === tenderId) || backupMockTender
    
    if (doc1 && doc2) {
      return { doc1, doc2, tender }
    }
    return null
  }, [bids, tendersData, document1Id, document2Id, tenderId])

  // Mock findings data - in real implementation this would come from API
  const mockFindings: FindingData[] = [
    {
      id: 'finding-1',
      findingType: 'document_layout_embedding',
      riskLevel: 'high',
      summary: 'High vector similarity (0.92) in Construction Safety Plan section chunks. Document layout analysis reveals identical paragraph structures and technical specifications.',
      evidence: {
        chunkId1: 'chunk-1-doc1',
        chunkId2: 'chunk-1-doc2',
        score: 0.92,
        highlightDetails: {
          text1: 'All scaffold nodes must use M16 fixing screws with galvanized coating. Safety barriers shall be installed at 2-meter intervals...',
          text2: 'All scaffold nodes must use M16 fixing screws with galvanized coating. Safety barriers shall be installed at 2-meter intervals...'
        }
      }
    },
    {
      id: 'finding-2',
      findingType: 'document_layout_embedding',
      riskLevel: 'critical',
      summary: 'Vector embeddings show 96% similarity in project timeline chunks with identical milestone descriptions and delivery schedules.',
      evidence: {
        chunkId1: 'chunk-5-doc1',
        chunkId2: 'chunk-5-doc2',
        score: 0.96,
        highlightDetails: {
          text1: 'Phase 1: Site preparation (Weeks 1-2)\nPhase 2: Foundation work (Weeks 3-6)\nPhase 3: Structural assembly (Weeks 7-12)',
          text2: 'Phase 1: Site preparation (Weeks 1-2)\nPhase 2: Foundation work (Weeks 3-6)\nPhase 3: Structural assembly (Weeks 7-12)'
        }
      }
    },
    {
      id: 'finding-3',
      findingType: 'layout_ocr_comparison',
      riskLevel: 'critical',
      summary: 'OCR boundary box analysis shows 98% positional similarity. Page 5-8 elements have nearly identical coordinates, margins, and section headers.',
      evidence: {
        chunkId1: 'chunk-2-doc1',
        chunkId2: 'chunk-2-doc2',
        score: 0.98,
        highlightDetails: {
          text1: 'Technical Specifications\n\n1. Foundation Requirements\n2. Material Standards\n3. Quality Control',
          text2: 'Technical Specifications\n\n1. Foundation Requirements\n2. Material Standards\n3. Quality Control'
        },
        boundingBoxes: {
          doc1: [
            { x: 72, y: 200, width: 450, height: 24, page: 5 },
            { x: 90, y: 240, width: 180, height: 16, page: 5 },
            { x: 90, y: 260, width: 160, height: 16, page: 5 },
            { x: 90, y: 280, width: 140, height: 16, page: 5 }
          ],
          doc2: [
            { x: 72, y: 198, width: 450, height: 24, page: 5 },
            { x: 90, y: 238, width: 180, height: 16, page: 5 },
            { x: 90, y: 258, width: 160, height: 16, page: 5 },
            { x: 90, y: 278, width: 140, height: 16, page: 5 }
          ]
        }
      }
    },
    {
      id: 'finding-4',
      findingType: 'layout_ocr_comparison',
      riskLevel: 'high',
      summary: 'Layout positioning analysis reveals identical table structures and formatting patterns. Boundary boxes show 94% overlap in cost breakdown sections.',
      evidence: {
        chunkId1: 'chunk-6-doc1',
        chunkId2: 'chunk-6-doc2',
        score: 0.94,
        highlightDetails: {
          text1: 'COST BREAKDOWN\n\nItem | Quantity | Unit Price | Total\nLabor | 120 days | $2,500 | $300,000\nMaterials | - | Cost + 15% | $180,000',
          text2: 'COST BREAKDOWN\n\nItem | Quantity | Unit Price | Total\nLabor | 125 days | $2,450 | $306,250\nMaterials | - | Cost + 15% | $175,000'
        },
        boundingBoxes: {
          doc1: [
            { x: 72, y: 400, width: 450, height: 20, page: 8 },
            { x: 72, y: 440, width: 450, height: 80, page: 8 }
          ],
          doc2: [
            { x: 72, y: 402, width: 450, height: 20, page: 8 },
            { x: 72, y: 442, width: 450, height: 80, page: 8 }
          ]
        }
      }
    },
    {
      id: 'finding-5',
      findingType: 'llm_service_price_extraction',
      riskLevel: 'medium',
      summary: 'LLM analysis detected suspicious pricing patterns. Similar services with nearly identical markup percentages and cost structures.',
      evidence: {
        chunkId1: 'chunk-3-doc1',
        chunkId2: 'chunk-3-doc2',
        score: 0.87,
        highlightDetails: {
          text1: 'Labor services: $2,500/day × 120 days = $300,000\nMaterial procurement with 15% markup\nEquipment rental: $50,000',
          text2: 'Labor services: $2,450/day × 125 days = $306,250\nMaterial procurement with 15% markup\nEquipment rental: $48,000'
        },
        extractedServices: {
          doc1: [
            { service: 'Daily Labor', price: 2500, suspicious: true },
            { service: 'Material Markup', price: 0.15, suspicious: true },
            { service: 'Equipment Rental', price: 50000, suspicious: false }
          ],
          doc2: [
            { service: 'Daily Labor', price: 2450, suspicious: true },
            { service: 'Material Markup', price: 0.15, suspicious: true },
            { service: 'Equipment Rental', price: 48000, suspicious: false }
          ]
        }
      }
    },
    {
      id: 'finding-6',
      findingType: 'llm_service_price_extraction',
      riskLevel: 'high',
      summary: 'LLM detected identical service descriptions with suspicious price coordination. Multiple services show coordinated pricing strategies.',
      evidence: {
        chunkId1: 'chunk-7-doc1',
        chunkId2: 'chunk-7-doc2',
        score: 0.93,
        highlightDetails: {
          text1: 'Site preparation and cleanup services\nSafety equipment and training programs\nQuality assurance and testing protocols',
          text2: 'Site preparation and cleanup services\nSafety equipment and training programs\nQuality assurance and testing protocols'
        },
        extractedServices: {
          doc1: [
            { service: 'Site Preparation', price: 25000, suspicious: true },
            { service: 'Safety Programs', price: 12000, suspicious: true },
            { service: 'Quality Assurance', price: 8500, suspicious: true }
          ],
          doc2: [
            { service: 'Site Preparation', price: 24800, suspicious: true },
            { service: 'Safety Programs', price: 12100, suspicious: true },
            { service: 'Quality Assurance', price: 8400, suspicious: true }
          ]
        }
      }
    }
  ]

  const algorithmFindings = {
    document_layout_embedding: mockFindings.filter(f => f.findingType === 'document_layout_embedding'),
    layout_ocr_comparison: mockFindings.filter(f => f.findingType === 'layout_ocr_comparison'),
    llm_service_price_extraction: mockFindings.filter(f => f.findingType === 'llm_service_price_extraction')
  }

  // Flatten all findings into one continuous list with section markers
  const allFindings = [
    ...algorithmFindings.document_layout_embedding,
    ...algorithmFindings.layout_ocr_comparison,
    ...algorithmFindings.llm_service_price_extraction
  ]

  const handleFindingSelect = (finding: FindingData) => {
    setSelectedFinding(finding)
    setShowDocumentViewer(true)
  }

  const handleAddToReport = (findingId: string) => {
    setSelectedForReport(prev => 
      prev.includes(findingId) 
        ? prev.filter(id => id !== findingId)
        : [...prev, findingId]
    )
  }

  // Handle algorithm tab click - scroll to section
  const handleAlgorithmChange = (algorithm: string) => {
    setActiveAlgorithm(algorithm)
    const sectionElement = sectionRefs.current[algorithm]
    if (sectionElement && scrollContainerRef.current) {
      sectionElement.scrollIntoView({ 
        behavior: 'smooth', 
        block: 'start' 
      })
    }
  }

  // Intersection Observer to track which section is in view
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        for (const entry of entries) {
          if (entry.isIntersecting) {
            const sectionId = entry.target.getAttribute('data-section')
            if (sectionId) {
              setActiveAlgorithm(sectionId)
            }
          }
        }
      },
      {
        root: scrollContainerRef.current,
        rootMargin: '-20% 0px -60% 0px',
        threshold: 0.1
      }
    )

    // Observe all section elements
    for (const element of Object.values(sectionRefs.current)) {
      if (element) {
        observer.observe(element)
      }
    }

    return () => observer.disconnect()
  }, [])

  if (!documentData) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-slate-900 mb-2">Documents Not Found</h2>
          <p className="text-slate-600 mb-4">Could not find the specified documents for comparison.</p>
          <Link 
            to="/dashboard"
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Back to Dashboard
          </Link>
        </div>
      </div>
    )
  }

  const { doc1, doc2, tender } = documentData

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="bg-white border-b border-slate-200 px-6 py-4 flex-shrink-0">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Link 
              to="/dashboard"
              className="p-2 hover:bg-slate-100 rounded-lg transition-colors"
            >
              <svg className="w-5 h-5 text-slate-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <title>Back</title>
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </Link>
            
            <div>
              <h1 className="text-xl font-semibold text-slate-900">Document Comparison</h1>
              <p className="text-sm text-slate-600 mt-1">
                {doc1.companyName} vs {doc2.companyName} • {tender.title}
              </p>
            </div>
          </div>

          <div className="flex items-center space-x-3">
            <button
              type="button"
              onClick={() => setShowDocumentViewer(!showDocumentViewer)}
              className={`px-4 py-2 rounded-lg border transition-colors ${
                showDocumentViewer 
                  ? 'bg-blue-50 border-blue-200 text-blue-700'
                  : 'bg-white border-slate-300 text-slate-700 hover:bg-slate-50'
              }`}
            >
              {showDocumentViewer ? 'Hide' : 'Show'} Documents
            </button>
            
            <Link
              to="/report/$projectId"
              params={{ projectId: tender.id }}
              className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
            >
              Generate Report ({selectedForReport.length})
            </Link>
          </div>
        </div>
      </div>

      <div className="flex-1 flex overflow-hidden">
        {/* Main Content */}
        <div className="flex-1 flex flex-col">
          {/* Fixed Header Section */}
          <div className="bg-white border-b border-slate-200 flex-shrink-0">
            <div className="max-w-4xl mx-auto p-6">
              {/* Company Comparison Header */}
              <div className="grid grid-cols-2 gap-6 mb-6">
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <h3 className="font-semibold text-blue-900 mb-2">{doc1.companyName}</h3>
                  <div className="text-sm text-blue-700 space-y-1">
                    <div>Bid Amount: ${doc1.totalPrice?.toLocaleString() || 'N/A'}</div>
                    <div>File: {doc1.fileName}</div>
                    <div>Uploaded: {new Date(doc1.uploadDate).toLocaleDateString()}</div>
                  </div>
                </div>
                
                <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
                  <h3 className="font-semibold text-orange-900 mb-2">{doc2.companyName}</h3>
                  <div className="text-sm text-orange-700 space-y-1">
                    <div>Bid Amount: ${doc2.totalPrice?.toLocaleString() || 'N/A'}</div>
                    <div>File: {doc2.fileName}</div>
                    <div>Uploaded: {new Date(doc2.uploadDate).toLocaleDateString()}</div>
                  </div>
                </div>
              </div>

              {/* Algorithm Tabs */}
              <AlgorithmTabs 
                activeAlgorithm={activeAlgorithm}
                onAlgorithmChange={handleAlgorithmChange}
                findingCounts={{
                  document_layout_embedding: algorithmFindings.document_layout_embedding.length,
                  layout_ocr_comparison: algorithmFindings.layout_ocr_comparison.length,
                  llm_service_price_extraction: algorithmFindings.llm_service_price_extraction.length
                }}
              />
            </div>
          </div>

          {/* Scrollable Findings List */}
          <div ref={scrollContainerRef} className="flex-1 overflow-y-auto bg-slate-50">
            <div className="max-w-4xl mx-auto p-6">
              {/* All Findings in Continuous List */}
              <div className="space-y-8">
              {/* Document Layout + Embedding Section */}
              {algorithmFindings.document_layout_embedding.length > 0 && (
                <div 
                  ref={(el) => {
                    if (el) sectionRefs.current.document_layout_embedding = el
                  }}
                  data-section="document_layout_embedding"
                  className="scroll-mt-24"
                >
                  <div className="flex items-center space-x-3 mb-6">
                    <div className="w-3 h-3 bg-blue-500 rounded-full" />
                    <h2 className="text-xl font-semibold text-slate-900">Document Layout + Vector Similarity</h2>
                    <span className="text-sm text-slate-500">({algorithmFindings.document_layout_embedding.length} findings)</span>
                  </div>
                  <div className="space-y-4">
                    {algorithmFindings.document_layout_embedding.map((finding) => (
                      <ComparisonCard
                        key={finding.id}
                        finding={finding}
                        isSelected={selectedFinding?.id === finding.id}
                        isAddedToReport={selectedForReport.includes(finding.id)}
                        onClick={() => handleFindingSelect(finding)}
                        onAddToReport={() => handleAddToReport(finding.id)}
                      />
                    ))}
                  </div>
                </div>
              )}

              {/* Layout OCR Comparison Section */}
              {algorithmFindings.layout_ocr_comparison.length > 0 && (
                <div 
                  ref={(el) => {
                    if (el) sectionRefs.current.layout_ocr_comparison = el
                  }}
                  data-section="layout_ocr_comparison"
                  className="scroll-mt-24"
                >
                  <div className="flex items-center space-x-3 mb-6">
                    <div className="w-3 h-3 bg-orange-500 rounded-full" />
                    <h2 className="text-xl font-semibold text-slate-900">Layout OCR Boundary Comparison</h2>
                    <span className="text-sm text-slate-500">({algorithmFindings.layout_ocr_comparison.length} findings)</span>
                  </div>
                  <div className="space-y-4">
                    {algorithmFindings.layout_ocr_comparison.map((finding) => (
                      <ComparisonCard
                        key={finding.id}
                        finding={finding}
                        isSelected={selectedFinding?.id === finding.id}
                        isAddedToReport={selectedForReport.includes(finding.id)}
                        onClick={() => handleFindingSelect(finding)}
                        onAddToReport={() => handleAddToReport(finding.id)}
                      />
                    ))}
                  </div>
                </div>
              )}

              {/* LLM Service & Price Analysis Section */}
              {algorithmFindings.llm_service_price_extraction.length > 0 && (
                <div 
                  ref={(el) => {
                    if (el) sectionRefs.current.llm_service_price_extraction = el
                  }}
                  data-section="llm_service_price_extraction"
                  className="scroll-mt-24"
                >
                  <div className="flex items-center space-x-3 mb-6">
                    <div className="w-3 h-3 bg-green-500 rounded-full" />
                    <h2 className="text-xl font-semibold text-slate-900">LLM Service & Price Analysis</h2>
                    <span className="text-sm text-slate-500">({algorithmFindings.llm_service_price_extraction.length} findings)</span>
                  </div>
                  <div className="space-y-4">
                    {algorithmFindings.llm_service_price_extraction.map((finding) => (
                      <ComparisonCard
                        key={finding.id}
                        finding={finding}
                        isSelected={selectedFinding?.id === finding.id}
                        isAddedToReport={selectedForReport.includes(finding.id)}
                        onClick={() => handleFindingSelect(finding)}
                        onAddToReport={() => handleAddToReport(finding.id)}
                      />
                    ))}
                  </div>
                </div>
              )}

              {/* Empty State */}
              {allFindings.length === 0 && (
                <div className="text-center py-12 text-slate-500">
                  <div className="w-16 h-16 bg-slate-100 rounded-full mx-auto flex items-center justify-center mb-4">
                    <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <title>No findings</title>
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                  </div>
                  <h3 className="font-medium text-slate-900 mb-2">No findings detected</h3>
                  <p>No suspicious similarities found between these documents.</p>
                </div>
              )}
              </div>
            </div>
          </div>
        </div>

        {/* Document Viewer Panel */}
        {showDocumentViewer && selectedFinding && (
          <div className="w-1/2 border-l border-slate-200 bg-white">
            <DocumentViewer
              documents={[
                { 
                  id: doc1.id, 
                  name: doc1.companyName, 
                  path: doc1.fileName,
                  highlightChunk: selectedFinding.evidence.chunkId1 
                },
                { 
                  id: doc2.id, 
                  name: doc2.companyName, 
                  path: doc2.fileName,
                  highlightChunk: selectedFinding.evidence.chunkId2 
                }
              ]}
              mode="side-by-side"
              selectedFinding={{
                id: selectedFinding.id,
                findingType: selectedFinding.findingType,
                riskLevel: selectedFinding.riskLevel,
                summary: selectedFinding.summary,
                evidence: {
                  text1: selectedFinding.evidence.highlightDetails.text1,
                  text2: selectedFinding.evidence.highlightDetails.text2,
                  score: selectedFinding.evidence.score
                }
              }}
            />
          </div>
        )}
      </div>
    </div>
  )
}