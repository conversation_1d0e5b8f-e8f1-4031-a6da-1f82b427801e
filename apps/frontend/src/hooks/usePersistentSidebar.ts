import { useState, useEffect, useCallback } from 'react'

interface SidebarState {
  leftSidebar: boolean
  rightSidebar: boolean
}

const STORAGE_KEY = 'tracefast-sidebar-state'

const defaultState: SidebarState = {
  leftSidebar: true,  // Left sidebar expanded by default
  rightSidebar: false // Right sidebar collapsed by default
}

/**
 * Custom hook for managing persistent sidebar state using localStorage
 * Provides a clean API for both left and right sidebar expansion states
 */
export function usePersistentSidebar() {
  const [sidebarState, setSidebarState] = useState<SidebarState>(defaultState)

  // Load state from localStorage on mount
  useEffect(() => {
    try {
      const saved = localStorage.getItem(STORAGE_KEY)
      if (saved) {
        const parsed = JSON.parse(saved) as Partial<SidebarState>
        setSidebarState(prev => ({ ...prev, ...parsed }))
      }
    } catch (error) {
      console.warn('Failed to load sidebar state from localStorage:', error)
    }
  }, [])

  // Save state to localStorage whenever it changes
  const saveState = useCallback((newState: SidebarState) => {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(newState))
    } catch (error) {
      console.warn('Failed to save sidebar state to localStorage:', error)
    }
  }, [])

  // Toggle left sidebar
  const toggleLeftSidebar = useCallback(() => {
    setSidebarState(prev => {
      const newState = { ...prev, leftSidebar: !prev.leftSidebar }
      saveState(newState)
      return newState
    })
  }, [saveState])

  // Toggle right sidebar
  const toggleRightSidebar = useCallback(() => {
    setSidebarState(prev => {
      const newState = { ...prev, rightSidebar: !prev.rightSidebar }
      saveState(newState)
      return newState
    })
  }, [saveState])

  // Set left sidebar state explicitly
  const setLeftSidebar = useCallback((expanded: boolean) => {
    setSidebarState(prev => {
      const newState = { ...prev, leftSidebar: expanded }
      saveState(newState)
      return newState
    })
  }, [saveState])

  // Set right sidebar state explicitly
  const setRightSidebar = useCallback((expanded: boolean) => {
    setSidebarState(prev => {
      const newState = { ...prev, rightSidebar: expanded }
      saveState(newState)
      return newState
    })
  }, [saveState])

  return {
    // State
    leftSidebarExpanded: sidebarState.leftSidebar,
    rightSidebarExpanded: sidebarState.rightSidebar,
    
    // Toggles
    toggleLeftSidebar,
    toggleRightSidebar,
    
    // Setters
    setLeftSidebar,
    setRightSidebar,
  }
}