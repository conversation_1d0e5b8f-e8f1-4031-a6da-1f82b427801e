import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { bidService } from '../services';
import { backupMockBids } from '../data/mockData';

export const useTenderBids = (tenderId: string) => {
  return useQuery({
    queryKey: ['tender-bids', tenderId],
    queryFn: () => bidService.getTenderBids(tenderId),
    select: (data) => data.map(bidService.convertToFrontendBid),
    enabled: !!tenderId,
    // Fallback to mock data if API fails
    placeholderData: backupMockBids.slice(0, 2),
    retry: 1,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
};

export const useBid = (bidId: string) => {
  return useQuery({
    queryKey: ['bid', bidId],
    queryFn: () => bidService.getBid(bidId),
    select: (data) => bidService.convertToFrontendBid(data),
    enabled: !!bidId,
    retry: 1,
    staleTime: 5 * 60 * 1000,
  });
};

export const useBidFileInfo = (bidId: string) => {
  return useQuery({
    queryKey: ['bid-file-info', bidId],
    queryFn: () => bidService.getBidFileInfo(bidId),
    enabled: !!bidId,
    retry: 1,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
};

export const useBidExtractedContent = (bidId: string) => {
  return useQuery({
    queryKey: ['bid-content', bidId],
    queryFn: () => bidService.getBidExtractedContent(bidId),
    enabled: !!bidId,
    retry: 1,
    staleTime: 30 * 60 * 1000, // 30 minutes
  });
};

export const useUploadBids = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ 
      tenderId, 
      files, 
      companies, 
      prices 
    }: { 
      tenderId: string; 
      files: File[]; 
      companies: string[]; 
      prices: number[] 
    }) => bidService.uploadBidFiles(tenderId, files, companies, prices),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ['tender-bids', variables.tenderId] });
      queryClient.invalidateQueries({ queryKey: ['tender-summary', variables.tenderId] });
    },
  });
};

export const useDeleteBid = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (bidId: string) => bidService.deleteBid(bidId),
    onSuccess: (_, bidId) => {
      // Invalidate all related queries
      queryClient.invalidateQueries({ queryKey: ['tender-bids'] });
      queryClient.removeQueries({ queryKey: ['bid', bidId] });
      queryClient.removeQueries({ queryKey: ['bid-file-info', bidId] });
      queryClient.removeQueries({ queryKey: ['bid-content', bidId] });
    },
  });
};

export const useReprocessBid = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (bidId: string) => bidService.reprocessBid(bidId),
    onSuccess: (_, bidId) => {
      queryClient.invalidateQueries({ queryKey: ['bid-content', bidId] });
      queryClient.invalidateQueries({ queryKey: ['bid-file-info', bidId] });
    },
  });
};