import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { analysisService } from '../services';

export const useAnalysisStatus = (tenderId: string) => {
  return useQuery({
    queryKey: ['analysis-status', tenderId],
    queryFn: () => analysisService.getAnalysisStatus(tenderId),
    enabled: !!tenderId,
    retry: 1,
    refetchInterval: (data) => 
      data?.status === 'analyzing' ? 5000 : false, // Poll every 5s when analyzing
    staleTime: 30 * 1000, // 30 seconds
  });
};

export const useAnalysisResults = (tenderId: string) => {
  return useQuery({
    queryKey: ['analysis-results', tenderId],
    queryFn: () => analysisService.getAnalysisResults(tenderId),
    enabled: !!tenderId,
    retry: 1,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useAnalysisSummary = (tenderId: string) => {
  return useQuery({
    queryKey: ['analysis-summary', tenderId],
    queryFn: () => analysisService.getAnalysisSummary(tenderId),
    enabled: !!tenderId,
    retry: 1,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
};

export const useSuspiciousSegments = (
  tenderId: string, 
  category?: string, 
  riskLevel?: string
) => {
  return useQuery({
    queryKey: ['suspicious-segments', tenderId, category, riskLevel],
    queryFn: () => analysisService.getSuspiciousSegments(tenderId, category, riskLevel),
    enabled: !!tenderId,
    retry: 1,
    staleTime: 5 * 60 * 1000,
    select: (data) => analysisService.convertToFrontendSegments(data),
  });
};

export const useAnalysisHealth = () => {
  return useQuery({
    queryKey: ['analysis-health'],
    queryFn: () => analysisService.getHealthStatus(),
    retry: 1,
    staleTime: 2 * 60 * 1000,
    refetchOnWindowFocus: false,
  });
};

export const useModelInfo = () => {
  return useQuery({
    queryKey: ['model-info'],
    queryFn: () => analysisService.getModelInfo(),
    retry: 1,
    staleTime: 30 * 60 * 1000, // 30 minutes
    refetchOnWindowFocus: false,
  });
};

export const useStartAnalysis = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (tenderId: string) => analysisService.startAnalysis(tenderId),
    onSuccess: (_, tenderId) => {
      // Invalidate and refetch analysis-related queries
      queryClient.invalidateQueries({ queryKey: ['analysis-status', tenderId] });
      queryClient.invalidateQueries({ queryKey: ['analysis-results', tenderId] });
      queryClient.invalidateQueries({ queryKey: ['analysis-summary', tenderId] });
      queryClient.invalidateQueries({ queryKey: ['suspicious-segments', tenderId] });
      queryClient.invalidateQueries({ queryKey: ['tender-summary', tenderId] });
    },
  });
};

export const useGenerateAIInsights = () => {
  return useMutation({
    mutationFn: ({ tenderId, segmentIds }: { tenderId: string; segmentIds: string[] }) =>
      analysisService.generateAIInsights(tenderId, segmentIds),
  });
};

export const useUpdateSegmentPosition = () => {
  return useMutation({
    mutationFn: ({ segmentId, position }: { 
      segmentId: string; 
      position: { x: number; y: number } 
    }) => analysisService.updateSegmentPosition(segmentId, position),
    onSuccess: (_, { segmentId }) => {
      // Optionally invalidate segment queries if needed
      // For now, we'll let the local state handle the update optimistically
      console.log(`Position updated for segment ${segmentId}`);
    },
    onError: (error) => {
      console.warn('Failed to save segment position:', error);
    },
  });
};