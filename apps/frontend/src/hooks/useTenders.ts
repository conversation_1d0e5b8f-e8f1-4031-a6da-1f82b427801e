import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { tenderService } from '../services';
import type { TenderCreate, TenderResponse } from '../services';
import { backupMockTender } from '../data/mockData';

export const useTenders = (skip = 0, limit = 100) => {
  return useQuery({
    queryKey: ['tenders', skip, limit],
    queryFn: () => tenderService.getTenders(skip, limit),
    select: (data) => data.map(tenderService.convertToFrontendTender),
    // Fallback to mock data if API fails
    placeholderData: [backupMockTender],
    retry: 1,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useTender = (tenderId: string) => {
  return useQuery({
    queryKey: ['tender', tenderId],
    queryFn: () => tenderService.getTender(tenderId),
    select: (data) => tenderService.convertToFrontendTender(data),
    enabled: !!tenderId,
    retry: 1,
    staleTime: 5 * 60 * 1000,
  });
};

export const useTenderSummary = (tenderId: string) => {
  return useQuery({
    queryKey: ['tender-summary', tenderId],
    queryFn: () => tenderService.getTenderSummary(tenderId),
    enabled: !!tenderId,
    retry: 1,
    staleTime: 1 * 60 * 1000, // 1 minute
  });
};

export const useCreateTender = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data: TenderCreate) => tenderService.createTender(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['tenders'] });
    },
  });
};

export const useUpdateTender = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: TenderCreate }) => 
      tenderService.updateTender(id, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ['tenders'] });
      queryClient.invalidateQueries({ queryKey: ['tender', variables.id] });
      queryClient.invalidateQueries({ queryKey: ['tender-summary', variables.id] });
    },
  });
};

export const useDeleteTender = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (tenderId: string) => tenderService.deleteTender(tenderId),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ['tenders'] });
      queryClient.removeQueries({ queryKey: ['tender', variables] });
      queryClient.removeQueries({ queryKey: ['tender-summary', variables] });
    },
  });
};