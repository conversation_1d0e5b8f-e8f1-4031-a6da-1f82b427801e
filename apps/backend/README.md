# TraceFast Backend

Public Procurement Analysis Platform - Backend API

## Overview

The TraceFast backend provides comprehensive document analysis capabilities for detecting bid collusion and procurement fraud through multi-dimensional comparison algorithms. Built with FastAPI, it offers a robust and scalable foundation for the TraceFast platform.

## Features

- 🔍 **Multi-Dimensional Analysis**: Semantic, stylometry, structural, and numerical comparison
- 📄 **OCR Processing**: Extract text and layout from PDF documents (placeholder for open-source models)
- 🛡️ **Privacy-First**: All processing done locally with no external API dependencies
- 💾 **SQLite Database**: Simple, file-based database perfect for MVP deployment
- 📁 **File Management**: Organized local storage with validation and metadata
- 🚀 **FastAPI**: Modern, fast, and well-documented API framework
- 🔐 **Comprehensive Validation**: Input validation and error handling throughout

## Quick Start

### Prerequisites

- Python 3.11+
- UV package manager (recommended) or pip

### Installation

1. Navigate to the backend directory:
```bash
cd apps/backend
```

2. Install dependencies:
```bash
uv sync
```

3. Run the server:
```bash
uv run python -m src.app.main
```

The API will be available at `http://localhost:8000`

### API Documentation

- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc

## API Endpoints

### Health & Status
- `GET /health` - Basic health check
- `GET /status` - Detailed service status
- `GET /api/v1/hello` - Demo endpoint

### Tenders
- `GET /api/v1/tenders/` - List all tenders
- `POST /api/v1/tenders/` - Create new tender
- `GET /api/v1/tenders/{tender_id}` - Get tender details
- `PUT /api/v1/tenders/{tender_id}` - Update tender
- `DELETE /api/v1/tenders/{tender_id}` - Delete tender
- `GET /api/v1/tenders/{tender_id}/summary` - Get tender summary

### Bids
- `POST /api/v1/bids/upload` - Upload bid files
- `GET /api/v1/bids/{bid_id}` - Get bid details
- `DELETE /api/v1/bids/{bid_id}` - Delete bid
- `GET /api/v1/bids/tender/{tender_id}` - Get all bids for tender
- `GET /api/v1/bids/{bid_id}/file-info` - Get file information
- `POST /api/v1/bids/{bid_id}/reprocess` - Reprocess bid content
- `GET /api/v1/bids/{bid_id}/extracted-content` - Get extracted content

### Analysis
- `POST /api/v1/analysis/start/{tender_id}` - Start analysis
- `GET /api/v1/analysis/status/{tender_id}` - Get analysis status
- `GET /api/v1/analysis/results/{tender_id}` - Get analysis results
- `GET /api/v1/analysis/results/{tender_id}/summary` - Get analysis summary
- `GET /api/v1/analysis/results/{tender_id}/segments` - Get suspicious segments
- `POST /api/v1/analysis/insights/{tender_id}` - Generate AI insights
- `GET /api/v1/analysis/health` - Analysis services health check
- `GET /api/v1/analysis/models/info` - Model information

## Architecture

### Directory Structure
```
src/app/
├── api/v1/                 # API endpoints
│   ├── tenders.py         # Tender management endpoints
│   ├── bids.py           # Bid management endpoints
│   └── analysis.py       # Analysis endpoints
├── core/                  # Core configuration and database
│   ├── config.py         # Application settings
│   └── database.py       # Database connection and setup
├── models/               # Data models
│   ├── database.py       # SQLAlchemy models
│   └── schemas.py        # Pydantic API models
├── services/             # Business logic
│   ├── file_service.py   # File operations
│   ├── ocr_service.py    # OCR processing (placeholder)
│   ├── analysis_service.py # Main analysis coordinator
│   └── comparison/       # Analysis algorithms
├── storage/              # File storage management
├── utils/                # Utilities and validators
└── main.py              # FastAPI application
```

### Analysis Pipeline

1. **File Upload**: PDF files are validated and stored locally
2. **OCR Processing**: Extract text and layout information (placeholder functions)
3. **Multi-Dimensional Analysis**:
   - **Semantic**: Text similarity using embeddings (placeholder)
   - **Stylometry**: Writing style analysis (15+ features)
   - **Structural**: Document layout comparison
   - **Numerical**: Price and specification analysis
4. **Result Aggregation**: Generate suspicious segments and risk scores
5. **Whiteboard Data**: Format results for frontend visualization

## Configuration

### Environment Variables

Create a `.env` file in the backend directory:

```bash
# Database
DATABASE_URL=sqlite:///./tracefast.db

# File Storage
STORAGE_PATH=./storage
MAX_FILE_SIZE=52428800  # 50MB
ALLOWED_EXTENSIONS=[".pdf"]

# Analysis
MAX_CONCURRENT_ANALYSIS=5
ANALYSIS_TIMEOUT=300

# API
API_PREFIX=/api/v1
FRONTEND_ORIGIN=http://localhost:5173
PORT=8000
```

### Settings

All configuration is managed through `core/config.py` with Pydantic Settings:

- **Database**: SQLite by default, easily upgradeable to PostgreSQL
- **Storage**: Local file system with organized directory structure
- **Analysis**: Configurable timeouts and concurrency limits
- **CORS**: Frontend origin configuration for development

## Development

### Database

The application uses SQLAlchemy with SQLite for the MVP. Tables are automatically created on startup.

**Models**:
- `Tender`: Project/procurement information
- `Bid`: Company submissions with file references
- `AnalysisResult`: Comparison results between bid pairs
- `SuspiciousSegment`: Individual suspicious text segments

### Adding New Analysis Algorithms

1. Create a new module in `services/comparison/`
2. Implement the analysis logic
3. Add to `AnalysisService` in `services/analysis_service.py`
4. Update the API documentation

### File Storage

Files are organized as:
```
storage/
├── tenders/
│   └── {tender_id}/
│       └── bids/
│           ├── {bid_id}.pdf
│           └── extracted/
│               └── {bid_id}/
│                   ├── text.txt
│                   ├── layout.json
│                   └── metadata.json
```

## Open Source Model Integration

The backend is designed with placeholder functions ready for open-source model integration:

### OCR Integration Points
- `OCRService.extract_document_content()` in `services/ocr_service.py`
- Recommended: PaddleOCR, Tesseract, or similar

### Embedding Model Integration Points
- `SemanticComparison.get_embeddings()` in `services/comparison/semantic.py`
- Recommended: sentence-transformers, all-MiniLM-L6-v2, or similar

### LLM Integration Points
- `generate_ai_insights()` in `api/v1/analysis.py`
- Recommended: Llama, Mistral, or similar local models

## Testing

Run tests with:
```bash
uv run pytest
```

## Deployment

### Development
```bash
uv run python -m src.app.main
```

### Production
```bash
uv run uvicorn src.app.main:app --host 0.0.0.0 --port 8000
```

### Docker (Future)
```dockerfile
FROM python:3.11-slim
COPY . /app
WORKDIR /app
RUN pip install uv && uv sync
CMD ["uv", "run", "uvicorn", "src.app.main:app", "--host", "0.0.0.0"]
```

## Contributing

1. Follow the existing code structure and patterns
2. Add comprehensive error handling
3. Include input validation for all endpoints
4. Update API documentation
5. Add tests for new functionality

## License

This project is part of the TraceFast competition submission.

---

For more information, see the complete backend plan documentation in `docs/backend-plan.md`.
