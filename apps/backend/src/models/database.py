from sqlalchemy import Column, String, Float, DateTime, Text, Foreign<PERSON>ey, Enum, Integer, UniqueConstraint, JSON
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import enum
import uuid
from ..core.database import Base


class ProjectStatus(str, enum.Enum):
    UPLOADING = "uploading"
    ANALYZING = "analyzing"
    COMPLETED = "completed"
    ERROR = "error"


class DocumentStatus(str, enum.Enum):
    PENDING = "pending"
    PROCESSING = "processing"
    PROCESSED = "processed"
    ERROR = "error"


class ChunkType(str, enum.Enum):
    TEXT = "text"
    IMAGE = "image"
    TABLE = "table"


class RiskLevel(str, enum.Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class AnalysisType(str, enum.Enum):
    SEMANTIC = "semantic"
    STYLOMETRY = "stylometry"
    STRUCTURAL = "structural"
    NUMERICAL = "numerical"


# New Schema Tables (Version 3)

class Project(Base):
    __tablename__ = "projects"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(255), nullable=False)
    status = Column(Enum(ProjectStatus), nullable=False, default=ProjectStatus.UPLOADING)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # Relationships
    documents = relationship("Document", back_populates="project", cascade="all, delete-orphan")
    analysis_results = relationship("AnalysisResult", back_populates="project", cascade="all, delete-orphan")


class Document(Base):
    __tablename__ = "documents"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id"), nullable=False)
    company_name = Column(String(255), nullable=True)
    original_filename = Column(String(255), nullable=False)
    storage_path = Column(String(1024), nullable=False)
    page_count = Column(Integer, nullable=True)
    status = Column(Enum(DocumentStatus), nullable=False, default=DocumentStatus.PENDING)
    uploaded_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    project = relationship("Project", back_populates="documents")
    chunks = relationship("DocumentChunk", back_populates="document", cascade="all, delete-orphan")
    # Analysis relationships
    analysis_as_doc1 = relationship("AnalysisResult", back_populates="document_1", foreign_keys="AnalysisResult.document_1_id")
    analysis_as_doc2 = relationship("AnalysisResult", back_populates="document_2", foreign_keys="AnalysisResult.document_2_id")


class DocumentChunk(Base):
    __tablename__ = "document_chunks"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    document_id = Column(UUID(as_uuid=True), ForeignKey("documents.id"), nullable=False)
    page_number = Column(Integer, nullable=False)
    chunk_type = Column(Enum(ChunkType), nullable=False)
    content = Column(Text, nullable=True)  # OCR text content for text chunks
    bounding_box = Column(JSON, nullable=False)  # Position coordinates: {'x1': 100, 'y1': 200, 'x2': 500, 'y2': 250}
    # embedding = Column(VECTOR(1536), nullable=True)  # Requires pgvector extension

    # Relationships
    document = relationship("Document", back_populates="chunks")


class AnalysisResult(Base):
    __tablename__ = "analysis_results"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id"), nullable=False)
    document_1_id = Column(UUID(as_uuid=True), ForeignKey("documents.id"), nullable=False)
    document_2_id = Column(UUID(as_uuid=True), ForeignKey("documents.id"), nullable=False)
    
    # JSONB field for algorithm extensibility
    overall_scores = Column(JSON, nullable=False)
    
    # JSONB field storing detailed chunk comparison evidence
    detailed_findings = Column(JSON, nullable=True)
    
    analysis_version = Column(String(50), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Ensure only one analysis result per document pair
    __table_args__ = (
        UniqueConstraint('project_id', 'document_1_id', 'document_2_id', name='unique_document_pair_analysis'),
    )

    # Relationships
    project = relationship("Project", back_populates="analysis_results")
    document_1 = relationship("Document", back_populates="analysis_as_doc1", foreign_keys=[document_1_id])
    document_2 = relationship("Document", back_populates="analysis_as_doc2", foreign_keys=[document_2_id])


# Legacy Tables (kept for backward compatibility during migration)

class Tender(Base):
    __tablename__ = "tenders"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    title = Column(String, nullable=False)
    publish_date = Column(DateTime, nullable=False)
    department = Column(String, nullable=False)
    budget = Column(Float, nullable=False)
    analysis_status = Column(Enum(ProjectStatus), default=ProjectStatus.UPLOADING)
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())

    # Relationships
    bids = relationship("Bid", back_populates="tender", cascade="all, delete-orphan")
    legacy_analysis_results = relationship("LegacyAnalysisResult", back_populates="tender", cascade="all, delete-orphan")


class Bid(Base):
    __tablename__ = "bids"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    tender_id = Column(String, ForeignKey("tenders.id"), nullable=False)
    company_name = Column(String, nullable=False)
    file_name = Column(String, nullable=False)
    file_path = Column(String, nullable=False)
    total_price = Column(Float, nullable=False)
    upload_date = Column(DateTime, server_default=func.now())
    created_at = Column(DateTime, server_default=func.now())

    # Relationships
    tender = relationship("Tender", back_populates="bids")
    suspicious_segments = relationship("SuspiciousSegment", back_populates="source_bid", cascade="all, delete-orphan", foreign_keys="SuspiciousSegment.source_bid_id")


class LegacyAnalysisResult(Base):
    __tablename__ = "legacy_analysis_results"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    tender_id = Column(String, ForeignKey("tenders.id"), nullable=False)
    bid_pair = Column(Text, nullable=False)  # JSON string: [bid_id_1, bid_id_2]
    overall_similarity = Column(Float, nullable=False)
    category_scores = Column(Text, nullable=False)  # JSON string
    risk_level = Column(Enum(RiskLevel), nullable=False)
    created_at = Column(DateTime, server_default=func.now())

    # Relationships
    tender = relationship("Tender", back_populates="legacy_analysis_results")
    suspicious_segments = relationship("SuspiciousSegment", back_populates="analysis_result", cascade="all, delete-orphan")


class SuspiciousSegment(Base):
    __tablename__ = "suspicious_segments"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    analysis_result_id = Column(String, ForeignKey("legacy_analysis_results.id"), nullable=False)
    content = Column(Text, nullable=False)
    source_bid_id = Column(String, ForeignKey("bids.id"), nullable=False)
    category = Column(Enum(AnalysisType), nullable=False)
    suspicion_level = Column(Enum(RiskLevel), nullable=False)
    similarity_score = Column(Float, nullable=False)
    position_x = Column(Float, default=0.0)
    position_y = Column(Float, default=0.0)
    created_at = Column(DateTime, server_default=func.now())
    
    # New fields for comparison pairs
    comparison_content = Column(Text, nullable=True)  # Content from the compared bid
    comparison_bid_id = Column(String, ForeignKey("bids.id"), nullable=True)  # ID of compared bid
    chapter = Column(String, nullable=True)  # Chapter/section information
    comparison_chapter = Column(String, nullable=True)  # Chapter/section of compared content

    # Relationships
    analysis_result = relationship("LegacyAnalysisResult", back_populates="suspicious_segments")
    source_bid = relationship("Bid", back_populates="suspicious_segments", foreign_keys=[source_bid_id])
    comparison_bid = relationship("Bid", foreign_keys=[comparison_bid_id])