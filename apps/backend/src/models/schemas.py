from pydantic import BaseModel, Field
from datetime import datetime
from typing import List, Dict, Tu<PERSON>, Optional, Any
from enum import Enum


class AnalysisStatus(str, Enum):
    UPLOADING = "uploading"
    ANALYZING = "analyzing"
    COMPLETED = "completed"
    ERROR = "error"


class RiskLevel(str, Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class AnalysisType(str, Enum):
    SEMANTIC = "semantic"
    STYLOMETRY = "stylometry"
    STRUCTURAL = "structural"
    NUMERICAL = "numerical"


class Position(BaseModel):
    x: float
    y: float


# Tender Schemas
class TenderBase(BaseModel):
    title: str
    publish_date: datetime
    department: str
    budget: float


class TenderCreate(TenderBase):
    pass


class TenderResponse(TenderBase):
    id: str
    analysis_status: AnalysisStatus
    created_at: datetime
    updated_at: datetime
    bids: List["BidResponse"] = []

    class Config:
        from_attributes = True


# Bid Schemas
class BidBase(BaseModel):
    company_name: str
    total_price: float


class BidUpload(BidBase):
    tender_id: str


class BidResponse(BidBase):
    id: str
    tender_id: str
    file_name: str
    upload_date: datetime
    analysis_results: List["AnalysisResultResponse"] = []

    class Config:
        from_attributes = True


# Analysis Schemas
class AnalysisRequest(BaseModel):
    tender_id: str


class AnalysisStatusResponse(BaseModel):
    tender_id: str
    status: str
    bid_count: int
    results_count: int
    progress: float = Field(ge=0, le=100, description="Progress percentage")
    message: str = ""


class CategoryScores(BaseModel):
    semantic: float = Field(ge=0, le=1)
    stylometry: float = Field(ge=0, le=1)
    structural: float = Field(ge=0, le=1)
    numerical: float = Field(ge=0, le=1)


class SuspiciousSegmentResponse(BaseModel):
    id: str
    content: str
    source_bid: str
    bid_company: str
    category: AnalysisType
    suspicion_level: RiskLevel
    position: Position
    similarity: float = Field(ge=0, le=1)
    chapter: Optional[str] = None
    # Comparison data
    comparison_content: Optional[str] = None
    comparison_bid: Optional[str] = None
    comparison_company: Optional[str] = None
    comparison_chapter: Optional[str] = None

    class Config:
        from_attributes = True


class AnalysisResultResponse(BaseModel):
    id: str
    bid_pair: List[str]  # Changed from Tuple to List to match service output
    overall_similarity: float = Field(ge=0, le=1)
    category_scores: Dict[str, float]  # Changed from CategoryScores to match service output
    suspicious_segments: List[SuspiciousSegmentResponse] = []
    risk_level: RiskLevel

    class Config:
        from_attributes = True


class AnalysisResults(BaseModel):
    tender_id: str
    status: str
    results: List[AnalysisResultResponse] = []
    summary: Dict[str, Any] = {}


# AI Insight Schemas
class AIInsight(BaseModel):
    group_id: str
    segments: List[SuspiciousSegmentResponse]
    explanation: str
    evidence_strength: float = Field(ge=0, le=1)
    recommended_action: str


class AnalysisCategory(BaseModel):
    type: AnalysisType
    name: str
    description: str
    color: str
    segments: List[SuspiciousSegmentResponse] = []


# Connection Schema (for whiteboard visualization)
class Connection(BaseModel):
    id: str
    from_segment_id: str
    to_segment_id: str
    strength: float = Field(ge=0, le=1)
    type: AnalysisType


# Error Response Schemas
class ErrorResponse(BaseModel):
    detail: str
    error_code: Optional[str] = None


class ValidationError(BaseModel):
    field: str
    message: str


class ValidationErrorResponse(BaseModel):
    detail: str = "Validation failed"
    errors: List[ValidationError]


# New Schema for Version 3 Dashboard

# Project Schemas
class ProjectBase(BaseModel):
    name: str

class ProjectCreate(ProjectBase):
    pass

class ProjectResponse(ProjectBase):
    id: str
    status: str
    created_at: datetime
    updated_at: datetime
    documents: List["DocumentResponse"] = []

    class Config:
        from_attributes = True


# Document Schemas  
class DocumentBase(BaseModel):
    company_name: Optional[str] = None
    original_filename: str

class DocumentCreate(DocumentBase):
    project_id: str
    storage_path: str
    page_count: Optional[int] = None

class DocumentResponse(DocumentBase):
    id: str
    project_id: str
    storage_path: str
    page_count: Optional[int] = None
    status: str
    uploaded_at: datetime

    class Config:
        from_attributes = True


# Heatmap Data Schema
class HeatmapDataPoint(BaseModel):
    document1_id: str
    document2_id: str
    company1_name: str
    company2_name: str
    overall_risk: float = Field(ge=0, le=1)
    semantic_similarity: float = Field(ge=0, le=1)
    layout_similarity: float = Field(ge=0, le=1)
    pricing_similarity: float = Field(ge=0, le=1)
    stylometry_similarity: Optional[float] = Field(ge=0, le=1, default=0)


class HeatmapDataResponse(BaseModel):
    project_id: str
    project_name: str
    heatmap_data: List[HeatmapDataPoint]


# Document Chunk Schema
class DocumentChunkResponse(BaseModel):
    id: str
    page_number: int
    chunk_type: str
    content: Optional[str] = None
    bounding_box: Dict[str, Any]

    class Config:
        from_attributes = True


# Update forward references
TenderResponse.model_rebuild()
BidResponse.model_rebuild()
ProjectResponse.model_rebuild()