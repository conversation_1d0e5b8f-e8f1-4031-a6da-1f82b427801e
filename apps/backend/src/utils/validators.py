from typing import List, Dict, Any, Optional
from fastapi import UploadFile
from datetime import datetime
import re

from .exceptions import ValidationException


def validate_tender_data(tender_data: Dict[str, Any]) -> Dict[str, Any]:
    """Validate tender creation data.
    
    Args:
        tender_data: Raw tender data from API request
        
    Returns:
        Validated and cleaned tender data
        
    Raises:
        ValidationException: If validation fails
    """
    errors = []
    
    # Required fields
    required_fields = ["title", "publish_date", "department", "budget"]
    for field in required_fields:
        if field not in tender_data or not tender_data[field]:
            errors.append(f"Field '{field}' is required")
    
    # Title validation
    if "title" in tender_data:
        title = tender_data["title"].strip()
        if len(title) < 5:
            errors.append("Title must be at least 5 characters long")
        if len(title) > 200:
            errors.append("Title must be less than 200 characters")
        tender_data["title"] = title
    
    # Department validation
    if "department" in tender_data:
        department = tender_data["department"].strip()
        if len(department) < 2:
            errors.append("Department must be at least 2 characters long")
        if len(department) > 100:
            errors.append("Department must be less than 100 characters")
        tender_data["department"] = department
    
    # Budget validation
    if "budget" in tender_data:
        try:
            budget = float(tender_data["budget"])
            if budget <= 0:
                errors.append("Budget must be greater than 0")
            if budget > 1e12:  # 1 trillion limit
                errors.append("Budget exceeds maximum allowed value")
            tender_data["budget"] = budget
        except (ValueError, TypeError):
            errors.append("Budget must be a valid number")
    
    # Date validation
    if "publish_date" in tender_data:
        publish_date = tender_data["publish_date"]
        if isinstance(publish_date, str):
            try:
                # Try to parse ISO format
                parsed_date = datetime.fromisoformat(publish_date.replace('Z', '+00:00'))
                tender_data["publish_date"] = parsed_date
            except ValueError:
                errors.append("Invalid date format. Use ISO format (YYYY-MM-DDTHH:MM:SS)")
        elif not isinstance(publish_date, datetime):
            errors.append("Publish date must be a valid datetime")
    
    if errors:
        raise ValidationException("Tender validation failed", {"errors": errors})
    
    return tender_data


def validate_bid_files(files: List[UploadFile]) -> List[UploadFile]:
    """Validate uploaded bid files.
    
    Args:
        files: List of uploaded files
        
    Returns:
        Validated files
        
    Raises:
        ValidationException: If validation fails
    """
    errors = []
    
    if not files:
        errors.append("At least one file is required")
        
    if len(files) > 20:  # Reasonable limit
        errors.append("Maximum 20 files allowed per upload")
    
    for i, file in enumerate(files):
        file_errors = []
        
        # Check filename
        if not file.filename:
            file_errors.append("Filename is required")
        else:
            # Check file extension
            if not file.filename.lower().endswith('.pdf'):
                file_errors.append("Only PDF files are allowed")
            
            # Check filename length
            if len(file.filename) > 255:
                file_errors.append("Filename too long (max 255 characters)")
            
            # Check for valid characters in filename
            if not re.match(r'^[a-zA-Z0-9\u4e00-\u9fff._\-\s()]+\.pdf$', file.filename, re.IGNORECASE):
                file_errors.append("Filename contains invalid characters")
        
        # Check content type
        if file.content_type and not file.content_type.startswith('application/pdf'):
            file_errors.append("Invalid file type. Only PDF files are allowed")
        
        if file_errors:
            errors.extend([f"File {i+1} ({file.filename}): {error}" for error in file_errors])
    
    if errors:
        raise ValidationException("File validation failed", {"errors": errors})
    
    return files


def validate_bid_metadata(companies: List[str], prices: List[float], file_count: int) -> tuple:
    """Validate bid metadata (companies and prices).
    
    Args:
        companies: List of company names
        prices: List of bid prices
        file_count: Number of files uploaded
        
    Returns:
        Tuple of (validated_companies, validated_prices)
        
    Raises:
        ValidationException: If validation fails
    """
    errors = []
    
    # Check list lengths
    if len(companies) != file_count:
        errors.append(f"Number of company names ({len(companies)}) must match number of files ({file_count})")
    
    if len(prices) != file_count:
        errors.append(f"Number of prices ({len(prices)}) must match number of files ({file_count})")
    
    # Validate company names
    validated_companies = []
    for i, company in enumerate(companies):
        if not company or not company.strip():
            errors.append(f"Company name {i+1} is required")
        else:
            company_name = company.strip()
            if len(company_name) < 2:
                errors.append(f"Company name {i+1} must be at least 2 characters long")
            if len(company_name) > 100:
                errors.append(f"Company name {i+1} must be less than 100 characters")
            validated_companies.append(company_name)
    
    # Validate prices
    validated_prices = []
    for i, price in enumerate(prices):
        try:
            price_value = float(price)
            if price_value <= 0:
                errors.append(f"Price {i+1} must be greater than 0")
            if price_value > 1e12:  # 1 trillion limit
                errors.append(f"Price {i+1} exceeds maximum allowed value")
            validated_prices.append(price_value)
        except (ValueError, TypeError):
            errors.append(f"Price {i+1} must be a valid number")
    
    if errors:
        raise ValidationException("Bid metadata validation failed", {"errors": errors})
    
    return validated_companies, validated_prices


def validate_analysis_request(tender_id: str) -> str:
    """Validate analysis request.
    
    Args:
        tender_id: ID of the tender to analyze
        
    Returns:
        Validated tender ID
        
    Raises:
        ValidationException: If validation fails
    """
    if not tender_id or not tender_id.strip():
        raise ValidationException("Tender ID is required")
    
    tender_id = tender_id.strip()
    
    # Allow both UUID format and demo tender format (tender-YYYY-NNN)
    # uuid_pattern = r'^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$'
    # demo_pattern = r'^tender-\d{4}-\d{3}$'
    
    # if not (re.match(uuid_pattern, tender_id, re.IGNORECASE) or re.match(demo_pattern, tender_id)):
    #     raise ValidationException("Invalid tender ID format (must be UUID or demo format: tender-YYYY-NNN)")
    
    return tender_id


def validate_pagination_params(skip: int, limit: int) -> tuple:
    """Validate pagination parameters.
    
    Args:
        skip: Number of records to skip
        limit: Maximum number of records to return
        
    Returns:
        Tuple of (validated_skip, validated_limit)
        
    Raises:
        ValidationException: If validation fails
    """
    errors = []
    
    if skip < 0:
        errors.append("Skip parameter must be non-negative")
    
    if limit <= 0:
        errors.append("Limit parameter must be greater than 0")
    
    if limit > 100:
        errors.append("Limit parameter must not exceed 100")
    
    if errors:
        raise ValidationException("Pagination validation failed", {"errors": errors})
    
    return skip, limit


def sanitize_filename(filename: str) -> str:
    """Sanitize filename for safe storage.
    
    Args:
        filename: Original filename
        
    Returns:
        Sanitized filename
    """
    if not filename:
        return "unnamed.pdf"
    
    # Remove path components
    filename = filename.split('/')[-1].split('\\')[-1]
    
    # Replace problematic characters
    sanitized = re.sub(r'[<>:"/\\|?*]', '_', filename)
    
    # Ensure it ends with .pdf
    if not sanitized.lower().endswith('.pdf'):
        sanitized += '.pdf'
    
    # Limit length
    if len(sanitized) > 255:
        name_part = sanitized[:-4]  # Remove .pdf
        sanitized = name_part[:251] + '.pdf'  # Keep .pdf extension
    
    return sanitized