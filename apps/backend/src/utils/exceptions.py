from fastapi import HTT<PERSON>Exception
from typing import Optional, Dict, Any


class TraceFastException(Exception):
    """Base exception for TraceFast application."""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        self.message = message
        self.details = details or {}
        super().__init__(self.message)


class ValidationException(TraceFastException):
    """Exception raised for validation errors."""
    pass


class NotFoundException(TraceFastException):
    """Exception raised when a resource is not found."""
    pass


class ProcessingException(TraceFastException):
    """Exception raised during document processing."""
    pass


class StorageException(TraceFastException):
    """Exception raised during file storage operations."""
    pass


class AnalysisException(TraceFastException):
    """Exception raised during analysis operations."""
    pass


# HTTP Exception handlers
def validation_exception_handler(message: str, details: Optional[Dict] = None) -> HTTPException:
    """Convert validation exception to HTTP 400."""
    return HTTPException(
        status_code=400,
        detail={
            "message": message,
            "error_code": "VALIDATION_ERROR",
            "details": details or {}
        }
    )


def not_found_exception_handler(message: str, resource_type: str = "Resource") -> HTTPException:
    """Convert not found exception to HTTP 404."""
    return HTTPException(
        status_code=404,
        detail={
            "message": message,
            "error_code": "NOT_FOUND",
            "resource_type": resource_type
        }
    )


def processing_exception_handler(message: str, details: Optional[Dict] = None) -> HTTPException:
    """Convert processing exception to HTTP 500."""
    return HTTPException(
        status_code=500,
        detail={
            "message": message,
            "error_code": "PROCESSING_ERROR",
            "details": details or {}
        }
    )


def storage_exception_handler(message: str, details: Optional[Dict] = None) -> HTTPException:
    """Convert storage exception to HTTP 500."""
    return HTTPException(
        status_code=500,
        detail={
            "message": message,
            "error_code": "STORAGE_ERROR",
            "details": details or {}
        }
    )