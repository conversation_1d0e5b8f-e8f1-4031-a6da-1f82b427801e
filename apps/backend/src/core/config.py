from pydantic_settings import BaseSettings, SettingsConfigDict
import os
from pathlib import Path


class Settings(BaseSettings):
    # Basic settings
    env: str = "development"
    api_prefix: str = "/api/v1"
    frontend_origin: str | None = "http://localhost:3000"
    port: int = 8000

    # Database
    database_url: str = "sqlite:///./tracefast.db"

    # File Storage
    storage_path: str = "./storage"
    max_file_size: int = 50 * 1024 * 1024  # 50MB
    allowed_extensions: list[str] = [".pdf"]

    # Analysis
    max_concurrent_analysis: int = 5
    analysis_timeout: int = 300  # 5 minutes

    # OCR & LLM (placeholders)
    ocr_model_path: str = ""
    embedding_model_path: str = ""

    model_config = SettingsConfigDict(
        env_file=".env",
        env_prefix="",
        extra="ignore",
    )

    def __post_init__(self):
        """Ensure storage directory exists."""
        Path(self.storage_path).mkdir(parents=True, exist_ok=True)


settings = Settings()


