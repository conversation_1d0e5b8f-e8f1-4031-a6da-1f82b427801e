from fastapi import APIR<PERSON><PERSON>, Depends, HTTPException, BackgroundTasks, Form
from sqlalchemy.orm import Session
from typing import Optional

from ...core.database import get_db
from ...models.database import (
    Tender, SuspiciousSegment, Project, Document, AnalysisResult, DocumentChunk
)
from ...models.schemas import AnalysisRequest, AnalysisStatusResponse, AnalysisResults, AIInsight, Position
from ...services.analysis_service import AnalysisService
from ...services.comparison.semantic import SemanticComparison
from ...services.embedding_service import embedding_service
from ...utils.validators import validate_analysis_request
from ...utils.exceptions import ValidationException, not_found_exception_handler, validation_exception_handler
import logging

logger = logging.getLogger(__name__)

router = APIRouter()
analysis_service = AnalysisService()


@router.post("/start/{tender_id}")
async def start_analysis(
    tender_id: str,
    db: Session = Depends(get_db)
):
    """Start comprehensive analysis for a tender."""
    try:
        # Validate tender ID
        validated_tender_id = validate_analysis_request(tender_id)
        
        # Start analysis (synchronous for MVP)
        result = analysis_service.start_analysis(db, validated_tender_id)
        
        return {
            "message": "Analysis completed successfully",
            "tender_id": validated_tender_id,
            "status": result["status"],
            "analysis_count": result["analysis_count"],
            "results_summary": {
                "total_comparisons": result["analysis_count"],
                "processing_time": "completed",
                "note": "Analysis completed synchronously for MVP demo"
            }
        }
        
    except ValidationException as e:
        raise validation_exception_handler(e.message, e.details)
    except ValueError as e:
        raise not_found_exception_handler(str(e), "Tender")
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail={
                "message": f"Analysis failed: {str(e)}",
                "error_code": "ANALYSIS_ERROR"
            }
        )


@router.get("/status/{tender_id}", response_model=AnalysisStatusResponse)
async def get_analysis_status(
    tender_id: str,
    db: Session = Depends(get_db)
):
    """Get current analysis status for a tender."""
    try:
        # Validate tender ID
        validated_tender_id = validate_analysis_request(tender_id)
        
        # Get status
        status = analysis_service.get_analysis_status(db, validated_tender_id)
        
        if status["status"] == "not_found":
            raise not_found_exception_handler(
                f"Tender with ID {tender_id} not found",
                "Tender"
            )
        
        return status
        
    except ValidationException as e:
        raise validation_exception_handler(e.message, e.details)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail={
                "message": f"Failed to get analysis status: {str(e)}",
                "error_code": "STATUS_ERROR"
            }
        )


@router.get("/results/{tender_id}", response_model=AnalysisResults)
async def get_analysis_results(
    tender_id: str,
    db: Session = Depends(get_db)
):
    """Get comprehensive analysis results for a tender."""
    try:
        # Validate tender ID
        validated_tender_id = validate_analysis_request(tender_id)
        
        # Get results
        results = analysis_service.get_analysis_results(db, validated_tender_id)
        
        return results
        
    except ValidationException as e:
        raise validation_exception_handler(e.message, e.details)
    except ValueError as e:
        raise not_found_exception_handler(str(e), "Tender")
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail={
                "message": f"Failed to get analysis results: {str(e)}",
                "error_code": "RESULTS_ERROR"
            }
        )


@router.get("/results/{tender_id}/summary")
async def get_analysis_summary(
    tender_id: str,
    db: Session = Depends(get_db)
):
    """Get a summary of analysis results for a tender."""
    try:
        # Validate tender ID
        validated_tender_id = validate_analysis_request(tender_id)
        
        # Get full results
        results = analysis_service.get_analysis_results(db, validated_tender_id)
        
        # Return just the summary
        return {
            "tender_id": validated_tender_id,
            "status": results["status"],
            "summary": results["summary"],
            "total_results": len(results["results"]),
            "high_risk_findings": len([
                r for r in results["results"] 
                if r["risk_level"] in ["high", "critical"]
            ])
        }
        
    except ValidationException as e:
        raise validation_exception_handler(e.message, e.details)
    except ValueError as e:
        raise not_found_exception_handler(str(e), "Tender")
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail={
                "message": f"Failed to get analysis summary: {str(e)}",
                "error_code": "SUMMARY_ERROR"
            }
        )


@router.get("/results/{tender_id}/segments")
async def get_suspicious_segments(
    tender_id: str,
    category: str = None,
    risk_level: str = None,
    db: Session = Depends(get_db)
):
    """Get suspicious segments with optional filtering."""
    try:
        # Validate tender ID
        validated_tender_id = validate_analysis_request(tender_id)
        
        # Check if tender exists first
        tender = db.query(Tender).filter(Tender.id == validated_tender_id).first()
        if not tender:
            raise not_found_exception_handler(
                f"Tender with ID {tender_id} not found",
                "Tender"
            )
        
        # Get full results - handle case where no analysis results exist yet
        try:
            results = analysis_service.get_analysis_results(db, validated_tender_id)
        except ValueError as e:
            # Tender not found - already handled above
            raise not_found_exception_handler(str(e), "Tender")
        
        # Collect all segments (handle empty results gracefully)
        all_segments = []
        if results and "results" in results:
            for result in results["results"]:
                for segment in result["suspicious_segments"]:
                    segment["analysis_result_id"] = result["id"]
                    segment["bid_pair"] = result["bid_pair"]
                    all_segments.append(segment)
        
        # Apply filters
        filtered_segments = all_segments
        
        if category:
            filtered_segments = [
                s for s in filtered_segments 
                if s["category"].lower() == category.lower()
            ]
        
        if risk_level:
            filtered_segments = [
                s for s in filtered_segments 
                if s["suspicion_level"].lower() == risk_level.lower()
            ]
        
        return {
            "tender_id": validated_tender_id,
            "total_segments": len(all_segments),
            "filtered_segments": len(filtered_segments),
            "filters": {
                "category": category,
                "risk_level": risk_level
            },
            "segments": filtered_segments
        }
        
    except ValidationException as e:
        raise validation_exception_handler(e.message, e.details)
    except ValueError as e:
        raise not_found_exception_handler(str(e), "Tender")
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail={
                "message": f"Failed to get suspicious segments: {str(e)}",
                "error_code": "SEGMENTS_ERROR"
            }
        )


@router.post("/insights/{tender_id}")
async def generate_ai_insights(
    tender_id: str,
    segment_ids: list[str],
    db: Session = Depends(get_db)
):
    """Generate AI insights for a group of segments."""
    try:
        # Validate tender ID
        validated_tender_id = validate_analysis_request(tender_id)
        
        # This is a placeholder for AI insight generation
        # In the real implementation, this would use an LLM to analyze the segments
        
        if not segment_ids:
            raise ValidationException("At least one segment ID is required")
        
        # Mock AI insight generation
        insight = {
            "group_id": f"group-{len(segment_ids)}-segments",
            "segment_count": len(segment_ids),
            "explanation": f"Analysis of {len(segment_ids)} suspicious segments reveals potential bid collusion patterns. The segments show high similarity in writing style, document structure, and numerical values, suggesting possible coordination between bidders.",
            "evidence_strength": 0.87,
            "recommended_action": "Further investigation recommended. Consider examining the relationship between the companies involved and requesting additional documentation.",
            "key_findings": [
                "High semantic similarity between bid documents",
                "Identical or near-identical pricing structures",
                "Similar document formatting and layout",
                "Consistent writing patterns suggesting common authorship"
            ],
            "risk_assessment": "HIGH",
            "confidence_level": 0.85
        }
        
        return {
            "tender_id": validated_tender_id,
            "insight": insight,
            "note": "This is a placeholder implementation. Actual AI insights will be generated using open-source LLM models."
        }
        
    except ValidationException as e:
        raise validation_exception_handler(e.message, e.details)
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail={
                "message": f"Failed to generate AI insights: {str(e)}",
                "error_code": "INSIGHTS_ERROR"
            }
        )


@router.put("/segments/{segment_id}/position")
async def update_segment_position(
    segment_id: str,
    position: Position,
    db: Session = Depends(get_db)
):
    """Update the position of a suspicious segment on the whiteboard."""
    try:
        # Find the segment
        segment = db.query(SuspiciousSegment).filter(SuspiciousSegment.id == segment_id).first()
        if not segment:
            raise HTTPException(
                status_code=404,
                detail={
                    "message": f"Segment with ID {segment_id} not found",
                    "error_code": "SEGMENT_NOT_FOUND"
                }
            )
        
        # Update position
        segment.position_x = position.x
        segment.position_y = position.y
        db.commit()
        
        return {
            "message": "Position updated successfully",
            "segment_id": segment_id,
            "position": {
                "x": position.x,
                "y": position.y
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=500,
            detail={
                "message": f"Failed to update segment position: {str(e)}",
                "error_code": "POSITION_UPDATE_ERROR"
            }
        )


@router.get("/health")
async def analysis_health_check():
    """Check if analysis services are available."""
    try:
        # Check if all analysis components are available
        health_status = {
            "ocr_service": analysis_service.ocr_service.is_available(),
            "semantic_analyzer": analysis_service.semantic_analyzer.is_available(),
            "stylometry_analyzer": True,  # Always available
            "structural_analyzer": True,  # Always available
            "numerical_analyzer": True,  # Always available
            "overall_status": "healthy"
        }
        
        # If any service is not available, mark as degraded
        if not all(health_status[service] for service in health_status if service != "overall_status"):
            health_status["overall_status"] = "degraded"
        
        return {
            "status": health_status["overall_status"],
            "services": health_status,
            "message": "Analysis services status check completed",
            "timestamp": "2025-01-01T00:00:00Z"  # This would be current time in real implementation
        }
        
    except Exception as e:
        return {
            "status": "unhealthy",
            "services": {},
            "message": f"Health check failed: {str(e)}",
            "timestamp": "2025-01-01T00:00:00Z"
        }


@router.post("/semantic")
async def analyze_semantic_similarity(
    document1_id: str = Form(...),
    document2_id: str = Form(...),
    threshold: float = Form(0.8),
    db: Session = Depends(get_db)
):
    """Analyze semantic similarity between two documents."""
    try:
        # Verify documents exist
        doc1 = db.query(Document).filter(Document.id == document1_id).first()
        doc2 = db.query(Document).filter(Document.id == document2_id).first()
        
        if not doc1 or not doc2:
            raise HTTPException(status_code=404, detail="One or both documents not found")
        
        # Check if documents are processed
        if doc1.status != "processed" or doc2.status != "processed":
            raise HTTPException(
                status_code=400, 
                detail="Documents must be processed before analysis"
            )
        
        # Initialize semantic comparison service
        semantic_service = SemanticComparison()
        
        # Perform comparison
        similar_chunks = semantic_service.compare_document_chunks(
            document1_id, document2_id, db, threshold
        )
        
        # Calculate overall semantic similarity
        if similar_chunks:
            avg_similarity = sum(chunk["similarity_score"] for chunk in similar_chunks) / len(similar_chunks)
            max_similarity = max(chunk["similarity_score"] for chunk in similar_chunks)
        else:
            avg_similarity = 0.0
            max_similarity = 0.0
        
        return {
            "document1_id": document1_id,
            "document2_id": document2_id,
            "document1_name": doc1.original_filename,
            "document2_name": doc2.original_filename,
            "company1_name": doc1.company_name,
            "company2_name": doc2.company_name,
            "analysis_type": "semantic_similarity",
            "threshold_used": threshold,
            "similar_chunks_found": len(similar_chunks),
            "overall_similarity": {
                "average_score": avg_similarity,
                "max_score": max_similarity,
                "risk_assessment": "high" if max_similarity >= 0.9 else "medium" if max_similarity >= 0.7 else "low"
            },
            "similar_chunks": similar_chunks[:20]  # Limit to top 20 results
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Semantic analysis failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Analysis failed: {str(e)}")


@router.post("/compare-chunks")
async def compare_specific_chunks(
    chunk1_id: str = Form(...),
    chunk2_id: str = Form(...),
    db: Session = Depends(get_db)
):
    """Compare two specific chunks for detailed analysis."""
    try:
        # Get chunks
        chunk1 = db.query(DocumentChunk).filter(DocumentChunk.id == chunk1_id).first()
        chunk2 = db.query(DocumentChunk).filter(DocumentChunk.id == chunk2_id).first()
        
        if not chunk1 or not chunk2:
            raise HTTPException(status_code=404, detail="One or both chunks not found")
        
        # Get document information
        doc1 = db.query(Document).filter(Document.id == chunk1.document_id).first()
        doc2 = db.query(Document).filter(Document.id == chunk2.document_id).first()
        
        # Initialize semantic comparison
        semantic_service = SemanticComparison()
        
        # Compare chunks
        similarity_score = semantic_service.compare_texts(chunk1.content, chunk2.content)
        
        return {
            "chunk1_id": chunk1_id,
            "chunk2_id": chunk2_id,
            "chunk1_info": {
                "document_id": str(chunk1.document_id),
                "document_name": doc1.original_filename if doc1 else "Unknown",
                "company_name": doc1.company_name if doc1 else "Unknown",
                "page_number": chunk1.page_number,
                "chunk_type": chunk1.chunk_type.value,
                "content_preview": chunk1.content[:200] + "..." if len(chunk1.content) > 200 else chunk1.content
            },
            "chunk2_info": {
                "document_id": str(chunk2.document_id),
                "document_name": doc2.original_filename if doc2 else "Unknown",
                "company_name": doc2.company_name if doc2 else "Unknown",
                "page_number": chunk2.page_number,
                "chunk_type": chunk2.chunk_type.value,
                "content_preview": chunk2.content[:200] + "..." if len(chunk2.content) > 200 else chunk2.content
            },
            "comparison_result": {
                "similarity_score": similarity_score,
                "risk_level": "critical" if similarity_score >= 0.95 else "high" if similarity_score >= 0.85 else "medium" if similarity_score >= 0.7 else "low",
                "analysis_type": "semantic_similarity"
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Chunk comparison failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Comparison failed: {str(e)}")


@router.get("/service/status")
async def get_analysis_service_status():
    """Get status of analysis services."""
    try:
        semantic_service = SemanticComparison()
        
        return {
            "semantic_analysis": {
                "available": semantic_service.is_available(),
                "model_info": semantic_service.get_model_info()
            },
            "embedding_service": {
                "available": embedding_service.is_available(),
                "model_info": embedding_service.get_model_info()
            },
            "legacy_analysis": {
                "available": analysis_service.ocr_service.is_available(),
                "model_info": analysis_service.ocr_service.get_model_info()
            },
            "system_status": "operational" if semantic_service.is_available() else "degraded"
        }
        
    except Exception as e:
        logger.error(f"Service status check failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Service status check failed: {str(e)}")


@router.get("/models/info")
async def get_model_info():
    """Get information about available analysis models."""
    try:
        semantic_service = SemanticComparison()
        
        model_info = {
            "ocr_model": analysis_service.ocr_service.get_model_info(),
            "semantic_model": semantic_service.get_model_info(),
            "embedding_model": embedding_service.get_model_info(),
            "analysis_engines": {
                "stylometry": "Built-in statistical analysis",
                "structural": "Layout-based comparison using OCR data", 
                "numerical": "Pattern-based numerical extraction and comparison",
                "semantic": "Qwen3-Embedding-0.6B for semantic similarity"
            },
            "privacy_status": "All models run locally - no external API calls",
            "ready_for_production": semantic_service.is_available() and embedding_service.is_available(),
            "integration_notes": "Production-ready ML models integrated with deepdoctection OCR and Qwen3 embeddings"
        }
        
        return model_info
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail={
                "message": f"Failed to get model information: {str(e)}",
                "error_code": "MODEL_INFO_ERROR"
            }
        )