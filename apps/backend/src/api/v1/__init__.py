from fastapi import APIRouter
from .tenders import router as tenders_router
from .bids import router as bids_router
from .analysis import router as analysis_router
from .projects import router as projects_router
from .documents import router as documents_router

router = APIRouter()

# Include all route modules
router.include_router(tenders_router, prefix="/tenders", tags=["tenders"])
router.include_router(bids_router, prefix="/bids", tags=["bids"])
router.include_router(analysis_router, prefix="/analysis", tags=["analysis"])
router.include_router(projects_router, prefix="/projects", tags=["projects"])
router.include_router(documents_router, prefix="/documents", tags=["documents"])

__all__ = ["router"]