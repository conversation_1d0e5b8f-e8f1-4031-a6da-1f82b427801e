from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks, Form
from sqlalchemy.orm import Session
from sqlalchemy import desc
from typing import List, Dict, Any
from uuid import UUID
import json

from ...core.database import get_db
from ...models.database import Project, Document, AnalysisResult, Tender, Bid, DocumentChunk, ProjectStatus
from ...models.schemas import ProjectResponse, ProjectCreate, HeatmapDataResponse
from ...services.document_service import document_processing_service
import logging

logger = logging.getLogger(__name__)

router = APIRouter()


@router.post("/create", response_model=Dict[str, Any])
def create_project(
    name: str = Form(...),
    tender_id: str = Form(None),  # Optional tender ID for backward compatibility
    db: Session = Depends(get_db)
):
    """Create a new project."""
    try:
        import uuid
        
        logger.info(f"Creating project with name: {name}, tender_id: {tender_id}")
        
        # Use tender_id as project_id if provided and valid UUID, otherwise generate new one
        if tender_id:
            try:
                project_id = uuid.UUID(tender_id)
                logger.info(f"Using tender_id as project_id: {project_id}")
            except ValueError:
                logger.warning(f"Invalid UUID format for tender_id: {tender_id}, generating new project_id")
                project_id = uuid.uuid4()
        else:
            project_id = uuid.uuid4()
            
        project = Project(
            id=project_id,
            name=name,
            status=ProjectStatus.UPLOADING
        )
        
        db.add(project)
        db.commit()
        db.refresh(project)
        
        logger.info(f"Successfully created project: {project.id}")
        
        return {
            "project_id": str(project.id),
            "name": project.name,
            "status": project.status.value,
            "tender_id": tender_id,  # Include for frontend compatibility
            "created_at": project.created_at.isoformat(),
            "message": "Project created successfully"
        }
        
    except Exception as e:
        logger.error(f"Failed to create project: {str(e)}")
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Failed to create project: {str(e)}")


@router.get("/{project_id}/exists")
def check_project_exists(
    project_id: str,
    db: Session = Depends(get_db)
):
    """Check if a project exists."""
    try:
        import uuid as uuid_lib
        
        # Validate UUID format
        try:
            project_uuid = uuid_lib.UUID(project_id)
        except (ValueError, TypeError):
            return {"exists": False, "reason": "Invalid UUID format"}
        
        # Check if project exists
        project = db.query(Project).filter(Project.id == project_uuid).first()
        
        return {
            "exists": project is not None,
            "project_id": project_id,
            "project_name": project.name if project else None
        }
        
    except Exception as e:
        logger.error(f"Failed to check project existence: {str(e)}")
        return {"exists": False, "reason": str(e)}


@router.get("/list")
def list_projects(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db)
):
    """List all projects."""
    try:
        projects = db.query(Project).offset(skip).limit(limit).all()
        
        return {
            "projects": [
                {
                    "project_id": str(p.id),
                    "name": p.name,
                    "status": p.status.value,
                    "created_at": p.created_at.isoformat(),
                    "updated_at": p.updated_at.isoformat()
                }
                for p in projects
            ],
            "total": db.query(Project).count()
        }
        
    except Exception as e:
        logger.error(f"Failed to list projects: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to list projects: {str(e)}")

@router.get("/{project_id}/heatmap", response_model=HeatmapDataResponse)
def get_project_heatmap(
    project_id: str,
    db: Session = Depends(get_db)
):
    """Get heatmap data for project dashboard."""
    # Try to find in new Projects table first
    try:
        import uuid as uuid_lib
        project_uuid = uuid_lib.UUID(project_id)
        project = db.query(Project).filter(Project.id == project_uuid).first()
    except ValueError:
        project = None
    
    if project:
        # New schema approach
        documents = db.query(Document).filter(Document.project_id == project_uuid).all()
        analysis_results = db.query(AnalysisResult).filter(
            AnalysisResult.project_id == project_uuid
        ).all()
        
        # Build heatmap data structure
        heatmap_data = []
        if analysis_results:
            # Use actual analysis results
            for result in analysis_results:
                doc1 = next((d for d in documents if d.id == result.document_1_id), None)
                doc2 = next((d for d in documents if d.id == result.document_2_id), None)
                
                if doc1 and doc2:
                    overall_scores = result.overall_scores
                    heatmap_data.append({
                        "document1_id": str(result.document_1_id),
                        "document2_id": str(result.document_2_id),
                        "company1_name": doc1.company_name,
                        "company2_name": doc2.company_name,
                        "overall_risk": overall_scores.get("overall_risk", 0),
                        "semantic_similarity": overall_scores.get("text_semantic_similarity", 0),
                        "layout_similarity": overall_scores.get("layout_similarity", 0),
                        "pricing_similarity": overall_scores.get("pricing_model_similarity", 0),
                        "stylometry_similarity": overall_scores.get("stylometry_similarity", 0)
                    })
        elif len(documents) >= 2:
            # Generate mock similarity data for document pairs
            import hashlib
            for i, doc1 in enumerate(documents):
                for doc2 in documents[i+1:]:
                    # Generate consistent mock similarity scores
                    pair_key = f"{doc1.id}{doc2.id}"
                    hash_val = int(hashlib.md5(pair_key.encode()).hexdigest()[:8], 16)
                    
                    base_risk = 0.3 + (hash_val % 100) / 200.0  # 0.3 to 0.8
                    semantic_sim = 0.4 + (hash_val % 120) / 300.0  # 0.4 to 0.8
                    layout_sim = 0.2 + (hash_val % 160) / 400.0  # 0.2 to 0.6
                    pricing_sim = 0.1 + (hash_val % 180) / 200.0  # 0.1 to 1.0
                    stylometry_sim = 0.3 + (hash_val % 140) / 350.0  # 0.3 to 0.7
                    
                    heatmap_data.append({
                        "document1_id": str(doc1.id),
                        "document2_id": str(doc2.id),
                        "company1_name": doc1.company_name or "Unknown",
                        "company2_name": doc2.company_name or "Unknown",
                        "overall_risk": min(1.0, base_risk),
                        "semantic_similarity": min(1.0, semantic_sim),
                        "layout_similarity": min(1.0, layout_sim),
                        "pricing_similarity": min(1.0, pricing_sim),
                        "stylometry_similarity": min(1.0, stylometry_sim)
                    })
        
        return HeatmapDataResponse(
            project_id=str(project.id),
            project_name=project.name,
            heatmap_data=heatmap_data
        )
    
    else:
        # Fallback to legacy Tender table
        tender = db.query(Tender).filter(Tender.id == project_id).first()
        if not tender:
            raise HTTPException(status_code=404, detail="Project not found")
        
        bids = db.query(Bid).filter(Bid.tender_id == project_id).all()
        
        # Generate mock heatmap data for legacy system
        heatmap_data = []
        for i, bid1 in enumerate(bids):
            for bid2 in bids[i+1:]:
                # Calculate mock similarity based on price difference
                price_diff = abs(bid1.total_price - bid2.total_price) / max(bid1.total_price, bid2.total_price)
                overall_risk = max(0.1, 0.9 - price_diff * 2)
                
                heatmap_data.append({
                    "document1_id": bid1.id,
                    "document2_id": bid2.id,
                    "company1_name": bid1.company_name,
                    "company2_name": bid2.company_name,
                    "overall_risk": min(1.0, max(0.0, overall_risk + (hash(bid1.id + bid2.id) % 100) / 500)),
                    "semantic_similarity": min(1.0, max(0.0, overall_risk + (hash(bid1.id + bid2.id + "sem") % 100) / 500)),
                    "layout_similarity": min(1.0, max(0.0, overall_risk + (hash(bid1.id + bid2.id + "lay") % 100) / 500)),
                    "pricing_similarity": min(1.0, max(0.0, 0.9 - price_diff)),
                    "stylometry_similarity": min(1.0, max(0.0, overall_risk + (hash(bid1.id + bid2.id + "sty") % 100) / 500))
                })
        
        return HeatmapDataResponse(
            project_id=tender.id,
            project_name=tender.title,
            heatmap_data=heatmap_data
        )


@router.get("/{project_id}/statistics")
def get_project_statistics(
    project_id: str,
    db: Session = Depends(get_db)
):
    """Get statistics for project dashboard."""
    # Try to find in new Projects table first
    try:
        import uuid as uuid_lib
        project_uuid = uuid_lib.UUID(project_id)
        project = db.query(Project).filter(Project.id == project_uuid).first()
    except ValueError:
        project = None
    
    if project:
        # New schema approach
        documents = db.query(Document).filter(Document.project_id == project_uuid).all()
        analysis_results = db.query(AnalysisResult).filter(
            AnalysisResult.project_id == project_uuid
        ).all()
        
        # Calculate statistics from analysis results
        total_comparisons = len(analysis_results)
        critical_findings = sum(1 for r in analysis_results 
                               if r.overall_scores.get("overall_risk", 0) >= 0.8)
        high_findings = sum(1 for r in analysis_results 
                           if 0.6 <= r.overall_scores.get("overall_risk", 0) < 0.8)
        medium_findings = sum(1 for r in analysis_results 
                             if 0.4 <= r.overall_scores.get("overall_risk", 0) < 0.6)
        
        return {
            "project_id": str(project.id),
            "total_documents": len(documents),
            "total_comparisons": total_comparisons,
            "suspicious_findings": critical_findings + high_findings,
            "critical_findings": critical_findings,
            "high_findings": high_findings,
            "medium_findings": medium_findings,
            "companies_involved": len(set(d.company_name for d in documents if d.company_name)),
            "analysis_progress": 100 if project.status == "completed" else 75 if project.status == "analyzing" else 0
        }
    
    else:
        # Fallback to legacy Tender table
        tender = db.query(Tender).filter(Tender.id == project_id).first()
        if not tender:
            raise HTTPException(status_code=404, detail="Project not found")
        
        bids = db.query(Bid).filter(Bid.tender_id == project_id).all()
        total_comparisons = len(bids) * (len(bids) - 1) // 2
        
        return {
            "project_id": tender.id,
            "total_documents": len(bids),
            "total_comparisons": total_comparisons,
            "suspicious_findings": max(1, total_comparisons // 3),  # Mock data
            "critical_findings": max(1, total_comparisons // 10),   # Mock data
            "high_findings": max(1, total_comparisons // 5),        # Mock data
            "medium_findings": max(1, total_comparisons // 4),      # Mock data
            "companies_involved": len(bids),
            "analysis_progress": 100 if tender.analysis_status == "completed" else 75 if tender.analysis_status == "analyzing" else 0
        }


@router.get("/{document1_id}/{document2_id}/findings")
def get_comparison_findings(
    document1_id: str,
    document2_id: str,
    db: Session = Depends(get_db)
):
    """Get detailed findings for document pair comparison."""
    # Try to find analysis result in new schema
    analysis_result = db.query(AnalysisResult).filter(
        ((AnalysisResult.document_1_id == document1_id) & (AnalysisResult.document_2_id == document2_id)) |
        ((AnalysisResult.document_1_id == document2_id) & (AnalysisResult.document_2_id == document1_id))
    ).first()
    
    if analysis_result and analysis_result.detailed_findings:
        return {
            "document1_id": document1_id,
            "document2_id": document2_id,
            "overall_scores": analysis_result.overall_scores,
            "detailed_findings": analysis_result.detailed_findings
        }
    
    # Fallback to generating mock findings for legacy system
    return {
        "document1_id": document1_id,
        "document2_id": document2_id,
        "overall_scores": {
            "overall_risk": 0.85,
            "text_semantic_similarity": 0.92,
            "layout_similarity": 0.78,
            "pricing_model_similarity": 0.67
        },
        "detailed_findings": [
            {
                "finding_type": "text_semantic_similarity",
                "risk_level": "high",
                "summary": "High similarity found in Construction Safety Plan section with identical technical specifications.",
                "evidence": {
                    "chunk_id_1": f"chunk-1-{document1_id}",
                    "chunk_id_2": f"chunk-1-{document2_id}",
                    "score": 0.92,
                    "highlight_details": {
                        "text_1": "All scaffold nodes must use M16 fixing screws with galvanized coating. Safety barriers shall be installed at 2-meter intervals...",
                        "text_2": "All scaffold nodes must use M16 fixing screws with galvanized coating. Safety barriers shall be installed at 2-meter intervals..."
                    }
                }
            },
            {
                "finding_type": "layout_similarity",
                "risk_level": "medium",
                "summary": "Similar document structure and formatting patterns detected.",
                "evidence": {
                    "chunk_id_1": f"chunk-2-{document1_id}",
                    "chunk_id_2": f"chunk-2-{document2_id}",
                    "score": 0.78,
                    "highlight_details": {
                        "text_1": "Technical Specifications\n\n1. Foundation Requirements\n2. Material Standards\n3. Quality Control",
                        "text_2": "Technical Specifications\n\n1. Foundation Requirements\n2. Material Standards\n3. Quality Control"
                    }
                }
            }
        ]
    }


@router.post("/reports/generate")
def generate_report(
    project_id: str,
    selected_findings: List[str],
    db: Session = Depends(get_db)
):
    """Generate AI-powered audit report."""
    # This would integrate with an AI service to generate reports
    # For now, return a success response
    return {
        "report_id": f"report-{project_id}-{len(selected_findings)}",
        "status": "generated",
        "findings_count": len(selected_findings),
        "generated_at": "2025-01-01T00:00:00Z"
    }


@router.post("/{project_id}/process")
def process_project_documents(
    project_id: str,
    background_tasks: BackgroundTasks,
    document_ids: List[str] = None,
    db: Session = Depends(get_db)
):
    """Trigger OCR processing for all documents in a project."""
    try:
        # Check if project exists
        project = db.query(Project).filter(Project.id == project_id).first()
        if not project:
            raise HTTPException(status_code=404, detail="Project not found")
        
        # Get documents to process
        if document_ids:
            documents = db.query(Document).filter(
                Document.project_id == project_id,
                Document.id.in_(document_ids)
            ).all()
        else:
            documents = db.query(Document).filter(
                Document.project_id == project_id
            ).all()
        
        if not documents:
            return {
                "project_id": project_id,
                "status": "no_documents",
                "message": "No documents found to process"
            }
        
        # Queue background processing
        background_tasks.add_task(
            _process_project_background,
            project_id,
            [str(doc.id) for doc in documents]
        )
        
        return {
            "project_id": project_id,
            "status": "processing_queued",
            "documents_queued": len(documents),
            "message": "Project documents queued for processing"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Processing failed: {str(e)}")


def _process_project_background(project_id: str, document_ids: List[str]):
    """Background task for project processing."""
    try:
        from ...core.database import SessionLocal
        db = SessionLocal()
        try:
            result = document_processing_service.process_project_documents(
                project_id, db, document_ids
            )
            print(f"Project processing completed: {result['summary']}")
        finally:
            db.close()
    except Exception as e:
        print(f"Project processing failed: {str(e)}")


@router.get("/{project_id}/progress")
def get_project_progress(
    project_id: str,
    db: Session = Depends(get_db)
):
    """Get processing progress for a project."""
    try:
        progress = document_processing_service.get_project_processing_status(project_id, db)
        
        if progress.get("status") == "not_found":
            raise HTTPException(status_code=404, detail="Project not found")
        
        return progress
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Progress check failed: {str(e)}")


@router.get("/documents/{document_id}/chunks")
def get_document_chunks(
    document_id: str,
    db: Session = Depends(get_db)
):
    """Get document chunks for highlighting."""
    chunks = db.query(DocumentChunk).filter(
        DocumentChunk.document_id == document_id
    ).all()
    
    if chunks:
        return {
            "document_id": document_id,
            "chunks": [
                {
                    "id": str(chunk.id),
                    "page_number": chunk.page_number,
                    "chunk_type": chunk.chunk_type.value,
                    "content": chunk.content,
                    "bounding_box": chunk.bounding_box
                }
                for chunk in chunks
            ]
        }
    
    # Fallback for legacy system - return empty chunks
    return {
        "document_id": document_id,
        "chunks": []
    }