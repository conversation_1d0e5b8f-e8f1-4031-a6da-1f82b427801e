"""
Document API endpoints for TraceFast

Handles document upload, processing, and chunk retrieval operations.
"""

from fastapi import APIRouter, HTTPException, Depends, UploadFile, File, Form, BackgroundTasks
from sqlalchemy.orm import Session
from typing import List, Dict, Any, Optional
import os
import uuid
from pathlib import Path
import logging

from ...core.database import get_db
from ...models.database import Project, Document, DocumentChunk, DocumentStatus, ProjectStatus
from ...models.schemas import DocumentResponse, DocumentCreate
from ...services.document_service import document_processing_service
from ...storage.manager import file_storage

logger = logging.getLogger(__name__)
router = APIRouter()


@router.post("/upload", response_model=Dict[str, Any])
async def upload_document(
    background_tasks: BackgroundTasks,
    project_id: str = Form(...),
    company_name: str = Form(...),
    file: UploadFile = File(...),
    process_immediately: bool = Form(True),
    db: Session = Depends(get_db)
):
    """Upload a document and optionally process it immediately."""
    try:
        logger.info(f"Upload request for project_id: {project_id}")
        
        # Validate project exists (frontend should create it first)
        try:
            import uuid as uuid_lib
            project_uuid = uuid_lib.UUID(project_id)
            project = db.query(Project).filter(Project.id == project_uuid).first()
            if project:
                logger.info(f"Found existing project: {project.id}")
            else:
                logger.error(f"Project not found: {project_uuid}")
                raise HTTPException(status_code=404, detail=f"Project not found: {project_id}")
        except (ValueError, TypeError) as e:
            logger.error(f"Invalid UUID format for project_id: {project_id} (error: {e})")
            raise HTTPException(status_code=400, detail=f"Invalid project ID format: {project_id}")
        
        # Validate file type
        if not file.filename.lower().endswith('.pdf'):
            raise HTTPException(status_code=400, detail="Only PDF files are supported")
        
        # Generate unique document ID and storage path
        document_uuid = uuid.uuid4()
        document_id_str = str(document_uuid)
        storage_dir = Path(f"uploads/{project.id}")
        storage_dir.mkdir(parents=True, exist_ok=True)
        
        # Create unique filename to avoid conflicts
        file_extension = Path(file.filename).suffix
        safe_filename = f"{document_id_str}_{file.filename}"
        storage_path = storage_dir / safe_filename
        
        # Save uploaded file
        content = await file.read()
        with open(storage_path, "wb") as f:
            f.write(content)
        
        logger.info(f"File uploaded: {storage_path} ({len(content)} bytes)")
        
        # Create document record in database
        document = Document(
            id=document_uuid,  # Use UUID object, not string
            project_id=project.id,
            company_name=company_name,
            original_filename=file.filename,
            storage_path=str(storage_path),
            status=DocumentStatus.PENDING
        )
        
        db.add(document)
        db.commit()
        
        response_data = {
            "document_id": document_id_str,
            "filename": file.filename,
            "company_name": company_name,
            "project_id": str(project.id),
            "status": "uploaded",
            "file_size": len(content),
            "processing_queued": process_immediately
        }
        
        # Queue processing if requested
        if process_immediately:
            background_tasks.add_task(
                _process_document_background,
                document_id_str,
                str(project.id)
            )
            response_data["message"] = "Document uploaded and queued for processing"
        else:
            response_data["message"] = "Document uploaded successfully"
        
        return response_data
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Document upload failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Upload failed: {str(e)}")


def _process_document_background(document_id: str, project_id: str):
    """Background task for document processing."""
    try:
        from ...core.database import SessionLocal
        db = SessionLocal()
        try:
            result = document_processing_service.process_document(document_id, db)
            logger.info(f"Background processing completed for {document_id}: {result['status']}")
        finally:
            db.close()
    except Exception as e:
        logger.error(f"Background processing failed for {document_id}: {str(e)}")


@router.post("/{document_id}/process")
def process_document(
    document_id: str,
    background_tasks: BackgroundTasks,
    generate_embeddings: bool = True,
    db: Session = Depends(get_db)
):
    """Trigger OCR processing for a specific document."""
    try:
        # Check if document exists
        document = db.query(Document).filter(Document.id == document_id).first()
        if not document:
            raise HTTPException(status_code=404, detail="Document not found")
        
        # Check if already processing
        if document.status == DocumentStatus.PROCESSING:
            return {
                "document_id": document_id,
                "status": "already_processing",
                "message": "Document is already being processed"
            }
        
        # Check if already processed
        if document.status == DocumentStatus.PROCESSED:
            chunk_count = db.query(DocumentChunk).filter(
                DocumentChunk.document_id == document_id
            ).count()
            return {
                "document_id": document_id,
                "status": "already_processed",
                "chunks_count": chunk_count,
                "message": "Document has already been processed"
            }
        
        # Queue processing
        background_tasks.add_task(
            _process_document_background,
            document_id,
            document.project_id
        )
        
        return {
            "document_id": document_id,
            "status": "processing_queued",
            "message": "Document processing has been queued",
            "generate_embeddings": generate_embeddings
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Process document failed for {document_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Processing failed: {str(e)}")


@router.get("/service/status")
def get_service_status():
    """Get status of document processing services."""
    try:
        return document_processing_service.is_service_available()
        
    except Exception as e:
        logger.error(f"Service status check failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Service status check failed: {str(e)}")


@router.get("/{document_id}/status")
def get_document_status(
    document_id: str,
    db: Session = Depends(get_db)
):
    """Get processing status for a document."""
    try:
        status = document_processing_service.get_processing_status(document_id, db)
        
        if status.get("status") == "not_found":
            raise HTTPException(status_code=404, detail="Document not found")
        
        return status
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Get document status failed for {document_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Status check failed: {str(e)}")


@router.get("/{document_id}/chunks")
def get_document_chunks(
    document_id: str,
    page_number: Optional[int] = None,
    chunk_type: Optional[str] = None,
    limit: int = 100,
    offset: int = 0,
    db: Session = Depends(get_db)
):
    """Get chunks for a document with optional filtering."""
    try:
        import uuid as uuid_lib
        
        # Convert string to UUID
        try:
            doc_uuid = uuid_lib.UUID(document_id)
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid document ID format")
        
        # Check if document exists
        document = db.query(Document).filter(Document.id == doc_uuid).first()
        if not document:
            raise HTTPException(status_code=404, detail="Document not found")
        
        # Build query
        query = db.query(DocumentChunk).filter(DocumentChunk.document_id == doc_uuid)
        
        # Apply filters
        if page_number is not None:
            query = query.filter(DocumentChunk.page_number == page_number)
        
        if chunk_type:
            query = query.filter(DocumentChunk.chunk_type == chunk_type)
        
        # Get total count
        total_count = query.count()
        
        # Apply pagination
        chunks = query.offset(offset).limit(limit).all()
        
        # Format response
        chunk_data = []
        for chunk in chunks:
            chunk_data.append({
                "id": str(chunk.id),
                "page_number": chunk.page_number,
                "chunk_type": chunk.chunk_type.value,
                "content": chunk.content,
                "bounding_box": chunk.bounding_box,
                "content_preview": chunk.content[:100] + "..." if chunk.content and len(chunk.content) > 100 else chunk.content
            })
        
        return {
            "document_id": document_id,
            "filename": document.original_filename,
            "company_name": document.company_name,
            "total_chunks": total_count,
            "page_count": document.page_count,
            "chunks": chunk_data,
            "pagination": {
                "offset": offset,
                "limit": limit,
                "total": total_count,
                "has_more": offset + limit < total_count
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Get document chunks failed for {document_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Chunk retrieval failed: {str(e)}")


@router.get("/{document_id}/chunks/{chunk_id}")
def get_chunk_details(
    document_id: str,
    chunk_id: str,
    db: Session = Depends(get_db)
):
    """Get detailed information for a specific chunk."""
    try:
        import uuid as uuid_lib
        
        # Convert strings to UUIDs
        try:
            doc_uuid = uuid_lib.UUID(document_id)
            chunk_uuid = uuid_lib.UUID(chunk_id)
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid UUID format")
        
        # Get chunk with document verification
        chunk = db.query(DocumentChunk).filter(
            DocumentChunk.id == chunk_uuid,
            DocumentChunk.document_id == doc_uuid
        ).first()
        
        if not chunk:
            raise HTTPException(status_code=404, detail="Chunk not found")
        
        # Get document info
        document = db.query(Document).filter(Document.id == document_id).first()
        
        return {
            "chunk_id": str(chunk.id),
            "document_id": document_id,
            "document_name": document.original_filename if document else "Unknown",
            "company_name": document.company_name if document else "Unknown",
            "page_number": chunk.page_number,
            "chunk_type": chunk.chunk_type.value,
            "content": chunk.content,
            "content_length": len(chunk.content) if chunk.content else 0,
            "bounding_box": chunk.bounding_box,
            "dimensions": {
                "width": chunk.bounding_box.get("width", 0),
                "height": chunk.bounding_box.get("height", 0)
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Get chunk details failed for {chunk_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Chunk details failed: {str(e)}")


@router.post("/{document_id}/chunks/search")
def search_similar_chunks(
    document_id: str,
    query_text: str = Form(...),
    threshold: float = Form(0.7),
    limit: int = Form(10),
    db: Session = Depends(get_db)
):
    """Search for similar chunks within a document using semantic similarity."""
    try:
        import uuid as uuid_lib
        
        # Convert string to UUID
        try:
            doc_uuid = uuid_lib.UUID(document_id)
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid document ID format")
        
        # Check if document exists
        document = db.query(Document).filter(Document.id == doc_uuid).first()
        if not document:
            raise HTTPException(status_code=404, detail="Document not found")
        
        # Check if embedding service is available
        if not document_processing_service.embedding_service.is_available():
            raise HTTPException(status_code=503, detail="Embedding service not available")
        
        # Find similar chunks
        similar_chunks = document_processing_service.embedding_service.find_similar_chunks(
            query_text=query_text,
            document_id=document_id,
            threshold=threshold,
            limit=limit,
            db=db
        )
        
        return {
            "document_id": document_id,
            "query_text": query_text,
            "threshold": threshold,
            "total_matches": len(similar_chunks),
            "matches": similar_chunks
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Search similar chunks failed for {document_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Semantic search failed: {str(e)}")


@router.delete("/{document_id}")
def delete_document(
    document_id: str,
    db: Session = Depends(get_db)
):
    """Delete a document and all its associated chunks."""
    try:
        import uuid as uuid_lib
        
        # Convert string to UUID
        try:
            doc_uuid = uuid_lib.UUID(document_id)
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid document ID format")
        
        # Get document
        document = db.query(Document).filter(Document.id == doc_uuid).first()
        if not document:
            raise HTTPException(status_code=404, detail="Document not found")
        
        # Delete associated chunks (cascade should handle this, but let's be explicit)
        chunks_deleted = db.query(DocumentChunk).filter(
            DocumentChunk.document_id == doc_uuid
        ).delete()
        
        # Delete file from storage
        try:
            if os.path.exists(document.storage_path):
                os.remove(document.storage_path)
                logger.info(f"Deleted file: {document.storage_path}")
        except Exception as e:
            logger.warning(f"Could not delete file {document.storage_path}: {str(e)}")
        
        # Delete document record
        db.delete(document)
        db.commit()
        
        return {
            "document_id": document_id,
            "status": "deleted",
            "chunks_deleted": chunks_deleted,
            "message": "Document and associated data deleted successfully"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Delete document failed for {document_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Deletion failed: {str(e)}")


@router.post("/test-project-creation")
def test_project_creation(
    project_name: str = Form("Test Project"),
    db: Session = Depends(get_db)
):
    """Test endpoint to create a project and verify database connectivity."""
    try:
        logger.info(f"Testing project creation with name: {project_name}")
        
        # Create a test project
        project_id = uuid.uuid4()
        project = Project(
            id=project_id,
            name=project_name,
            status=ProjectStatus.UPLOADING
        )
        
        db.add(project)
        db.commit()
        db.refresh(project)
        
        logger.info(f"Successfully created test project: {project.id}")
        
        # Try to query it back
        queried_project = db.query(Project).filter(Project.id == project_id).first()
        
        if queried_project:
            logger.info(f"Successfully queried back project: {queried_project.id}")
            return {
                "status": "success",
                "project_id": str(project.id),
                "project_name": project.name,
                "can_query_back": True
            }
        else:
            logger.error("Could not query back the project")
            return {
                "status": "error",
                "message": "Project created but could not be queried back",
                "project_id": str(project.id)
            }
            
    except Exception as e:
        logger.error(f"Test project creation failed: {str(e)}")
        db.rollback()
        return {
            "status": "error", 
            "message": str(e),
            "error_type": type(e).__name__
        }


@router.get("/{document_id}/file")
async def get_document_file(
    document_id: str,
    db: Session = Depends(get_db)
):
    """Serve the actual document file for PDF viewing."""
    try:
        import uuid as uuid_lib
        from fastapi.responses import FileResponse
        import os
        
        # Convert string to UUID
        try:
            doc_uuid = uuid_lib.UUID(document_id)
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid document ID format")
        
        # Get document
        document = db.query(Document).filter(Document.id == doc_uuid).first()
        if not document:
            raise HTTPException(status_code=404, detail="Document not found")
        
        # Check if file exists - handle both absolute and relative paths
        file_path = document.storage_path
        
        # If the path is relative, make it absolute from project root
        if not os.path.isabs(file_path):
            # Go up to project root from src/api/v1/
            project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
            absolute_file_path = os.path.join(project_root, file_path)
        else:
            absolute_file_path = file_path
        
        if not os.path.exists(absolute_file_path):
            raise HTTPException(
                status_code=404,
                detail=f"Document file not found in storage: {absolute_file_path}"
            )
        
        # Determine media type based on file extension
        file_ext = os.path.splitext(document.original_filename)[1].lower()
        media_type = "application/pdf" if file_ext == ".pdf" else "application/octet-stream"
        
        # Return the file
        return FileResponse(
            path=absolute_file_path,
            media_type=media_type,
            filename=document.original_filename,
            headers={
                "Cache-Control": "public, max-age=3600",
                "Content-Disposition": f"inline; filename={document.original_filename}"
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to serve document file: {str(e)}"
        )