from typing import List
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from ...core.database import get_db
from ...models.database import Tender
from ...models.schemas import TenderCreate, TenderResponse, ErrorResponse
from ...utils.validators import validate_tender_data
from ...utils.exceptions import ValidationException, not_found_exception_handler, validation_exception_handler

router = APIRouter(include_in_schema=False)


@router.get("", response_model=List[TenderResponse])
async def get_tenders(
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=100, description="Maximum number of records to return"),
    db: Session = Depends(get_db)
):
    """Get list of all tenders with pagination."""
    try:
        tenders = db.query(Tender).offset(skip).limit(limit).all()
        return tenders
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail={
                "message": "Failed to retrieve tenders",
                "error_code": "DATABASE_ERROR"
            }
        )


@router.post("", response_model=TenderResponse)
async def create_tender(
    tender_data: TenderCreate,
    db: Session = Depends(get_db)
):
    """Create a new tender."""
    try:
        # Validate input data
        validated_data = validate_tender_data(tender_data.model_dump())
        
        # Create new tender
        tender = Tender(**validated_data)
        db.add(tender)
        db.commit()
        db.refresh(tender)
        
        return tender
        
    except ValidationException as e:
        raise validation_exception_handler(e.message, e.details)
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=500,
            detail={
                "message": "Failed to create tender",
                "error_code": "DATABASE_ERROR"
            }
        )


@router.get("/{tender_id}", response_model=TenderResponse)
async def get_tender(
    tender_id: str,
    db: Session = Depends(get_db)
):
    """Get a specific tender by ID."""
    tender = db.query(Tender).filter(Tender.id == tender_id).first()
    
    if not tender:
        raise not_found_exception_handler(
            f"Tender with ID {tender_id} not found",
            "Tender"
        )
    
    return tender


@router.put("/{tender_id}", response_model=TenderResponse)
async def update_tender(
    tender_id: str,
    tender_data: TenderCreate,
    db: Session = Depends(get_db)
):
    """Update a specific tender."""
    try:
        # Check if tender exists
        tender = db.query(Tender).filter(Tender.id == tender_id).first()
        if not tender:
            raise not_found_exception_handler(
                f"Tender with ID {tender_id} not found",
                "Tender"
            )
        
        # Validate input data
        validated_data = validate_tender_data(tender_data.model_dump())
        
        # Update tender fields
        for field, value in validated_data.items():
            setattr(tender, field, value)
        
        db.commit()
        db.refresh(tender)
        
        return tender
        
    except ValidationException as e:
        raise validation_exception_handler(e.message, e.details)
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=500,
            detail={
                "message": "Failed to update tender",
                "error_code": "DATABASE_ERROR"
            }
        )


@router.delete("/{tender_id}")
async def delete_tender(
    tender_id: str,
    db: Session = Depends(get_db)
):
    """Delete a specific tender and all associated data."""
    try:
        # Check if tender exists
        tender = db.query(Tender).filter(Tender.id == tender_id).first()
        if not tender:
            raise not_found_exception_handler(
                f"Tender with ID {tender_id} not found",
                "Tender"
            )
        
        # Delete associated files from storage
        from ...storage.manager import file_storage
        file_storage.delete_tender_files(tender_id)
        
        # Delete from database (cascade will handle related records)
        db.delete(tender)
        db.commit()
        
        return {"message": f"Tender {tender_id} deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=500,
            detail={
                "message": "Failed to delete tender",
                "error_code": "DATABASE_ERROR"
            }
        )


@router.get("/{tender_id}/summary")
async def get_tender_summary(
    tender_id: str,
    db: Session = Depends(get_db)
):
    """Get a summary of tender including bid count and analysis status."""
    tender = db.query(Tender).filter(Tender.id == tender_id).first()
    
    if not tender:
        raise not_found_exception_handler(
            f"Tender with ID {tender_id} not found",
            "Tender"
        )
    
    # Count bids
    from ...models.database import Bid, AnalysisResult
    bid_count = db.query(Bid).filter(Bid.tender_id == tender_id).count()
    analysis_count = db.query(AnalysisResult).filter(AnalysisResult.tender_id == tender_id).count()
    
    return {
        "tender_id": tender_id,
        "title": tender.title,
        "department": tender.department,
        "budget": tender.budget,
        "analysis_status": tender.analysis_status.value,
        "bid_count": bid_count,
        "analysis_results_count": analysis_count,
        "created_at": tender.created_at,
        "updated_at": tender.updated_at
    }