"""
Document Processing Service for TraceFast

This service orchestrates the complete document processing pipeline:
1. File upload handling
2. OCR processing with deepdoctection
3. Chunk extraction and database storage
4. Embedding generation
5. Status updates and progress tracking
"""

from typing import Dict, List, Optional, Any
import logging
import os
from pathlib import Path
from sqlalchemy.orm import Session
from uuid import UUID, uuid4
import asyncio
from concurrent.futures import ThreadPoolExecutor
import time

from ..models.database import Project, Document, DocumentChunk, DocumentStatus, ProjectStatus
from .ocr_service import OCRService
from .embedding_service import embedding_service
from ..storage.manager import file_storage
from ..core.database import get_db

logger = logging.getLogger(__name__)


class DocumentProcessingService:
    """Service for complete document processing pipeline."""

    def __init__(self):
        self.ocr_service = OCRService()
        self.embedding_service = embedding_service
        self.executor = ThreadPoolExecutor(max_workers=2)  # Limit concurrent OCR jobs
    
    def process_document(
        self,
        document_id: str,
        db: Session,
        generate_embeddings: bool = True
    ) -> Dict[str, Any]:
        """Process a single document through the complete pipeline.
        
        Args:
            document_id: ID of document to process
            db: Database session
            generate_embeddings: Whether to generate embeddings after OCR
            
        Returns:
            Processing results summary
        """
        try:
            # Convert string document_id to UUID for database query
            import uuid as uuid_lib
            try:
                document_uuid = uuid_lib.UUID(document_id)
            except (ValueError, TypeError):
                raise ValueError(f"Invalid document ID format: {document_id}")
            
            # Get document from database
            document = db.query(Document).filter(Document.id == document_uuid).first()
            if not document:
                raise ValueError(f"Document {document_id} not found")
            
            logger.info(f"Starting processing for document {document_id}: {document.original_filename}")
            
            # Update status to processing
            document.status = DocumentStatus.PROCESSING
            db.commit()
            
            start_time = time.time()
            results = {
                "document_id": document_id,
                "filename": document.original_filename,
                "status": "processing",
                "steps": {}
            }
            
            # Step 1: OCR Processing
            logger.info(f"Step 1: Running OCR on {document.storage_path}")
            ocr_start = time.time()
            
            try:
                # Extract content using OCR service
                ocr_content = self.ocr_service.extract_document_content(document.storage_path)
                
                # Save chunks to database
                chunks_saved = self.ocr_service.save_chunks_to_database(document_id, ocr_content, db)
                
                ocr_time = time.time() - ocr_start
                results["steps"]["ocr"] = {
                    "status": "completed",
                    "chunks_saved": chunks_saved,
                    "pages_processed": ocr_content["metadata"]["total_pages"],
                    "processing_time": ocr_time
                }
                
                # Update document metadata
                document.page_count = ocr_content["metadata"]["total_pages"]
                
                logger.info(f"OCR completed: {chunks_saved} chunks saved in {ocr_time:.2f}s")
                
            except Exception as e:
                logger.error(f"OCR failed for document {document_id}: {str(e)}")
                results["steps"]["ocr"] = {
                    "status": "failed",
                    "error": str(e)
                }
                document.status = DocumentStatus.ERROR
                db.commit()
                return results
            
            # Step 2: Generate Embeddings (if requested)
            if generate_embeddings and self.embedding_service.is_available():
                logger.info(f"Step 2: Generating embeddings for document {document_id}")
                embedding_start = time.time()
                
                try:
                    embedding_results = self.embedding_service.generate_chunk_embeddings_batch(
                        [document_id], db=db
                    )
                    
                    embedding_time = time.time() - embedding_start
                    results["steps"]["embeddings"] = {
                        "status": "completed",
                        "embeddings_generated": embedding_results["generated_embeddings"],
                        "processing_time": embedding_time
                    }
                    
                    logger.info(f"Embeddings generated: {embedding_results['generated_embeddings']} in {embedding_time:.2f}s")
                    
                except Exception as e:
                    logger.error(f"Embedding generation failed for document {document_id}: {str(e)}")
                    results["steps"]["embeddings"] = {
                        "status": "failed",
                        "error": str(e)
                    }
                    # Don't fail the entire process for embedding errors
            
            # Update final status
            document.status = DocumentStatus.PROCESSED
            total_time = time.time() - start_time
            
            results.update({
                "status": "completed",
                "total_processing_time": total_time,
                "completed_at": time.time()
            })
            
            db.commit()
            logger.info(f"Document processing completed for {document_id} in {total_time:.2f}s")
            
            return results
            
        except Exception as e:
            logger.error(f"Document processing failed for {document_id}: {str(e)}")
            
            # Update document status to error
            try:
                import uuid as uuid_lib
                try:
                    document_uuid = uuid_lib.UUID(document_id)
                    document = db.query(Document).filter(Document.id == document_uuid).first()
                    if document:
                        document.status = DocumentStatus.ERROR
                        db.commit()
                except (ValueError, TypeError):
                    pass
            except Exception:
                pass
            
            return {
                "document_id": document_id,
                "status": "failed",
                "error": str(e)
            }
    
    def process_project_documents(
        self,
        project_id: str,
        db: Session,
        document_ids: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """Process all documents in a project.
        
        Args:
            project_id: ID of project to process
            db: Database session
            document_ids: Specific document IDs to process (if None, process all)
            
        Returns:
            Processing results for all documents
        """
        try:
            # Get project
            project = db.query(Project).filter(Project.id == project_id).first()
            if not project:
                raise ValueError(f"Project {project_id} not found")
            
            # Update project status
            project.status = ProjectStatus.ANALYZING
            db.commit()
            
            logger.info(f"Starting project processing for {project_id}: {project.name}")
            
            # Get documents to process
            if document_ids:
                documents = db.query(Document).filter(
                    Document.project_id == project_id,
                    Document.id.in_(document_ids)
                ).all()
            else:
                documents = db.query(Document).filter(
                    Document.project_id == project_id
                ).all()
            
            if not documents:
                logger.warning(f"No documents found for project {project_id}")
                return {
                    "project_id": project_id,
                    "status": "completed",
                    "documents_processed": 0,
                    "message": "No documents to process"
                }
            
            start_time = time.time()
            results = {
                "project_id": project_id,
                "project_name": project.name,
                "total_documents": len(documents),
                "documents": {},
                "summary": {
                    "successful": 0,
                    "failed": 0,
                    "total_chunks": 0,
                    "total_pages": 0
                }
            }
            
            # Process each document
            for doc in documents:
                doc_id = str(doc.id)
                logger.info(f"Processing document {doc_id}: {doc.original_filename}")
                
                try:
                    doc_result = self.process_document(doc_id, db)
                    results["documents"][doc_id] = doc_result
                    
                    if doc_result["status"] == "completed":
                        results["summary"]["successful"] += 1
                        if "ocr" in doc_result["steps"]:
                            results["summary"]["total_chunks"] += doc_result["steps"]["ocr"].get("chunks_saved", 0)
                            results["summary"]["total_pages"] += doc_result["steps"]["ocr"].get("pages_processed", 0)
                    else:
                        results["summary"]["failed"] += 1
                        
                except Exception as e:
                    logger.error(f"Failed to process document {doc_id}: {str(e)}")
                    results["documents"][doc_id] = {
                        "document_id": doc_id,
                        "status": "failed",
                        "error": str(e)
                    }
                    results["summary"]["failed"] += 1
            
            # Update project status
            if results["summary"]["failed"] == 0:
                project.status = ProjectStatus.COMPLETED
            elif results["summary"]["successful"] > 0:
                project.status = ProjectStatus.COMPLETED  # Partial success still counts as completed
            else:
                project.status = ProjectStatus.ERROR
            
            total_time = time.time() - start_time
            results.update({
                "status": "completed",
                "total_processing_time": total_time,
                "completed_at": time.time()
            })
            
            db.commit()
            
            logger.info(f"Project processing completed: {results['summary']['successful']}/{results['total_documents']} documents successful")
            return results
            
        except Exception as e:
            logger.error(f"Project processing failed for {project_id}: {str(e)}")
            
            # Update project status to error
            try:
                project = db.query(Project).filter(Project.id == project_id).first()
                if project:
                    project.status = ProjectStatus.ERROR
                    db.commit()
            except Exception:
                pass
            
            return {
                "project_id": project_id,
                "status": "failed",
                "error": str(e)
            }
    
    def get_processing_status(self, document_id: str, db: Session) -> Dict[str, Any]:
        """Get current processing status of a document.
        
        Args:
            document_id: ID of document
            db: Database session
            
        Returns:
            Current processing status and progress information
        """
        try:
            # Convert string document_id to UUID for database query
            import uuid as uuid_lib
            try:
                document_uuid = uuid_lib.UUID(document_id)
            except (ValueError, TypeError):
                return {
                    "document_id": document_id,
                    "status": "invalid_id",
                    "error": "Invalid document ID format"
                }
            
            document = db.query(Document).filter(Document.id == document_uuid).first()
            if not document:
                return {
                    "document_id": document_id,
                    "status": "not_found",
                    "error": "Document not found"
                }
            
            # Get chunk count for progress estimation
            chunk_count = db.query(DocumentChunk).filter(
                DocumentChunk.document_id == document_uuid
            ).count()
            
            status_info = {
                "document_id": document_id,
                "filename": document.original_filename,
                "status": document.status.value,
                "uploaded_at": document.uploaded_at.isoformat(),
                "page_count": document.page_count,
                "chunks_extracted": chunk_count
            }
            
            # Add progress estimation
            if document.status == DocumentStatus.PROCESSING:
                status_info["progress"] = "in_progress"
                status_info["message"] = "Document is being processed..."
            elif document.status == DocumentStatus.PROCESSED:
                status_info["progress"] = "completed"
                status_info["message"] = f"Processing completed. {chunk_count} chunks extracted."
            elif document.status == DocumentStatus.ERROR:
                status_info["progress"] = "failed"
                status_info["message"] = "Processing failed. Please check logs."
            else:
                status_info["progress"] = "pending"
                status_info["message"] = "Waiting to be processed."
            
            return status_info
            
        except Exception as e:
            logger.error(f"Failed to get status for document {document_id}: {str(e)}")
            return {
                "document_id": document_id,
                "status": "error",
                "error": str(e)
            }
    
    def get_project_processing_status(self, project_id: str, db: Session) -> Dict[str, Any]:
        """Get processing status for all documents in a project.
        
        Args:
            project_id: ID of project
            db: Database session
            
        Returns:
            Project processing status and document details
        """
        try:
            project = db.query(Project).filter(Project.id == project_id).first()
            if not project:
                return {
                    "project_id": project_id,
                    "status": "not_found",
                    "error": "Project not found"
                }
            
            documents = db.query(Document).filter(
                Document.project_id == project_id
            ).all()
            
            # Count documents by status
            status_counts = {
                "pending": 0,
                "processing": 0,
                "processed": 0,
                "error": 0
            }
            
            document_details = []
            total_chunks = 0
            
            for doc in documents:
                status_counts[doc.status.value] += 1
                
                chunk_count = db.query(DocumentChunk).filter(
                    DocumentChunk.document_id == doc.id
                ).count()
                total_chunks += chunk_count
                
                document_details.append({
                    "document_id": str(doc.id),
                    "filename": doc.original_filename,
                    "company_name": doc.company_name,
                    "status": doc.status.value,
                    "page_count": doc.page_count,
                    "chunks": chunk_count
                })
            
            # Calculate overall progress
            total_docs = len(documents)
            completed_docs = status_counts["processed"]
            failed_docs = status_counts["error"]
            
            if total_docs == 0:
                progress_percentage = 0
            else:
                progress_percentage = int((completed_docs / total_docs) * 100)
            
            return {
                "project_id": project_id,
                "project_name": project.name,
                "project_status": project.status.value,
                "total_documents": total_docs,
                "status_counts": status_counts,
                "progress_percentage": progress_percentage,
                "total_chunks_extracted": total_chunks,
                "documents": document_details,
                "summary": {
                    "completed": completed_docs,
                    "failed": failed_docs,
                    "remaining": total_docs - completed_docs - failed_docs
                }
            }
            
        except Exception as e:
            logger.error(f"Failed to get project status for {project_id}: {str(e)}")
            return {
                "project_id": project_id,
                "status": "error",
                "error": str(e)
            }
    
    def is_service_available(self) -> Dict[str, Any]:
        """Check availability of all processing services.
        
        Returns:
            Service availability status
        """
        return {
            "ocr_service": {
                "available": self.ocr_service.is_available(),
                "info": self.ocr_service.get_model_info()
            },
            "embedding_service": {
                "available": self.embedding_service.is_available(),
                "info": self.embedding_service.get_model_info()
            }
        }


# Singleton instance for global use
document_processing_service = DocumentProcessingService()