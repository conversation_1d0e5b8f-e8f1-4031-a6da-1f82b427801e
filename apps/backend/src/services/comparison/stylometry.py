from typing import Dict, List
import re
import statistics
import logging

logger = logging.getLogger(__name__)


class StylometryAnalysis:
    """Stylometry analysis for detecting writing style similarities.
    
    This class analyzes various writing style features to determine if 
    different documents might be written by the same author or using 
    the same template.
    """

    def __init__(self):
        self.similarity_threshold = 0.75

    def analyze_writing_style(self, text: str) -> Dict:
        """Analyze writing style features of a text.
        
        Args:
            text: Input text to analyze
            
        Returns:
            Dictionary containing various style metrics
        """
        if not text:
            return {}

        # Clean text for analysis
        clean_text = self._clean_text(text)
        sentences = self._split_sentences(clean_text)
        words = clean_text.split()

        style_features = {
            # Sentence-level features
            "avg_sentence_length": self._average_sentence_length(sentences),
            "sentence_length_variance": self._sentence_length_variance(sentences),
            "sentence_count": len(sentences),
            
            # Word-level features
            "avg_word_length": self._average_word_length(words),
            "word_count": len(words),
            "unique_word_ratio": len(set(words)) / len(words) if words else 0,
            
            # Punctuation features
            "comma_frequency": self._punctuation_frequency(text, '，'),
            "period_frequency": self._punctuation_frequency(text, '。'),
            "question_frequency": self._punctuation_frequency(text, '？'),
            "exclamation_frequency": self._punctuation_frequency(text, '！'),
            
            # Character-level features
            "character_count": len(text),
            "digit_ratio": self._digit_ratio(text),
            "punctuation_ratio": self._punctuation_ratio(text),
            
            # Readability features
            "readability_score": self._calculate_readability(sentences, words),
            
            # Structural features
            "paragraph_count": self._count_paragraphs(text),
            "avg_paragraph_length": self._average_paragraph_length(text),
            
            # Language-specific features (Chinese)
            "traditional_chinese_ratio": self._traditional_chinese_ratio(text),
            "formal_language_score": self._formal_language_score(text),
        }

        return style_features

    def compare_writing_styles(self, text1: str, text2: str) -> float:
        """Compare writing styles between two texts.
        
        Args:
            text1: First text
            text2: Second text
            
        Returns:
            Similarity score between 0 and 1
        """
        style1 = self.analyze_writing_style(text1)
        style2 = self.analyze_writing_style(text2)
        
        if not style1 or not style2:
            return 0.0

        # Calculate similarity for each feature
        similarities = []
        
        for feature in style1.keys():
            if feature in style2:
                sim = self._calculate_feature_similarity(
                    style1[feature], 
                    style2[feature], 
                    feature
                )
                similarities.append(sim)

        # Calculate weighted average
        overall_similarity = statistics.mean(similarities) if similarities else 0.0
        
        logger.debug(f"Stylometry similarity: {overall_similarity:.3f}")
        return overall_similarity

    def find_stylometric_similarities(
        self, 
        documents: List[str]
    ) -> List[Dict]:
        """Find stylometric similarities between multiple documents.
        
        Args:
            documents: List of document texts
            
        Returns:
            List of similar document pairs with similarity scores
        """
        similarities = []
        
        for i in range(len(documents)):
            for j in range(i + 1, len(documents)):
                similarity = self.compare_writing_styles(documents[i], documents[j])
                
                if similarity >= self.similarity_threshold:
                    similarities.append({
                        "doc1_index": i,
                        "doc2_index": j,
                        "similarity_score": similarity,
                        "analysis_type": "stylometry",
                        "features_analyzed": self._get_dominant_features(
                            documents[i], documents[j]
                        )
                    })
        
        # Sort by similarity score
        similarities.sort(key=lambda x: x["similarity_score"], reverse=True)
        
        return similarities

    def _clean_text(self, text: str) -> str:
        """Clean text for stylometric analysis."""
        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text)
        return text.strip()

    def _split_sentences(self, text: str) -> List[str]:
        """Split text into sentences."""
        # Split on Chinese and English sentence endings
        sentences = re.split(r'[。！？\.!\?]+', text)
        return [s.strip() for s in sentences if s.strip()]

    def _average_sentence_length(self, sentences: List[str]) -> float:
        """Calculate average sentence length."""
        if not sentences:
            return 0.0
        lengths = [len(s.split()) for s in sentences]
        return statistics.mean(lengths)

    def _sentence_length_variance(self, sentences: List[str]) -> float:
        """Calculate variance in sentence lengths."""
        if len(sentences) < 2:
            return 0.0
        lengths = [len(s.split()) for s in sentences]
        return statistics.variance(lengths)

    def _average_word_length(self, words: List[str]) -> float:
        """Calculate average word length."""
        if not words:
            return 0.0
        lengths = [len(word) for word in words]
        return statistics.mean(lengths)

    def _punctuation_frequency(self, text: str, punctuation: str) -> float:
        """Calculate frequency of specific punctuation."""
        if not text:
            return 0.0
        count = text.count(punctuation)
        return count / len(text)

    def _digit_ratio(self, text: str) -> float:
        """Calculate ratio of digits in text."""
        if not text:
            return 0.0
        digit_count = sum(1 for char in text if char.isdigit())
        return digit_count / len(text)

    def _punctuation_ratio(self, text: str) -> float:
        """Calculate ratio of punctuation in text."""
        if not text:
            return 0.0
        punctuation_chars = "，。！？；：、（）「」『』【】"
        punct_count = sum(1 for char in text if char in punctuation_chars)
        return punct_count / len(text)

    def _calculate_readability(self, sentences: List[str], words: List[str]) -> float:
        """Calculate simple readability score."""
        if not sentences or not words:
            return 0.0
        
        avg_sentence_length = len(words) / len(sentences)
        avg_word_length = sum(len(word) for word in words) / len(words)
        
        # Simple readability formula
        readability = 100 - (avg_sentence_length * 2) - (avg_word_length * 3)
        return max(0, min(100, readability))

    def _count_paragraphs(self, text: str) -> int:
        """Count paragraphs in text."""
        paragraphs = text.split('\n\n')
        return len([p for p in paragraphs if p.strip()])

    def _average_paragraph_length(self, text: str) -> float:
        """Calculate average paragraph length."""
        paragraphs = [p.strip() for p in text.split('\n\n') if p.strip()]
        if not paragraphs:
            return 0.0
        lengths = [len(p.split()) for p in paragraphs]
        return statistics.mean(lengths)

    def _traditional_chinese_ratio(self, text: str) -> float:
        """Calculate ratio of traditional Chinese characters."""
        if not text:
            return 0.0
        
        # Simplified approach - count characters in traditional Chinese range
        traditional_count = sum(1 for char in text if '\u4e00' <= char <= '\u9fff')
        return traditional_count / len(text)

    def _formal_language_score(self, text: str) -> float:
        """Calculate formal language usage score."""
        formal_indicators = [
            '本公司', '敝公司', '貴公司', '茲', '惟', '倘若', '爰', '謹', 
            '敬請', '恭請', '附請', '檢附', '承蒙', '蒙此'
        ]
        
        if not text:
            return 0.0
        
        formal_count = sum(1 for indicator in formal_indicators if indicator in text)
        return formal_count / len(formal_indicators)

    def _calculate_feature_similarity(
        self, 
        value1: float, 
        value2: float, 
        feature_name: str
    ) -> float:
        """Calculate similarity between two feature values."""
        if value1 == 0 and value2 == 0:
            return 1.0
        
        if value1 == 0 or value2 == 0:
            return 0.0
        
        # For ratio-based features, use direct comparison
        if 'ratio' in feature_name or 'frequency' in feature_name:
            max_val = max(value1, value2)
            min_val = min(value1, value2)
            return min_val / max_val if max_val > 0 else 0.0
        
        # For count-based features, use relative difference
        if 'count' in feature_name:
            diff = abs(value1 - value2)
            avg = (value1 + value2) / 2
            return max(0, 1 - (diff / avg)) if avg > 0 else 0.0
        
        # For other features, use normalized difference
        diff = abs(value1 - value2)
        max_val = max(value1, value2)
        return max(0, 1 - (diff / max_val)) if max_val > 0 else 0.0

    def _get_dominant_features(self, text1: str, text2: str) -> List[str]:
        """Get list of dominant features that contributed to similarity."""
        style1 = self.analyze_writing_style(text1)
        style2 = self.analyze_writing_style(text2)
        
        feature_similarities = []
        for feature in style1.keys():
            if feature in style2:
                sim = self._calculate_feature_similarity(
                    style1[feature], 
                    style2[feature], 
                    feature
                )
                feature_similarities.append((feature, sim))
        
        # Sort by similarity and return top features
        feature_similarities.sort(key=lambda x: x[1], reverse=True)
        return [feature for feature, _ in feature_similarities[:5]]

    def get_style_fingerprint(self, text: str) -> str:
        """Generate a style fingerprint for the text."""
        style = self.analyze_writing_style(text)
        
        # Create a simple fingerprint based on key features
        fingerprint_features = [
            f"sl:{style.get('avg_sentence_length', 0):.1f}",
            f"wl:{style.get('avg_word_length', 0):.1f}",
            f"pr:{style.get('punctuation_ratio', 0):.3f}",
            f"rs:{style.get('readability_score', 0):.1f}",
            f"fs:{style.get('formal_language_score', 0):.2f}"
        ]
        
        return "-".join(fingerprint_features)