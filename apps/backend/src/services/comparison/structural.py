from typing import Dict, List, Tuple
import logging

logger = logging.getLogger(__name__)


class StructuralComparison:
    """Structural analysis for comparing document layouts and organization.
    
    This class analyzes document structure using layout OCR information
    to detect similarities in document templates, formatting, and organization.
    """

    def __init__(self):
        self.similarity_threshold = 0.8

    def compare_document_structures(self, layout1: Dict, layout2: Dict) -> float:
        """Compare structural similarity between two document layouts.
        
        Args:
            layout1: Layout information from first document
            layout2: Layout information from second document
            
        Returns:
            Structural similarity score between 0 and 1
        """
        if not layout1 or not layout2:
            return 0.0

        similarity_scores = []

        # Compare page-level structure
        page_similarity = self._compare_page_structure(layout1, layout2)
        similarity_scores.append(("page_structure", page_similarity, 0.3))

        # Compare section organization
        section_similarity = self._compare_section_structure(layout1, layout2)
        similarity_scores.append(("section_structure", section_similarity, 0.25))

        # Compare table structures
        table_similarity = self._compare_table_structures(layout1, layout2)
        similarity_scores.append(("table_structure", table_similarity, 0.2))

        # Compare overall document organization
        doc_similarity = self._compare_document_organization(layout1, layout2)
        similarity_scores.append(("document_organization", doc_similarity, 0.25))

        # Calculate weighted average
        total_weight = sum(weight for _, _, weight in similarity_scores)
        weighted_sum = sum(score * weight for _, score, weight in similarity_scores)
        
        overall_similarity = weighted_sum / total_weight if total_weight > 0 else 0.0
        
        logger.debug(f"Structural similarity: {overall_similarity:.3f}")
        return overall_similarity

    def find_structural_similarities(
        self, 
        layouts: List[Dict]
    ) -> List[Dict]:
        """Find structural similarities between multiple documents.
        
        Args:
            layouts: List of document layout information
            
        Returns:
            List of structurally similar document pairs
        """
        similarities = []
        
        for i in range(len(layouts)):
            for j in range(i + 1, len(layouts)):
                similarity = self.compare_document_structures(layouts[i], layouts[j])
                
                if similarity >= self.similarity_threshold:
                    similarities.append({
                        "doc1_index": i,
                        "doc2_index": j,
                        "similarity_score": similarity,
                        "analysis_type": "structural",
                        "structural_features": self._analyze_structural_features(
                            layouts[i], layouts[j]
                        )
                    })
        
        # Sort by similarity score
        similarities.sort(key=lambda x: x["similarity_score"], reverse=True)
        
        return similarities

    def _compare_page_structure(self, layout1: Dict, layout2: Dict) -> float:
        """Compare page-level structure between documents."""
        pages1 = layout1.get("pages", [])
        pages2 = layout2.get("pages", [])
        
        if not pages1 or not pages2:
            return 0.0

        # Compare page counts
        page_count_similarity = min(len(pages1), len(pages2)) / max(len(pages1), len(pages2))
        
        # Compare page layouts for first few pages
        page_layout_similarities = []
        compare_count = min(3, len(pages1), len(pages2))  # Compare first 3 pages
        
        for i in range(compare_count):
            page_sim = self._compare_single_page(pages1[i], pages2[i])
            page_layout_similarities.append(page_sim)
        
        avg_page_similarity = sum(page_layout_similarities) / len(page_layout_similarities) if page_layout_similarities else 0.0
        
        # Weighted combination
        return (page_count_similarity * 0.3) + (avg_page_similarity * 0.7)

    def _compare_single_page(self, page1: Dict, page2: Dict) -> float:
        """Compare layout of two pages."""
        sections1 = page1.get("sections", [])
        sections2 = page2.get("sections", [])
        
        if not sections1 or not sections2:
            return 0.0

        # Compare number of sections
        section_count_sim = min(len(sections1), len(sections2)) / max(len(sections1), len(sections2))
        
        # Compare section types and positions
        type_similarities = []
        for section1 in sections1:
            best_match = 0.0
            for section2 in sections2:
                sim = self._compare_section_similarity(section1, section2)
                best_match = max(best_match, sim)
            type_similarities.append(best_match)
        
        avg_type_similarity = sum(type_similarities) / len(type_similarities) if type_similarities else 0.0
        
        return (section_count_sim * 0.4) + (avg_type_similarity * 0.6)

    def _compare_section_similarity(self, section1: Dict, section2: Dict) -> float:
        """Compare similarity between two sections."""
        # Compare section types
        type1 = section1.get("type", "")
        type2 = section2.get("type", "")
        type_match = 1.0 if type1 == type2 else 0.0
        
        # Compare relative positions (normalized coordinates)
        bbox1 = section1.get("bbox", [0, 0, 0, 0])
        bbox2 = section2.get("bbox", [0, 0, 0, 0])
        position_sim = self._compare_bounding_boxes(bbox1, bbox2)
        
        return (type_match * 0.6) + (position_sim * 0.4)

    def _compare_bounding_boxes(self, bbox1: List, bbox2: List) -> float:
        """Compare similarity between two bounding boxes."""
        if len(bbox1) != 4 or len(bbox2) != 4:
            return 0.0
        
        # Calculate center points
        center1 = ((bbox1[0] + bbox1[2]) / 2, (bbox1[1] + bbox1[3]) / 2)
        center2 = ((bbox2[0] + bbox2[2]) / 2, (bbox2[1] + bbox2[3]) / 2)
        
        # Calculate size similarity
        area1 = (bbox1[2] - bbox1[0]) * (bbox1[3] - bbox1[1])
        area2 = (bbox2[2] - bbox2[0]) * (bbox2[3] - bbox2[1])
        
        if area1 == 0 and area2 == 0:
            area_sim = 1.0
        elif area1 == 0 or area2 == 0:
            area_sim = 0.0
        else:
            area_sim = min(area1, area2) / max(area1, area2)
        
        # Calculate position similarity (normalized distance)
        distance = ((center1[0] - center2[0]) ** 2 + (center1[1] - center2[1]) ** 2) ** 0.5
        max_distance = 1000  # Assume maximum page dimension
        position_sim = max(0, 1 - (distance / max_distance))
        
        return (area_sim * 0.5) + (position_sim * 0.5)

    def _compare_section_structure(self, layout1: Dict, layout2: Dict) -> float:
        """Compare section organization between documents."""
        doc_structure1 = layout1.get("document_structure", {})
        doc_structure2 = layout2.get("document_structure", {})
        
        if not doc_structure1 or not doc_structure2:
            return 0.0

        similarities = []

        # Compare section names and order
        sections1 = doc_structure1.get("sections", [])
        sections2 = doc_structure2.get("sections", [])
        section_similarity = self._compare_section_lists(sections1, sections2)
        similarities.append(section_similarity)

        # Compare title similarity
        title1 = doc_structure1.get("title", "")
        title2 = doc_structure2.get("title", "")
        title_similarity = self._compare_text_similarity(title1, title2)
        similarities.append(title_similarity)

        return sum(similarities) / len(similarities) if similarities else 0.0

    def _compare_section_lists(self, sections1: List, sections2: List) -> float:
        """Compare lists of section names."""
        if not sections1 or not sections2:
            return 0.0

        # Calculate order-aware similarity
        matches = 0
        for i, section1 in enumerate(sections1):
            if i < len(sections2):
                if self._normalize_section_name(section1) == self._normalize_section_name(sections2[i]):
                    matches += 1

        order_similarity = matches / max(len(sections1), len(sections2))

        # Calculate set-based similarity (order-independent)
        set1 = set(self._normalize_section_name(s) for s in sections1)
        set2 = set(self._normalize_section_name(s) for s in sections2)
        
        intersection = len(set1.intersection(set2))
        union = len(set1.union(set2))
        set_similarity = intersection / union if union > 0 else 0.0

        # Weighted combination (favor order-aware similarity)
        return (order_similarity * 0.7) + (set_similarity * 0.3)

    def _normalize_section_name(self, section_name: str) -> str:
        """Normalize section names for comparison."""
        import re
        # Remove numbers, spaces, and normalize Chinese characters
        normalized = re.sub(r'[0-9\s]+', '', section_name.lower())
        return normalized.strip()

    def _compare_table_structures(self, layout1: Dict, layout2: Dict) -> float:
        """Compare table structures between documents."""
        # Extract table information from layouts
        tables1 = self._extract_table_info(layout1)
        tables2 = self._extract_table_info(layout2)

        if not tables1 and not tables2:
            return 1.0  # Both have no tables
        
        if not tables1 or not tables2:
            return 0.0  # One has tables, other doesn't

        # Compare table counts
        count_similarity = min(len(tables1), len(tables2)) / max(len(tables1), len(tables2))

        # Compare table structures
        structure_similarities = []
        for table1 in tables1:
            best_match = 0.0
            for table2 in tables2:
                sim = self._compare_table_structure(table1, table2)
                best_match = max(best_match, sim)
            structure_similarities.append(best_match)

        avg_structure_similarity = sum(structure_similarities) / len(structure_similarities) if structure_similarities else 0.0

        return (count_similarity * 0.4) + (avg_structure_similarity * 0.6)

    def _extract_table_info(self, layout: Dict) -> List[Dict]:
        """Extract table information from layout data."""
        tables = []
        
        for page in layout.get("pages", []):
            for section in page.get("sections", []):
                if section.get("type") == "table":
                    tables.append({
                        "rows": section.get("rows", 0),
                        "columns": section.get("columns", 0),
                        "bbox": section.get("bbox", []),
                        "content": section.get("content", "")
                    })
        
        return tables

    def _compare_table_structure(self, table1: Dict, table2: Dict) -> float:
        """Compare structure of two tables."""
        # Compare dimensions
        rows1, cols1 = table1.get("rows", 0), table1.get("columns", 0)
        rows2, cols2 = table2.get("rows", 0), table2.get("columns", 0)
        
        if rows1 == 0 or cols1 == 0 or rows2 == 0 or cols2 == 0:
            return 0.0

        row_similarity = min(rows1, rows2) / max(rows1, rows2)
        col_similarity = min(cols1, cols2) / max(cols1, cols2)

        # Compare table positions
        bbox1 = table1.get("bbox", [])
        bbox2 = table2.get("bbox", [])
        position_similarity = self._compare_bounding_boxes(bbox1, bbox2)

        return (row_similarity * 0.3) + (col_similarity * 0.3) + (position_similarity * 0.4)

    def _compare_document_organization(self, layout1: Dict, layout2: Dict) -> float:
        """Compare overall document organization."""
        doc1 = layout1.get("document_structure", {})
        doc2 = layout2.get("document_structure", {})

        similarities = []

        # Compare presence of tables
        has_tables1 = doc1.get("has_tables", False)
        has_tables2 = doc2.get("has_tables", False)
        table_presence_sim = 1.0 if has_tables1 == has_tables2 else 0.0
        similarities.append(table_presence_sim)

        # Compare presence of images
        has_images1 = doc1.get("has_images", False)
        has_images2 = doc2.get("has_images", False)
        image_presence_sim = 1.0 if has_images1 == has_images2 else 0.0
        similarities.append(image_presence_sim)

        return sum(similarities) / len(similarities) if similarities else 0.0

    def _compare_text_similarity(self, text1: str, text2: str) -> float:
        """Simple text similarity comparison."""
        if not text1 and not text2:
            return 1.0
        
        if not text1 or not text2:
            return 0.0

        # Simple word-based similarity
        words1 = set(text1.lower().split())
        words2 = set(text2.lower().split())
        
        intersection = len(words1.intersection(words2))
        union = len(words1.union(words2))
        
        return intersection / union if union > 0 else 0.0

    def _analyze_structural_features(self, layout1: Dict, layout2: Dict) -> List[str]:
        """Analyze which structural features contributed to similarity."""
        features = []

        # Check page structure
        pages1 = layout1.get("pages", [])
        pages2 = layout2.get("pages", [])
        if len(pages1) == len(pages2):
            features.append("同頁數結構")

        # Check table presence
        has_tables1 = layout1.get("document_structure", {}).get("has_tables", False)
        has_tables2 = layout2.get("document_structure", {}).get("has_tables", False)
        if has_tables1 and has_tables2:
            features.append("相似表格結構")

        # Check section organization
        sections1 = layout1.get("document_structure", {}).get("sections", [])
        sections2 = layout2.get("document_structure", {}).get("sections", [])
        common_sections = set(sections1).intersection(set(sections2))
        if len(common_sections) > 2:
            features.append("相似章節組織")

        return features