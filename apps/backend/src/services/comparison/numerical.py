from typing import Dict, List, Tuple, Optional
import re
import logging
from decimal import Decimal, InvalidOperation

logger = logging.getLogger(__name__)


class NumericalAnalysis:
    """Numerical analysis for comparing prices, specifications, and quantitative data.
    
    This class extracts and compares numerical values from bid documents
    to detect suspicious similarities in pricing, specifications, or other
    quantitative information.
    """

    def __init__(self):
        self.similarity_threshold = 0.9  # High threshold for numerical similarities
        
        # Patterns for extracting different types of numerical data
        self.price_patterns = [
            r'澳門幣\s*([0-9,]+)\s*元',  # MOP$ format
            r'MOP\$\s*([0-9,]+)',
            r'總價\s*[：:]\s*([0-9,]+)',
            r'投標金額\s*[：:]\s*([0-9,]+)',
            r'預算\s*[：:]\s*([0-9,]+)',
        ]
        
        self.spec_patterns = [
            r'([0-9.]+)\s*公尺',     # Meters
            r'([0-9.]+)\s*平方公尺',  # Square meters
            r'([0-9.]+)\s*立方公尺',  # Cubic meters
            r'([0-9.]+)\s*公斤',     # Kilograms
            r'([0-9.]+)\s*噸',       # Tons
            r'([0-9.]+)\s*天',       # Days
            r'([0-9.]+)\s*個月',     # Months
        ]

    def extract_numerical_data(self, text: str) -> Dict:
        """Extract numerical data from text.
        
        Args:
            text: Input text to analyze
            
        Returns:
            Dictionary containing extracted numerical information
        """
        if not text:
            return {}

        numerical_data = {
            "prices": self._extract_prices(text),
            "specifications": self._extract_specifications(text),
            "percentages": self._extract_percentages(text),
            "dates": self._extract_dates(text),
            "quantities": self._extract_quantities(text),
        }

        return numerical_data

    def compare_numerical_data(self, text1: str, text2: str) -> Dict:
        """Compare numerical data between two texts.
        
        Args:
            text1: First text to compare
            text2: Second text to compare
            
        Returns:
            Dictionary containing comparison results
        """
        data1 = self.extract_numerical_data(text1)
        data2 = self.extract_numerical_data(text2)

        comparison_results = {
            "price_similarity": self._compare_prices(data1["prices"], data2["prices"]),
            "spec_similarity": self._compare_specifications(data1["specifications"], data2["specifications"]),
            "percentage_similarity": self._compare_percentages(data1["percentages"], data2["percentages"]),
            "date_similarity": self._compare_dates(data1["dates"], data2["dates"]),
            "quantity_similarity": self._compare_quantities(data1["quantities"], data2["quantities"]),
            "overall_similarity": 0.0,
            "suspicious_matches": []
        }

        # Calculate overall similarity
        similarities = [
            comparison_results["price_similarity"]["score"],
            comparison_results["spec_similarity"]["score"],
            comparison_results["percentage_similarity"]["score"],
            comparison_results["quantity_similarity"]["score"]
        ]
        
        comparison_results["overall_similarity"] = sum(similarities) / len(similarities)

        # Collect suspicious matches
        suspicious_matches = []
        for category, result in comparison_results.items():
            if isinstance(result, dict) and "matches" in result:
                for match in result["matches"]:
                    if match["similarity"] >= self.similarity_threshold:
                        suspicious_matches.append({
                            "category": category,
                            "value1": match["value1"],
                            "value2": match["value2"],
                            "similarity": match["similarity"]
                        })

        comparison_results["suspicious_matches"] = suspicious_matches

        logger.debug(f"Numerical similarity: {comparison_results['overall_similarity']:.3f}")
        return comparison_results

    def find_numerical_similarities(
        self, 
        documents: List[str]
    ) -> List[Dict]:
        """Find numerical similarities between multiple documents.
        
        Args:
            documents: List of document texts
            
        Returns:
            List of documents with suspicious numerical similarities
        """
        similarities = []
        
        for i in range(len(documents)):
            for j in range(i + 1, len(documents)):
                comparison = self.compare_numerical_data(documents[i], documents[j])
                
                if comparison["overall_similarity"] >= self.similarity_threshold:
                    similarities.append({
                        "doc1_index": i,
                        "doc2_index": j,
                        "similarity_score": comparison["overall_similarity"],
                        "analysis_type": "numerical",
                        "suspicious_matches": comparison["suspicious_matches"],
                        "detailed_comparison": comparison
                    })
        
        # Sort by similarity score
        similarities.sort(key=lambda x: x["similarity_score"], reverse=True)
        
        return similarities

    def _extract_prices(self, text: str) -> List[Dict]:
        """Extract price information from text."""
        prices = []
        
        for pattern in self.price_patterns:
            matches = re.finditer(pattern, text, re.IGNORECASE)
            for match in matches:
                try:
                    value_str = match.group(1).replace(',', '')
                    value = float(value_str)
                    prices.append({
                        "value": value,
                        "text": match.group(0),
                        "position": match.span(),
                        "type": "price"
                    })
                except (ValueError, IndexError):
                    continue
        
        return prices

    def _extract_specifications(self, text: str) -> List[Dict]:
        """Extract specification measurements from text."""
        specs = []
        
        for pattern in self.spec_patterns:
            matches = re.finditer(pattern, text, re.IGNORECASE)
            for match in matches:
                try:
                    value = float(match.group(1))
                    unit = match.group(0).split(match.group(1))[-1].strip()
                    specs.append({
                        "value": value,
                        "unit": unit,
                        "text": match.group(0),
                        "position": match.span(),
                        "type": "specification"
                    })
                except (ValueError, IndexError):
                    continue
        
        return specs

    def _extract_percentages(self, text: str) -> List[Dict]:
        """Extract percentage values from text."""
        percentages = []
        
        # Pattern for percentages
        pattern = r'([0-9.]+)\s*%'
        matches = re.finditer(pattern, text)
        
        for match in matches:
            try:
                value = float(match.group(1))
                percentages.append({
                    "value": value,
                    "text": match.group(0),
                    "position": match.span(),
                    "type": "percentage"
                })
            except (ValueError, IndexError):
                continue
        
        return percentages

    def _extract_dates(self, text: str) -> List[Dict]:
        """Extract date information from text."""
        dates = []
        
        # Patterns for various date formats
        date_patterns = [
            r'(\d{4})\s*年\s*(\d{1,2})\s*月\s*(\d{1,2})\s*日',  # Chinese format
            r'(\d{4})/(\d{1,2})/(\d{1,2})',                      # Slash format
            r'(\d{4})-(\d{1,2})-(\d{1,2})',                      # Dash format
        ]
        
        for pattern in date_patterns:
            matches = re.finditer(pattern, text)
            for match in matches:
                dates.append({
                    "text": match.group(0),
                    "position": match.span(),
                    "type": "date"
                })
        
        return dates

    def _extract_quantities(self, text: str) -> List[Dict]:
        """Extract general quantity information from text."""
        quantities = []
        
        # Pattern for numbers followed by units
        pattern = r'([0-9,]+(?:\.[0-9]+)?)\s*([個件組批台輛棟層次回趟])'
        matches = re.finditer(pattern, text)
        
        for match in matches:
            try:
                value_str = match.group(1).replace(',', '')
                value = float(value_str)
                unit = match.group(2)
                quantities.append({
                    "value": value,
                    "unit": unit,
                    "text": match.group(0),
                    "position": match.span(),
                    "type": "quantity"
                })
            except (ValueError, IndexError):
                continue
        
        return quantities

    def _compare_prices(self, prices1: List[Dict], prices2: List[Dict]) -> Dict:
        """Compare price lists between two documents."""
        matches = []
        
        for price1 in prices1:
            for price2 in prices2:
                similarity = self._calculate_numerical_similarity(
                    price1["value"], price2["value"]
                )
                if similarity >= 0.8:  # Lower threshold for price matching
                    matches.append({
                        "value1": price1["value"],
                        "value2": price2["value"],
                        "similarity": similarity,
                        "text1": price1["text"],
                        "text2": price2["text"]
                    })
        
        avg_similarity = sum(m["similarity"] for m in matches) / len(matches) if matches else 0.0
        
        return {
            "score": avg_similarity,
            "matches": matches,
            "count1": len(prices1),
            "count2": len(prices2)
        }

    def _compare_specifications(self, specs1: List[Dict], specs2: List[Dict]) -> Dict:
        """Compare specification lists between two documents."""
        matches = []
        
        for spec1 in specs1:
            for spec2 in specs2:
                # Only compare specs with same unit
                if spec1["unit"] == spec2["unit"]:
                    similarity = self._calculate_numerical_similarity(
                        spec1["value"], spec2["value"]
                    )
                    if similarity >= 0.9:
                        matches.append({
                            "value1": spec1["value"],
                            "value2": spec2["value"],
                            "unit": spec1["unit"],
                            "similarity": similarity,
                            "text1": spec1["text"],
                            "text2": spec2["text"]
                        })
        
        avg_similarity = sum(m["similarity"] for m in matches) / len(matches) if matches else 0.0
        
        return {
            "score": avg_similarity,
            "matches": matches,
            "count1": len(specs1),
            "count2": len(specs2)
        }

    def _compare_percentages(self, percentages1: List[Dict], percentages2: List[Dict]) -> Dict:
        """Compare percentage lists between two documents."""
        matches = []
        
        for pct1 in percentages1:
            for pct2 in percentages2:
                similarity = self._calculate_numerical_similarity(
                    pct1["value"], pct2["value"]
                )
                if similarity >= 0.95:
                    matches.append({
                        "value1": pct1["value"],
                        "value2": pct2["value"],
                        "similarity": similarity,
                        "text1": pct1["text"],
                        "text2": pct2["text"]
                    })
        
        avg_similarity = sum(m["similarity"] for m in matches) / len(matches) if matches else 0.0
        
        return {
            "score": avg_similarity,
            "matches": matches,
            "count1": len(percentages1),
            "count2": len(percentages2)
        }

    def _compare_dates(self, dates1: List[Dict], dates2: List[Dict]) -> Dict:
        """Compare date lists between two documents."""
        matches = []
        
        for date1 in dates1:
            for date2 in dates2:
                # Simple text-based comparison for dates
                if date1["text"] == date2["text"]:
                    matches.append({
                        "text1": date1["text"],
                        "text2": date2["text"],
                        "similarity": 1.0
                    })
        
        avg_similarity = len(matches) / max(len(dates1), len(dates2)) if dates1 or dates2 else 0.0
        
        return {
            "score": avg_similarity,
            "matches": matches,
            "count1": len(dates1),
            "count2": len(dates2)
        }

    def _compare_quantities(self, quantities1: List[Dict], quantities2: List[Dict]) -> Dict:
        """Compare quantity lists between two documents."""
        matches = []
        
        for qty1 in quantities1:
            for qty2 in quantities2:
                # Only compare quantities with same unit
                if qty1["unit"] == qty2["unit"]:
                    similarity = self._calculate_numerical_similarity(
                        qty1["value"], qty2["value"]
                    )
                    if similarity >= 0.95:
                        matches.append({
                            "value1": qty1["value"],
                            "value2": qty2["value"],
                            "unit": qty1["unit"],
                            "similarity": similarity,
                            "text1": qty1["text"],
                            "text2": qty2["text"]
                        })
        
        avg_similarity = sum(m["similarity"] for m in matches) / len(matches) if matches else 0.0
        
        return {
            "score": avg_similarity,
            "matches": matches,
            "count1": len(quantities1),
            "count2": len(quantities2)
        }

    def _calculate_numerical_similarity(self, value1: float, value2: float) -> float:
        """Calculate similarity between two numerical values."""
        if value1 == 0 and value2 == 0:
            return 1.0
        
        if value1 == 0 or value2 == 0:
            return 0.0
        
        # Calculate relative difference
        diff = abs(value1 - value2)
        avg = (value1 + value2) / 2
        relative_diff = diff / avg
        
        # Convert to similarity (1 - relative_difference)
        similarity = max(0.0, 1.0 - relative_diff)
        
        return similarity

    def detect_price_collusion_patterns(self, documents: List[str]) -> List[Dict]:
        """Detect potential price collusion patterns.
        
        Args:
            documents: List of document texts
            
        Returns:
            List of potential collusion indicators
        """
        all_prices = []
        
        # Extract all prices from all documents
        for i, doc in enumerate(documents):
            data = self.extract_numerical_data(doc)
            for price in data["prices"]:
                all_prices.append({
                    "doc_index": i,
                    "value": price["value"],
                    "text": price["text"]
                })
        
        # Find suspiciously similar prices
        collusion_indicators = []
        
        for i in range(len(all_prices)):
            for j in range(i + 1, len(all_prices)):
                price1 = all_prices[i]
                price2 = all_prices[j]
                
                # Skip if same document
                if price1["doc_index"] == price2["doc_index"]:
                    continue
                
                similarity = self._calculate_numerical_similarity(
                    price1["value"], price2["value"]
                )
                
                if similarity >= 0.98:  # Very high similarity threshold
                    collusion_indicators.append({
                        "doc1_index": price1["doc_index"],
                        "doc2_index": price2["doc_index"],
                        "price1": price1["value"],
                        "price2": price2["value"],
                        "similarity": similarity,
                        "suspicion_level": "high" if similarity >= 0.99 else "medium"
                    })
        
        return collusion_indicators