from typing import List, Dict, Tuple, Optional
import logging
import numpy as np
from sqlalchemy.orm import Session

from ..embedding_service import embedding_service
from ...models.database import DocumentChunk

logger = logging.getLogger(__name__)


class SemanticComparison:
    """Semantic similarity analysis using Qwen3 embedding models.
    
    This class provides semantic comparison capabilities using the actual
    Qwen3-Embedding-0.6B model for document analysis.
    """

    def __init__(self):
        self.embedding_service = embedding_service
        self.similarity_threshold = 0.8
        
    def compare_texts(self, text1: str, text2: str) -> float:
        """Compare semantic similarity between two texts using Qwen3 embeddings.
        
        Args:
            text1: First text to compare
            text2: Second text to compare
            
        Returns:
            Similarity score between 0 and 1
        """
        if not text1.strip() or not text2.strip():
            return 0.0
        
        if not self.embedding_service.is_available():
            logger.warning("Embedding service not available, using fallback")
            return self._fallback_similarity(text1, text2)
        
        try:
            # Generate embeddings for both texts
            embeddings = self.embedding_service.generate_embeddings([text1, text2])
            
            # Calculate cosine similarity
            similarity_matrix = self.embedding_service.compute_similarity(
                embeddings[0:1], embeddings[1:2]
            )
            
            similarity = float(similarity_matrix[0][0])
            logger.debug(f"Semantic similarity: {similarity:.3f}")
            return similarity
            
        except Exception as e:
            logger.error(f"Embedding comparison failed: {str(e)}")
            return self._fallback_similarity(text1, text2)
    
    def _fallback_similarity(self, text1: str, text2: str) -> float:
        """Fallback similarity calculation when embedding service is unavailable."""
        words1 = set(text1.lower().split())
        words2 = set(text2.lower().split())
        
        if not words1 or not words2:
            return 0.0
        
        # Calculate Jaccard similarity
        intersection = len(words1.intersection(words2))
        union = len(words1.union(words2))
        return intersection / union if union > 0 else 0.0

    def find_similar_segments(
        self, 
        chunks1: List[str], 
        chunks2: List[str]
    ) -> List[Dict]:
        """Find semantically similar segments between two documents.
        
        Args:
            chunks1: Text chunks from first document
            chunks2: Text chunks from second document
            
        Returns:
            List of similar segment pairs with similarity scores
        """
        if not chunks1 or not chunks2:
            return []
        
        similar_segments = []
        
        try:
            if self.embedding_service.is_available():
                # Use batch embedding generation for efficiency
                all_texts = chunks1 + chunks2
                all_embeddings = self.embedding_service.generate_embeddings(all_texts)
                
                # Split embeddings back to chunks1 and chunks2
                embeddings1 = all_embeddings[:len(chunks1)]
                embeddings2 = all_embeddings[len(chunks1):]
                
                # Compute similarity matrix
                similarity_matrix = self.embedding_service.compute_similarity(
                    embeddings1, embeddings2
                )
                
                # Find pairs above threshold
                for i, chunk1 in enumerate(chunks1):
                    for j, chunk2 in enumerate(chunks2):
                        similarity = float(similarity_matrix[i][j])
                        
                        if similarity >= self.similarity_threshold:
                            similar_segments.append({
                                "chunk1_index": i,
                                "chunk2_index": j,
                                "chunk1_content": chunk1[:200] + "..." if len(chunk1) > 200 else chunk1,
                                "chunk2_content": chunk2[:200] + "..." if len(chunk2) > 200 else chunk2,
                                "similarity_score": similarity,
                                "analysis_type": "semantic"
                            })
            else:
                # Fallback to individual comparisons
                for i, chunk1 in enumerate(chunks1):
                    for j, chunk2 in enumerate(chunks2):
                        similarity = self.compare_texts(chunk1, chunk2)
                        
                        if similarity >= self.similarity_threshold:
                            similar_segments.append({
                                "chunk1_index": i,
                                "chunk2_index": j,
                                "chunk1_content": chunk1[:200] + "..." if len(chunk1) > 200 else chunk1,
                                "chunk2_content": chunk2[:200] + "..." if len(chunk2) > 200 else chunk2,
                                "similarity_score": similarity,
                                "analysis_type": "semantic"
                            })
        
        except Exception as e:
            logger.error(f"Segment comparison failed: {str(e)}")
            return []
        
        # Sort by similarity score (highest first)
        similar_segments.sort(key=lambda x: x["similarity_score"], reverse=True)
        
        return similar_segments

    def batch_compare(self, documents: List[str]) -> Dict[Tuple[int, int], float]:
        """Compare all document pairs in a batch.
        
        Args:
            documents: List of document texts
            
        Returns:
            Dictionary mapping (doc1_index, doc2_index) to similarity score
        """
        results = {}
        
        for i in range(len(documents)):
            for j in range(i + 1, len(documents)):
                similarity = self.compare_texts(documents[i], documents[j])
                results[(i, j)] = similarity
                
        return results

    def get_embeddings(self, text: str) -> List[float]:
        """Get embedding vector for text using Qwen3 model.
        
        Args:
            text: Input text
            
        Returns:
            Embedding vector as list of floats
        """
        if not self.embedding_service.is_available():
            logger.warning("Embedding service not available")
            return []
        
        try:
            embeddings = self.embedding_service.generate_embeddings([text])
            return embeddings[0].tolist() if len(embeddings) > 0 else []
        except Exception as e:
            logger.error(f"Embedding generation failed: {str(e)}")
            return []

    def calculate_cosine_similarity(self, vec1: List[float], vec2: List[float]) -> float:
        """Calculate cosine similarity between two vectors."""
        if len(vec1) != len(vec2):
            return 0.0
        
        # Convert to numpy arrays for calculation
        v1 = np.array(vec1)
        v2 = np.array(vec2)
        
        # Calculate cosine similarity
        dot_product = np.dot(v1, v2)
        norm1 = np.linalg.norm(v1)
        norm2 = np.linalg.norm(v2)
        
        if norm1 == 0 or norm2 == 0:
            return 0.0
        
        return dot_product / (norm1 * norm2)

    def is_available(self) -> bool:
        """Check if semantic comparison service is available."""
        return self.embedding_service.is_available()

    def get_model_info(self) -> Dict:
        """Get information about the embedding model."""
        if self.embedding_service.is_available():
            return self.embedding_service.get_model_info()
        else:
            return {
                "model_name": "unavailable",
                "status": "error",
                "note": "Embedding service is not available"
            }

    def compare_document_chunks(
        self,
        document1_id: str,
        document2_id: str,
        db: Session,
        threshold: Optional[float] = None
    ) -> List[Dict]:
        """Compare chunks between two documents using semantic similarity.
        
        Args:
            document1_id: First document ID
            document2_id: Second document ID
            db: Database session
            threshold: Custom similarity threshold (uses default if None)
            
        Returns:
            List of similar chunk pairs with detailed information
        """
        if threshold is not None:
            original_threshold = self.similarity_threshold
            self.similarity_threshold = threshold
        
        try:
            # Use the embedding service's chunk comparison method
            similar_pairs = self.embedding_service.compare_document_chunks(
                document1_id, document2_id, self.similarity_threshold, db
            )
            
            # Format results for semantic analysis
            formatted_results = []
            for pair in similar_pairs:
                formatted_results.append({
                    "chunk1_id": pair["chunk1_id"],
                    "chunk2_id": pair["chunk2_id"],
                    "chunk1_content": pair["chunk1_content"],
                    "chunk2_content": pair["chunk2_content"],
                    "similarity_score": pair["similarity_score"],
                    "chunk1_page": pair["chunk1_page"],
                    "chunk2_page": pair["chunk2_page"],
                    "chunk1_bbox": pair["chunk1_bbox"],
                    "chunk2_bbox": pair["chunk2_bbox"],
                    "analysis_type": "semantic",
                    "finding_type": "text_semantic_similarity",
                    "risk_level": self._determine_risk_level(pair["similarity_score"])
                })
            
            return formatted_results
            
        except Exception as e:
            logger.error(f"Document chunk comparison failed: {str(e)}")
            return []
        
        finally:
            # Restore original threshold if changed
            if threshold is not None:
                self.similarity_threshold = original_threshold
    
    def _determine_risk_level(self, similarity_score: float) -> str:
        """Determine risk level based on similarity score."""
        if similarity_score >= 0.95:
            return "critical"
        elif similarity_score >= 0.85:
            return "high"
        elif similarity_score >= 0.7:
            return "medium"
        else:
            return "low"
    
    def preprocess_for_embedding(self, text: str) -> str:
        """Preprocess text for embedding generation."""
        if not text:
            return ""
        
        # Basic preprocessing
        text = text.strip()
        
        # Remove excessive whitespace
        import re
        text = re.sub(r'\s+', ' ', text)
        
        return text