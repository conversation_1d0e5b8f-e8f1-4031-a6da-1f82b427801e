"""
Embedding Service for TraceFast

This service handles text embedding generation using the Qwen3-Embedding-0.6B model
for semantic similarity analysis of procurement documents.
"""

from typing import List, Dict, Optional, Any, Tuple
import logging
import numpy as np
import torch
import torch.nn.functional as F
from sentence_transformers import SentenceTransformer
from sqlalchemy.orm import Session
from sqlalchemy import text
import time

from ..models.database import DocumentChunk, AnalysisResult
from ..core.database import get_db

logger = logging.getLogger(__name__)


class EmbeddingService:
    """Service for generating and managing document embeddings using Qwen3 model."""

    def __init__(self):
        self.model = None
        self.model_name = "Qwen/Qwen3-Embedding-0.6B"
        self.device = self._get_optimal_device()
        self._initialize_model()
    
    def _get_optimal_device(self) -> str:
        """Determine the best device to use for inference."""
        if torch.cuda.is_available():
            return "cuda"
        elif hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
            return "mps"
        else:
            return "cpu"
    
    def _initialize_model(self):
        """Initialize the Qwen3 embedding model."""
        try:
            logger.info(f"Initializing {self.model_name} on {self.device}...")
            start_time = time.time()
            
            self.model = SentenceTransformer(self.model_name)
            
            # Move model to optimal device if needed
            if self.device != "cpu":
                try:
                    self.model = self.model.to(self.device)
                except Exception as e:
                    logger.warning(f"Could not move model to {self.device}, using CPU: {e}")
                    self.device = "cpu"
            
            load_time = time.time() - start_time
            logger.info(f"✅ Embedding model loaded in {load_time:.2f} seconds on {self.device}")
            
        except Exception as e:
            logger.error(f"Failed to initialize embedding model: {str(e)}")
            raise RuntimeError(f"Embedding service initialization failed: {str(e)}")
    
    def generate_embeddings(self, texts: List[str], is_query: bool = False, batch_size: int = 32) -> np.ndarray:
        """Generate embeddings for a list of texts.
        
        Args:
            texts: List of texts to embed
            is_query: Whether these are query texts (uses different prompt)
            batch_size: Batch size for processing
            
        Returns:
            NumPy array of embeddings with shape (n_texts, embedding_dim)
        """
        if not self.model:
            raise RuntimeError("Embedding model not initialized")
        
        if not texts:
            return np.array([])
        
        try:
            logger.info(f"Generating embeddings for {len(texts)} texts (query={is_query})")
            start_time = time.time()
            
            # Process in batches if needed
            all_embeddings = []
            
            for i in range(0, len(texts), batch_size):
                batch_texts = texts[i:i + batch_size]
                logger.debug(f"Processing batch {i//batch_size + 1}/{(len(texts) + batch_size - 1)//batch_size}")
                
                if is_query:
                    # Use query prompt for query texts
                    batch_embeddings = self.model.encode(batch_texts, prompt_name="query")
                else:
                    # Use default encoding for document texts
                    batch_embeddings = self.model.encode(batch_texts)
                
                all_embeddings.append(batch_embeddings)
            
            # Concatenate all batch results
            embeddings = np.vstack(all_embeddings) if len(all_embeddings) > 1 else all_embeddings[0]
            
            embedding_time = time.time() - start_time
            logger.info(f"✅ Generated {embeddings.shape[0]} embeddings of dimension {embeddings.shape[1]} in {embedding_time:.2f}s")
            
            return embeddings
            
        except Exception as e:
            logger.error(f"Failed to generate embeddings: {str(e)}")
            raise RuntimeError(f"Embedding generation failed: {str(e)}")
    
    def compute_similarity(self, embeddings1: np.ndarray, embeddings2: np.ndarray) -> np.ndarray:
        """Compute cosine similarity between two sets of embeddings.
        
        Args:
            embeddings1: First set of embeddings (n1, dim)
            embeddings2: Second set of embeddings (n2, dim)
            
        Returns:
            Similarity matrix of shape (n1, n2)
        """
        try:
            # Convert to tensors
            tensor1 = torch.from_numpy(embeddings1).float()
            tensor2 = torch.from_numpy(embeddings2).float()
            
            # Compute cosine similarity
            similarity = F.cosine_similarity(
                tensor1.unsqueeze(1), 
                tensor2.unsqueeze(0), 
                dim=2
            )
            
            return similarity.numpy()
            
        except Exception as e:
            logger.error(f"Failed to compute similarity: {str(e)}")
            raise RuntimeError(f"Similarity computation failed: {str(e)}")
    
    def find_similar_chunks(
        self, 
        query_text: str, 
        document_id: Optional[str] = None,
        project_id: Optional[str] = None,
        threshold: float = 0.8,
        limit: int = 10,
        db: Optional[Session] = None
    ) -> List[Dict[str, Any]]:
        """Find similar document chunks using semantic similarity.
        
        Args:
            query_text: Text to find similar chunks for
            document_id: Limit search to specific document
            project_id: Limit search to specific project
            threshold: Minimum similarity threshold
            limit: Maximum number of results
            db: Database session
            
        Returns:
            List of similar chunks with similarity scores
        """
        if not db:
            raise ValueError("Database session required")
        
        try:
            # Generate query embedding
            query_embedding = self.generate_embeddings([query_text], is_query=True)[0]
            
            # Get chunks to search through
            query_filter = db.query(DocumentChunk)
            
            if document_id:
                query_filter = query_filter.filter(DocumentChunk.document_id == document_id)
            elif project_id:
                from ..models.database import Document
                query_filter = query_filter.join(Document).filter(Document.project_id == project_id)
            
            chunks = query_filter.all()
            
            if not chunks:
                logger.info("No chunks found for similarity search")
                return []
            
            # Extract texts and generate embeddings
            chunk_texts = [chunk.content for chunk in chunks if chunk.content]
            
            if not chunk_texts:
                logger.info("No text content found in chunks")
                return []
            
            chunk_embeddings = self.generate_embeddings(chunk_texts)
            
            # Compute similarities
            similarities = self.compute_similarity(
                query_embedding.reshape(1, -1), 
                chunk_embeddings
            )[0]  # Get first row since query is single embedding
            
            # Find chunks above threshold
            results = []
            for i, (chunk, similarity) in enumerate(zip(chunks, similarities)):
                if similarity >= threshold:
                    results.append({
                        "chunk_id": str(chunk.id),
                        "document_id": str(chunk.document_id),
                        "content": chunk.content,
                        "page_number": chunk.page_number,
                        "chunk_type": chunk.chunk_type.value,
                        "bounding_box": chunk.bounding_box,
                        "similarity_score": float(similarity)
                    })
            
            # Sort by similarity and limit results
            results.sort(key=lambda x: x["similarity_score"], reverse=True)
            results = results[:limit]
            
            logger.info(f"Found {len(results)} similar chunks above threshold {threshold}")
            return results
            
        except Exception as e:
            logger.error(f"Failed to find similar chunks: {str(e)}")
            raise RuntimeError(f"Similar chunks search failed: {str(e)}")
    
    def compare_document_chunks(
        self,
        document1_id: str,
        document2_id: str,
        similarity_threshold: float = 0.8,
        db: Optional[Session] = None
    ) -> List[Dict[str, Any]]:
        """Compare chunks between two documents to find similarities.
        
        Args:
            document1_id: First document ID
            document2_id: Second document ID
            similarity_threshold: Minimum similarity to report
            db: Database session
            
        Returns:
            List of similar chunk pairs with detailed information
        """
        if not db:
            raise ValueError("Database session required")
        
        try:
            # Get chunks from both documents
            chunks1 = db.query(DocumentChunk).filter(
                DocumentChunk.document_id == document1_id
            ).all()
            
            chunks2 = db.query(DocumentChunk).filter(
                DocumentChunk.document_id == document2_id
            ).all()
            
            if not chunks1 or not chunks2:
                logger.warning(f"No chunks found for comparison between {document1_id} and {document2_id}")
                return []
            
            # Extract texts
            texts1 = [chunk.content for chunk in chunks1 if chunk.content]
            texts2 = [chunk.content for chunk in chunks2 if chunk.content]
            
            if not texts1 or not texts2:
                logger.warning("No text content found for comparison")
                return []
            
            # Generate embeddings
            embeddings1 = self.generate_embeddings(texts1)
            embeddings2 = self.generate_embeddings(texts2)
            
            # Compute similarity matrix
            similarity_matrix = self.compute_similarity(embeddings1, embeddings2)
            
            # Find high similarity pairs
            similar_pairs = []
            
            for i, chunk1 in enumerate(chunks1):
                if not chunk1.content:
                    continue
                    
                for j, chunk2 in enumerate(chunks2):
                    if not chunk2.content:
                        continue
                    
                    # Find the corresponding indices in the text arrays
                    text1_idx = [idx for idx, c in enumerate(chunks1) if c.content and c.id == chunk1.id]
                    text2_idx = [idx for idx, c in enumerate(chunks2) if c.content and c.id == chunk2.id]
                    
                    if not text1_idx or not text2_idx:
                        continue
                    
                    similarity = similarity_matrix[text1_idx[0]][text2_idx[0]]
                    
                    if similarity >= similarity_threshold:
                        similar_pairs.append({
                            "chunk1_id": str(chunk1.id),
                            "chunk2_id": str(chunk2.id),
                            "document1_id": document1_id,
                            "document2_id": document2_id,
                            "similarity_score": float(similarity),
                            "chunk1_content": chunk1.content[:200] + "..." if len(chunk1.content) > 200 else chunk1.content,
                            "chunk2_content": chunk2.content[:200] + "..." if len(chunk2.content) > 200 else chunk2.content,
                            "chunk1_page": chunk1.page_number,
                            "chunk2_page": chunk2.page_number,
                            "chunk1_type": chunk1.chunk_type.value,
                            "chunk2_type": chunk2.chunk_type.value,
                            "chunk1_bbox": chunk1.bounding_box,
                            "chunk2_bbox": chunk2.bounding_box
                        })
            
            # Sort by similarity score
            similar_pairs.sort(key=lambda x: x["similarity_score"], reverse=True)
            
            logger.info(f"Found {len(similar_pairs)} similar chunk pairs above threshold {similarity_threshold}")
            return similar_pairs
            
        except Exception as e:
            logger.error(f"Failed to compare document chunks: {str(e)}")
            raise RuntimeError(f"Document chunk comparison failed: {str(e)}")
    
    def generate_chunk_embeddings_batch(
        self,
        document_ids: List[str],
        db: Optional[Session] = None,
        update_existing: bool = False
    ) -> Dict[str, Any]:
        """Generate embeddings for all chunks in specified documents.
        
        Args:
            document_ids: List of document IDs to process
            db: Database session
            update_existing: Whether to regenerate existing embeddings
            
        Returns:
            Summary of processing results
        """
        if not db:
            raise ValueError("Database session required")
        
        results = {
            "processed_documents": 0,
            "generated_embeddings": 0,
            "skipped_chunks": 0,
            "errors": []
        }
        
        try:
            for doc_id in document_ids:
                try:
                    # Convert string document_id to UUID for database query
                    import uuid as uuid_lib
                    try:
                        document_uuid = uuid_lib.UUID(doc_id)
                    except (ValueError, TypeError):
                        logger.error(f"Invalid document ID format: {doc_id}")
                        errors.append(f"Invalid document ID format: {doc_id}")
                        continue
                    
                    # Get chunks for this document
                    query = db.query(DocumentChunk).filter(DocumentChunk.document_id == document_uuid)
                    
                    if not update_existing:
                        # Skip chunks that already have embeddings (if we had embedding column)
                        # For now, we'll process all chunks
                        pass
                    
                    chunks = query.all()
                    
                    if not chunks:
                        logger.info(f"No chunks found for document {doc_id}")
                        continue
                    
                    # Extract texts that have content
                    chunks_with_text = [chunk for chunk in chunks if chunk.content and chunk.content.strip()]
                    
                    if not chunks_with_text:
                        logger.info(f"No text content in chunks for document {doc_id}")
                        continue
                    
                    # Generate embeddings
                    texts = [chunk.content for chunk in chunks_with_text]
                    embeddings = self.generate_embeddings(texts)
                    
                    # Store embeddings (for now, we'll log this since we don't have vector storage yet)
                    logger.info(f"Generated {len(embeddings)} embeddings for document {doc_id}")
                    # TODO: Store embeddings in database when pgvector is enabled
                    
                    results["generated_embeddings"] += len(embeddings)
                    results["processed_documents"] += 1
                    
                except Exception as e:
                    error_msg = f"Failed to process document {doc_id}: {str(e)}"
                    logger.error(error_msg)
                    results["errors"].append(error_msg)
            
            logger.info(f"Batch embedding generation completed: {results}")
            return results
            
        except Exception as e:
            logger.error(f"Batch embedding generation failed: {str(e)}")
            raise RuntimeError(f"Batch embedding generation failed: {str(e)}")
    
    def is_available(self) -> bool:
        """Check if embedding service is available."""
        return self.model is not None
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get information about the embedding model."""
        return {
            "model_name": self.model_name,
            "status": "active" if self.model else "error",
            "device": self.device,
            "embedding_dimension": 896,  # Qwen3-Embedding-0.6B dimension
            "max_sequence_length": 8192,
            "supported_languages": ["en", "zh-CN", "zh-TW", "multilingual"],
            "capabilities": [
                "text_embedding",
                "semantic_similarity",
                "cross_lingual_retrieval",
                "query_document_matching"
            ]
        }


# Singleton instance for global use
embedding_service = EmbeddingService()