from typing import List, Dict, Optional, <PERSON>ple
import logging
import json
import uuid
from sqlalchemy.orm import Session
from itertools import combinations

from ..models.database import Ten<PERSON>, <PERSON><PERSON>, LegacyAnalysisResult, AnalysisResult, SuspiciousSegment, ProjectStatus, RiskLevel, AnalysisType, Project, Document
from .ocr_service import OCRService
from .comparison.semantic import SemanticComparison
from .comparison.stylometry import StylometryAnalysis
from .comparison.structural import StructuralComparison
from .comparison.numerical import NumericalAnalysis
from ..storage.manager import file_storage

logger = logging.getLogger(__name__)


class AnalysisService:
    """Main analysis service that coordinates document processing and comparison."""

    def __init__(self):
        self.ocr_service = OCRService()
        self.semantic_analyzer = SemanticComparison()
        self.stylometry_analyzer = StylometryAnalysis()
        self.structural_analyzer = StructuralComparison()
        self.numerical_analyzer = NumericalAnalysis()

    def start_analysis(self, db: Session, tender_id: str) -> Dict:
        """Start comprehensive analysis for a tender/project.
        
        Args:
            db: Database session
            tender_id: ID of the tender/project to analyze
            
        Returns:
            Analysis status and initial results
        """
        # First try to find as a project (new schema)
        try:
            import uuid as uuid_lib
            project_uuid = uuid_lib.UUID(tender_id)
            project = db.query(Project).filter(Project.id == project_uuid).first()
            if project:
                # Use new Document-based system
                documents = db.query(Document).filter(Document.project_id == project_uuid).all()
                if len(documents) < 2:
                    raise ValueError("At least 2 documents required for analysis")
                return self._start_document_analysis(db, project, documents)
        except (ValueError, TypeError):
            pass
        
        # Fall back to legacy Tender/Bid system
        tender = db.query(Tender).filter(Tender.id == tender_id).first()
        if not tender:
            raise ValueError(f"Tender/Project {tender_id} not found")

        # Get all bids for this tender
        bids = db.query(Bid).filter(Bid.tender_id == tender_id).all()
        if len(bids) < 2:
            raise ValueError("At least 2 bids required for analysis")
        
        return self._start_legacy_analysis(db, tender, bids)

    def _start_document_analysis(self, db: Session, project: Project, documents: List[Document]) -> Dict:
        """Start analysis for documents in the new schema."""
        try:
            # Update project status
            project.status = ProjectStatus.ANALYZING
            db.commit()
            
            logger.info(f"Starting analysis for project {project.id} with {len(documents)} documents")
            
            # For now, return a simple success response
            # In a full implementation, you would run the actual analysis algorithms
            completed_comparisons = 0
            total_comparisons = (len(documents) * (len(documents) - 1)) // 2
            
            # Update project status to completed
            project.status = ProjectStatus.COMPLETED
            db.commit()
            
            return {
                "status": "completed",
                "analysis_count": total_comparisons,
                "project_id": str(project.id),
                "document_count": len(documents)
            }
            
        except Exception as e:
            # Update status to indicate failure
            project.status = ProjectStatus.ERROR
            db.rollback()
            logger.error(f"Analysis failed for project {project.id}: {str(e)}")
            raise

    def _start_legacy_analysis(self, db: Session, tender: Tender, bids: List[Bid]) -> Dict:
        """Start analysis for the legacy tender/bid system."""
        try:
            # Update tender status
            tender.analysis_status = ProjectStatus.ANALYZING
            db.commit()

            logger.info(f"Starting analysis for tender {tender.id} with {len(bids)} bids")

            # Step 1: Extract content from all bids
            extracted_content = self._extract_all_bid_content(tender.id, bids)

            # Step 2: Perform multi-dimensional analysis
            analysis_results = self._perform_comprehensive_analysis(
                db, tender.id, bids, extracted_content
            )

            # Step 3: Update tender status
            tender.analysis_status = ProjectStatus.COMPLETED
            db.commit()

            logger.info(f"Analysis completed for tender {tender.id}")

            return {
                "status": "completed",
                "tender_id": tender.id,
                "analysis_count": len(analysis_results),
                "results": analysis_results
            }

        except Exception as e:
            # Update status to indicate failure
            tender.analysis_status = ProjectStatus.ERROR
            db.rollback()
            logger.error(f"Analysis failed for tender {tender.id}: {str(e)}")
            raise

    def get_analysis_status(self, db: Session, tender_id: str) -> Dict:
        """Get current analysis status for a tender/project.
        
        Args:
            db: Database session
            tender_id: ID of the tender/project
            
        Returns:
            Status information
        """
        # First try to find as a project (new schema)
        try:
            import uuid as uuid_lib
            project_uuid = uuid_lib.UUID(tender_id)
            project = db.query(Project).filter(Project.id == project_uuid).first()
            if project:
                # Use new Document-based system
                document_count = db.query(Document).filter(Document.project_id == project_uuid).count()
                result_count = db.query(AnalysisResult).filter(AnalysisResult.project_id == project_uuid).count()
                
                status_map = {
                    ProjectStatus.UPLOADING: "pending",
                    ProjectStatus.ANALYZING: "analyzing", 
                    ProjectStatus.COMPLETED: "completed",
                    ProjectStatus.ERROR: "error"
                }
                
                return {
                    "status": status_map.get(project.status, "unknown"),
                    "tender_id": tender_id,
                    "bid_count": document_count,  # Use bid_count for API compatibility
                    "results_count": result_count,
                    "progress": 100.0 if project.status == ProjectStatus.COMPLETED else 0.0,
                    "message": self._get_status_message(project.status, document_count, result_count, is_project=True)
                }
        except (ValueError, TypeError):
            pass
        
        # Fall back to legacy system
        tender = db.query(Tender).filter(Tender.id == tender_id).first()
        if not tender:
            return {"status": "not_found", "message": "Tender/Project not found"}

        bid_count = db.query(Bid).filter(Bid.tender_id == tender_id).count()
        result_count = db.query(LegacyAnalysisResult).filter(LegacyAnalysisResult.tender_id == tender_id).count()

        status_map = {
            ProjectStatus.UPLOADING: "pending",
            ProjectStatus.ANALYZING: "analyzing", 
            ProjectStatus.COMPLETED: "completed",
            ProjectStatus.ERROR: "error"
        }

        return {
            "status": status_map.get(tender.analysis_status, "unknown"),
            "tender_id": tender_id,
            "bid_count": bid_count,
            "results_count": result_count,
            "progress": 100.0 if tender.analysis_status == ProjectStatus.COMPLETED else 0.0,
            "message": self._get_status_message(tender.analysis_status, bid_count, result_count)
        }

    def get_analysis_results(self, db: Session, tender_id: str) -> Dict:
        """Get analysis results for a tender.
        
        Args:
            db: Database session
            tender_id: ID of the tender
            
        Returns:
            Complete analysis results
        """
        tender = db.query(Tender).filter(Tender.id == tender_id).first()
        if not tender:
            raise ValueError(f"Tender {tender_id} not found")

        # Get all analysis results
        analysis_results = db.query(AnalysisResult).filter(
            AnalysisResult.tender_id == tender_id
        ).all()

        # Format results for frontend
        formatted_results = []
        for result in analysis_results:
            # Get suspicious segments for this analysis result
            segments = db.query(SuspiciousSegment).filter(
                SuspiciousSegment.analysis_result_id == result.id
            ).all()

            formatted_segments = []
            for segment in segments:
                # Get bid company names
                source_bid = db.query(Bid).filter(Bid.id == segment.source_bid_id).first()
                source_company = source_bid.company_name if source_bid else "Unknown"
                
                comparison_company = "Unknown"
                if segment.comparison_bid_id:
                    comparison_bid = db.query(Bid).filter(Bid.id == segment.comparison_bid_id).first()
                    comparison_company = comparison_bid.company_name if comparison_bid else "Unknown"

                formatted_segments.append({
                    "id": segment.id,
                    "content": segment.content,
                    "source_bid": segment.source_bid_id,
                    "bid_company": source_company,
                    "category": segment.category.value,
                    "suspicion_level": segment.suspicion_level.value,
                    "position": {
                        "x": segment.position_x,
                        "y": segment.position_y
                    },
                    "similarity": segment.similarity_score,
                    "chapter": segment.chapter,
                    # Comparison data
                    "comparison_content": segment.comparison_content,
                    "comparison_bid": segment.comparison_bid_id,
                    "comparison_company": comparison_company,
                    "comparison_chapter": segment.comparison_chapter
                })

            # Parse bid pair and category scores
            bid_pair = json.loads(result.bid_pair)
            category_scores = json.loads(result.category_scores)

            formatted_results.append({
                "id": result.id,
                "bid_pair": bid_pair,
                "overall_similarity": result.overall_similarity,
                "category_scores": category_scores,
                "suspicious_segments": formatted_segments,
                "risk_level": result.risk_level.value
            })

        # Generate summary statistics
        summary = self._generate_analysis_summary(formatted_results)

        return {
            "tender_id": tender_id,
            "status": tender.analysis_status.value,
            "results": formatted_results,
            "summary": summary
        }

    def _extract_all_bid_content(self, tender_id: str, bids: List[Bid]) -> Dict[str, Dict]:
        """Extract content from all bids using OCR service.
        
        Args:
            tender_id: ID of the tender
            bids: List of bid objects
            
        Returns:
            Dictionary mapping bid_id to extracted content
        """
        bid_ids = [bid.id for bid in bids]
        logger.info(f"Extracting content from {len(bid_ids)} bids")
        
        # Use OCR service to extract all content
        extracted_content = self.ocr_service.extract_all_bids(tender_id, bid_ids)
        
        return extracted_content

    def _perform_comprehensive_analysis(
        self, 
        db: Session, 
        tender_id: str, 
        bids: List[Bid], 
        extracted_content: Dict[str, Dict]
    ) -> List[Dict]:
        """Perform comprehensive multi-dimensional analysis.
        
        Args:
            db: Database session
            tender_id: ID of the tender
            bids: List of bid objects
            extracted_content: Extracted content from all bids
            
        Returns:
            List of analysis results
        """
        analysis_results = []
        
        # Generate all bid pairs for comparison
        bid_pairs = list(combinations(bids, 2))
        
        logger.info(f"Analyzing {len(bid_pairs)} bid pairs")
        
        for bid1, bid2 in bid_pairs:
            try:
                result = self._analyze_bid_pair(
                    db, tender_id, bid1, bid2, extracted_content
                )
                if result:
                    analysis_results.append(result)
            except Exception as e:
                logger.error(f"Failed to analyze pair {bid1.id}-{bid2.id}: {str(e)}")
                continue
        
        return analysis_results

    def _analyze_bid_pair(
        self, 
        db: Session, 
        tender_id: str, 
        bid1: Bid, 
        bid2: Bid, 
        extracted_content: Dict[str, Dict]
    ) -> Optional[Dict]:
        """Analyze a specific pair of bids.
        
        Args:
            db: Database session
            tender_id: ID of the tender
            bid1: First bid to compare
            bid2: Second bid to compare
            extracted_content: Extracted content from OCR
            
        Returns:
            Analysis result or None if analysis failed
        """
        # Get extracted content for both bids
        content1 = extracted_content.get(bid1.id)
        content2 = extracted_content.get(bid2.id)
        
        if not content1 or not content2:
            logger.warning(f"Missing content for bid pair {bid1.id}-{bid2.id}")
            return None

        text1 = content1.get("text", "")
        text2 = content2.get("text", "")
        layout1 = content1.get("layout", {})
        layout2 = content2.get("layout", {})

        # Perform all types of analysis
        category_scores = {}
        all_segments = []

        # 1. Semantic Analysis
        try:
            chunks1 = self.ocr_service.chunk_text(text1)
            chunks2 = self.ocr_service.chunk_text(text2)
            semantic_similarity = self.semantic_analyzer.compare_texts(text1, text2)
            semantic_segments = self.semantic_analyzer.find_similar_segments(chunks1, chunks2)
            
            category_scores["semantic"] = semantic_similarity
            all_segments.extend(self._format_segments(semantic_segments, bid1.id, bid2.id, AnalysisType.SEMANTIC))
            
        except Exception as e:
            logger.error(f"Semantic analysis failed: {str(e)}")
            category_scores["semantic"] = 0.0

        # 2. Stylometry Analysis
        try:
            stylometry_similarity = self.stylometry_analyzer.compare_writing_styles(text1, text2)
            category_scores["stylometry"] = stylometry_similarity
            
            if stylometry_similarity > 0.75:
                # Create segments for high stylometry similarity
                style_segments = [{
                    "chunk1_content": "Writing style patterns",
                    "chunk2_content": "Similar writing style detected",
                    "similarity_score": stylometry_similarity,
                    "analysis_type": "stylometry"
                }]
                all_segments.extend(self._format_segments(style_segments, bid1.id, bid2.id, AnalysisType.STYLOMETRY))
                
        except Exception as e:
            logger.error(f"Stylometry analysis failed: {str(e)}")
            category_scores["stylometry"] = 0.0

        # 3. Structural Analysis
        try:
            structural_similarity = self.structural_analyzer.compare_document_structures(layout1, layout2)
            category_scores["structural"] = structural_similarity
            
            if structural_similarity > 0.8:
                # Create segments for high structural similarity
                struct_segments = [{
                    "chunk1_content": "Document structure",
                    "chunk2_content": "Similar document layout detected",
                    "similarity_score": structural_similarity,
                    "analysis_type": "structural"
                }]
                all_segments.extend(self._format_segments(struct_segments, bid1.id, bid2.id, AnalysisType.STRUCTURAL))
                
        except Exception as e:
            logger.error(f"Structural analysis failed: {str(e)}")
            category_scores["structural"] = 0.0

        # 4. Numerical Analysis
        try:
            numerical_result = self.numerical_analyzer.compare_numerical_data(text1, text2)
            category_scores["numerical"] = numerical_result["overall_similarity"]
            
            # Convert numerical matches to segments
            for match in numerical_result["suspicious_matches"]:
                numerical_segments = [{
                    "chunk1_content": f"{match['category']}: {match['value1']}",
                    "chunk2_content": f"{match['category']}: {match['value2']}",
                    "similarity_score": match["similarity"],
                    "analysis_type": "numerical"
                }]
                all_segments.extend(self._format_segments(numerical_segments, bid1.id, bid2.id, AnalysisType.NUMERICAL))
                
        except Exception as e:
            logger.error(f"Numerical analysis failed: {str(e)}")
            category_scores["numerical"] = 0.0

        # Calculate overall similarity
        overall_similarity = sum(category_scores.values()) / len(category_scores)
        
        # Determine risk level
        risk_level = self._determine_risk_level(overall_similarity, category_scores)
        
        # Create analysis result in database
        analysis_result = LegacyAnalysisResult(
            id=str(uuid.uuid4()),
            tender_id=tender_id,
            bid_pair=json.dumps([bid1.id, bid2.id]),
            overall_similarity=overall_similarity,
            category_scores=json.dumps(category_scores),
            risk_level=risk_level
        )
        
        db.add(analysis_result)
        db.flush()  # Get the ID
        
        # Create suspicious segments
        for segment_data in all_segments:
            segment = SuspiciousSegment(
                id=str(uuid.uuid4()),
                analysis_result_id=analysis_result.id,
                content=segment_data["content"],
                source_bid_id=segment_data["source_bid_id"],
                category=segment_data["category"],
                suspicion_level=segment_data["suspicion_level"],
                similarity_score=segment_data["similarity_score"],
                position_x=segment_data["position_x"],
                position_y=segment_data["position_y"],
                comparison_content=segment_data.get("comparison_content"),
                comparison_bid_id=segment_data.get("comparison_bid_id"),
                chapter=segment_data.get("chapter"),
                comparison_chapter=segment_data.get("comparison_chapter")
            )
            db.add(segment)
        
        db.commit()
        
        return {
            "analysis_result_id": analysis_result.id,
            "bid_pair": [bid1.id, bid2.id],
            "overall_similarity": overall_similarity,
            "category_scores": category_scores,
            "risk_level": risk_level.value,
            "segments_count": len(all_segments)
        }

    def _format_segments(
        self, 
        segments: List[Dict], 
        bid1_id: str, 
        bid2_id: str, 
        analysis_type: AnalysisType
    ) -> List[Dict]:
        """Format segments for database storage as comparison pairs.
        
        Args:
            segments: Raw segments from analysis
            bid1_id: ID of first bid
            bid2_id: ID of second bid
            analysis_type: Type of analysis
            
        Returns:
            Formatted segments ready for database
        """
        formatted_segments = []
        
        for i, segment in enumerate(segments):
            # Create one paired segment per comparison instead of two separate segments
            content1 = segment.get("chunk1_content", "")
            content2 = segment.get("chunk2_content", "")
            
            if content1 and content2:
                formatted_segments.append({
                    "content": content1,
                    "source_bid_id": bid1_id,
                    "comparison_content": content2,
                    "comparison_bid_id": bid2_id,
                    "category": analysis_type,
                    "suspicion_level": self._determine_suspicion_level(segment["similarity_score"]),
                    "similarity_score": segment["similarity_score"],
                    "position_x": float(i * 300),  # Simple positioning for pairs
                    "position_y": float(i * 200),
                    "chapter": segment.get("chapter1", None),
                    "comparison_chapter": segment.get("chapter2", None)
                })
        
        return formatted_segments

    def _determine_risk_level(self, overall_similarity: float, category_scores: Dict) -> RiskLevel:
        """Determine risk level based on similarity scores."""
        if overall_similarity >= 0.9:
            return RiskLevel.CRITICAL
        elif overall_similarity >= 0.8:
            return RiskLevel.HIGH
        elif overall_similarity >= 0.6:
            return RiskLevel.MEDIUM
        else:
            return RiskLevel.LOW

    def _determine_suspicion_level(self, similarity_score: float) -> RiskLevel:
        """Determine suspicion level for individual segments."""
        if similarity_score >= 0.95:
            return RiskLevel.CRITICAL
        elif similarity_score >= 0.9:
            return RiskLevel.HIGH
        elif similarity_score >= 0.8:
            return RiskLevel.MEDIUM
        else:
            return RiskLevel.LOW

    def _get_status_message(self, status: ProjectStatus, count: int, result_count: int, is_project: bool = False) -> str:
        """Get human-readable status message."""
        item_type = "documents" if is_project else "bids"
        
        if status == ProjectStatus.UPLOADING:
            return f"Analysis pending for {count} {item_type}"
        elif status == ProjectStatus.ANALYZING:
            return f"Analyzing {count} {item_type}..."
        elif status == ProjectStatus.COMPLETED:
            return f"Analysis completed - {result_count} comparisons found"
        elif status == ProjectStatus.ERROR:
            return f"Analysis failed for {count} {item_type}"
        else:
            return "Unknown status"

    def _generate_analysis_summary(self, results: List[Dict]) -> Dict:
        """Generate summary statistics for analysis results."""
        if not results:
            return {}

        total_pairs = len(results)
        high_risk_count = sum(1 for r in results if r["risk_level"] in ["high", "critical"])
        avg_similarity = sum(r["overall_similarity"] for r in results) / total_pairs
        
        # Count segments by category
        category_counts = {}
        for result in results:
            for segment in result["suspicious_segments"]:
                category = segment["category"]
                category_counts[category] = category_counts.get(category, 0) + 1

        return {
            "total_comparisons": total_pairs,
            "high_risk_pairs": high_risk_count,
            "average_similarity": round(avg_similarity, 3),
            "segments_by_category": category_counts,
            "risk_distribution": self._calculate_risk_distribution(results)
        }

    def _calculate_risk_distribution(self, results: List[Dict]) -> Dict:
        """Calculate distribution of risk levels."""
        distribution = {"low": 0, "medium": 0, "high": 0, "critical": 0}
        
        for result in results:
            risk_level = result["risk_level"]
            distribution[risk_level] = distribution.get(risk_level, 0) + 1
        
        return distribution