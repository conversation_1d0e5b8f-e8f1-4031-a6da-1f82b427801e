from typing import List, Dict, Optional
from fastapi import UploadFile, HTTPException
from sqlalchemy.orm import Session

from ..models.database import Bid, Tender
from ..storage.manager import file_storage


class FileService:
    """Service for handling file operations and bid management."""

    @staticmethod
    async def upload_bid_files(
        db: Session,
        tender_id: str,
        files: List[UploadFile],
        companies: List[str],
        prices: List[float]
    ) -> List[Bid]:
        """Upload multiple bid files and create database records."""
        
        # Validate tender exists
        tender = db.query(Tender).filter(Tender.id == tender_id).first()
        if not tender:
            raise HTTPException(status_code=404, detail="Tender not found")
        
        # Validate input lengths match
        if not (len(files) == len(companies) == len(prices)):
            raise HTTPException(
                status_code=400,
                detail="Number of files, companies, and prices must match"
            )
        
        try:
            # Save files to storage
            saved_files = await file_storage.save_multiple_bid_files(tender_id, files)
            
            # Create database records
            bids = []
            for i, file_info in enumerate(saved_files):
                bid = Bid(
                    id=file_info["bid_id"],
                    tender_id=tender_id,
                    company_name=companies[i],
                    file_name=file_info["file_name"],
                    file_path=file_info["file_path"],
                    total_price=prices[i]
                )
                db.add(bid)
                bids.append(bid)
            
            db.commit()
            return bids
            
        except Exception as e:
            db.rollback()
            raise HTTPException(
                status_code=500,
                detail=f"Failed to upload files: {str(e)}"
            )

    @staticmethod
    def get_bid(db: Session, bid_id: str) -> Optional[Bid]:
        """Get a bid by ID."""
        return db.query(Bid).filter(Bid.id == bid_id).first()

    @staticmethod
    def get_tender_bids(db: Session, tender_id: str) -> List[Bid]:
        """Get all bids for a tender."""
        return db.query(Bid).filter(Bid.tender_id == tender_id).all()

    @staticmethod
    def delete_bid(db: Session, bid_id: str) -> bool:
        """Delete a bid and its files."""
        bid = db.query(Bid).filter(Bid.id == bid_id).first()
        if not bid:
            return False
        
        try:
            # Delete files from storage
            file_storage.delete_bid_files(bid.tender_id, bid.id)
            
            # Delete database record
            db.delete(bid)
            db.commit()
            return True
            
        except Exception as e:
            db.rollback()
            raise HTTPException(
                status_code=500,
                detail=f"Failed to delete bid: {str(e)}"
            )

    @staticmethod
    def get_file_info(file_path: str) -> Optional[Dict]:
        """Get information about a file."""
        return file_storage.get_file_info(file_path)