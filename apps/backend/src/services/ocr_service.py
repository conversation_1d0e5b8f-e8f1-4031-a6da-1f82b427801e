from typing import Dict, Optional, List, Any
import logging
import os
import uuid
from pathlib import Path
from datetime import datetime
from sqlalchemy.orm import Session

import deepdoctection as dd

from ..storage.manager import file_storage
from ..models.database import Document, DocumentChunk, ChunkType
from ..core.database import get_db

logger = logging.getLogger(__name__)


class OCRService:
    """Service for OCR processing of bid documents using deepdoctection.
    
    This service processes PDF documents to extract text, layout, and structural information
    using the deepdoctection library with Detectron2 models.
    """

    def __init__(self):
        self.analyzer = None
        self._initialize_analyzer()
    
    def _initialize_analyzer(self):
        """Initialize the deepdoctection analyzer."""
        try:
            logger.info("Initializing deepdoctection analyzer...")
            self.analyzer = dd.get_dd_analyzer()
            logger.info("Deepdoctection analyzer initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize deepdoctection analyzer: {str(e)}")
            raise RuntimeError(f"OCR service initialization failed: {str(e)}")
        
    def extract_document_content(self, pdf_path: str) -> Dict:
        """Extract text and layout information from a PDF document using deepdoctection.
        
        Args:
            pdf_path: Path to the PDF file
            
        Returns:
            Dictionary containing extracted text, layout, and metadata
        """
        if not self.analyzer:
            raise RuntimeError("OCR analyzer not initialized")
        
        if not os.path.exists(pdf_path):
            raise FileNotFoundError(f"PDF file not found: {pdf_path}")
        
        logger.info(f"Processing OCR for: {pdf_path}")
        start_time = datetime.now()
        
        try:
            # Analyze the PDF using deepdoctection
            df = self.analyzer.analyze(path=pdf_path)
            df.reset_state()
            
            # Initialize data structure to store all OCR information
            document_data = {
                "metadata": {
                    "source_file": pdf_path,
                    "extraction_timestamp": start_time.isoformat(),
                    "total_pages": 0,
                    "analyzer": "deepdoctection",
                    "processing_time": 0
                },
                "pages": [],
                "full_text": ""
            }
            
            full_text_parts = []
            
            # Process all pages in the PDF
            for page_num, page in enumerate(df, 1):
                logger.info(f"Processing page {page_num}...")
                
                # Extract page data
                page_data = {
                    "page_number": page_num,
                    "dimensions": {
                        "height": page.height,
                        "width": page.width
                    },
                    "file_name": page.file_name,
                    "document_id": page.document_id,
                    "image_id": page.image_id,
                    "layouts": [],
                    "tables": []
                }
                
                # Extract layout information
                for layout in page.layouts:
                    layout_data = {
                        "category_name": layout.category_name,
                        "score": float(layout.score) if layout.score else None,
                        "reading_order": layout.reading_order,
                        "bounding_box": {
                            "x1": layout.bounding_box.ulx,
                            "y1": layout.bounding_box.uly,
                            "x2": layout.bounding_box.lrx,
                            "y2": layout.bounding_box.lry,
                            "width": layout.bounding_box.width,
                            "height": layout.bounding_box.height
                        },
                        "annotation_id": layout.annotation_id,
                        "text": layout.text if layout.text else ""
                    }
                    page_data["layouts"].append(layout_data)
                    
                    # Collect text for full document text
                    if layout.text:
                        full_text_parts.append(layout.text.strip())
                
                # Extract table information if available
                for table in page.tables:
                    table_data = {
                        "bounding_box": {
                            "x1": table.bounding_box.ulx,
                            "y1": table.bounding_box.uly,
                            "x2": table.bounding_box.lrx,
                            "y2": table.bounding_box.lry,
                            "width": table.bounding_box.width,
                            "height": table.bounding_box.height
                        },
                        "confidence": float(table.score) if table.score else None,
                        "cells": []
                    }
                    
                    # Extract table cells if available
                    if hasattr(table, 'cells'):
                        for cell in table.cells:
                            cell_data = {
                                "text": cell.text if hasattr(cell, 'text') else "",
                                "row": cell.row_number if hasattr(cell, 'row_number') else None,
                                "column": cell.column_number if hasattr(cell, 'column_number') else None,
                                "bounding_box": {
                                    "x1": cell.bounding_box.ulx,
                                    "y1": cell.bounding_box.uly,
                                    "x2": cell.bounding_box.lrx,
                                    "y2": cell.bounding_box.lry,
                                    "width": cell.bounding_box.width,
                                    "height": cell.bounding_box.height
                                }
                            }
                            table_data["cells"].append(cell_data)
                    
                    page_data["tables"].append(table_data)
                
                document_data["pages"].append(page_data)
            
            # Update metadata
            end_time = datetime.now()
            processing_time = (end_time - start_time).total_seconds()
            document_data["metadata"]["total_pages"] = len(document_data["pages"])
            document_data["metadata"]["processing_time"] = processing_time
            document_data["full_text"] = " ".join(full_text_parts)
            
            logger.info(f"OCR processing completed in {processing_time:.2f} seconds")
            logger.info(f"Extracted {len(document_data['pages'])} pages with {len(full_text_parts)} text segments")
            
            return document_data
            
        except Exception as e:
            logger.error(f"OCR processing failed for {pdf_path}: {str(e)}")
            raise RuntimeError(f"OCR processing failed: {str(e)}")

    def extract_all_documents(self, project_id: str, document_ids: List[str], db: Session) -> Dict[str, Dict]:
        """Extract content from all documents in a project.
        
        Args:
            project_id: ID of the project
            document_ids: List of document IDs to process
            db: Database session
            
        Returns:
            Dictionary mapping document_id to extracted content
        """
        results = {}
        
        for doc_id in document_ids:
            try:
                # Get document from database
                document = db.query(Document).filter(Document.id == doc_id).first()
                if not document:
                    logger.warning(f"Document {doc_id} not found in database")
                    continue
                
                # Check if already processed
                existing_chunks = db.query(DocumentChunk).filter(
                    DocumentChunk.document_id == doc_id
                ).count()
                
                if existing_chunks > 0:
                    logger.info(f"Using existing OCR data for document {doc_id}")
                    # Load existing data (simplified for now)
                    results[doc_id] = {"status": "already_processed", "chunks_count": existing_chunks}
                    continue
                
                # Extract content from PDF
                pdf_path = document.storage_path
                content = self.extract_document_content(pdf_path)
                
                # Save chunks to database
                chunks_saved = self.save_chunks_to_database(doc_id, content, db)
                
                # Update document status
                document.status = "processed"
                document.page_count = content["metadata"]["total_pages"]
                db.commit()
                
                results[doc_id] = {
                    "status": "processed",
                    "chunks_count": chunks_saved,
                    "pages": content["metadata"]["total_pages"],
                    "processing_time": content["metadata"]["processing_time"]
                }
                
            except Exception as e:
                logger.error(f"Failed to extract content for document {doc_id}: {str(e)}")
                # Update document status to error
                try:
                    document = db.query(Document).filter(Document.id == doc_id).first()
                    if document:
                        document.status = "error"
                        db.commit()
                except Exception:
                    pass
                continue
        
        return results

    def save_chunks_to_database(self, document_id: str, content: Dict, db: Session) -> int:
        """Save extracted chunks to database.
        
        Args:
            document_id: ID of the document
            content: Extracted content from OCR
            db: Database session
            
        Returns:
            Number of chunks saved
        """
        chunks_saved = 0
        
        try:
            # Convert string document_id to UUID for database operations
            import uuid as uuid_lib
            try:
                document_uuid = uuid_lib.UUID(document_id)
            except (ValueError, TypeError):
                raise ValueError(f"Invalid document ID format: {document_id}")
            
            for page in content["pages"]:
                page_number = page["page_number"]
                
                # Process layout elements as text chunks
                for layout in page["layouts"]:
                    if layout["text"].strip():  # Only save chunks with actual text
                        chunk = DocumentChunk(
                            id=uuid.uuid4(),
                            document_id=document_uuid,
                            page_number=page_number,
                            chunk_type=self._map_category_to_chunk_type(layout["category_name"]),
                            content=layout["text"],
                            bounding_box=layout["bounding_box"]
                        )
                        db.add(chunk)
                        chunks_saved += 1
                
                # Process tables as table chunks
                for table in page["tables"]:
                    # Create a text representation of the table
                    table_text = self._extract_table_text(table)
                    if table_text.strip():
                        chunk = DocumentChunk(
                            id=uuid.uuid4(),
                            document_id=document_uuid,
                            page_number=page_number,
                            chunk_type=ChunkType.TABLE,
                            content=table_text,
                            bounding_box=table["bounding_box"]
                        )
                        db.add(chunk)
                        chunks_saved += 1
            
            db.commit()
            logger.info(f"Saved {chunks_saved} chunks for document {document_id}")
            return chunks_saved
            
        except Exception as e:
            logger.error(f"Failed to save chunks for document {document_id}: {str(e)}")
            db.rollback()
            raise
    
    def _map_category_to_chunk_type(self, category_name: str) -> ChunkType:
        """Map deepdoctection category to our chunk type."""
        category_mapping = {
            "text": ChunkType.TEXT,
            "title": ChunkType.TEXT,
            "list": ChunkType.TEXT,
            "table": ChunkType.TABLE,
            "figure": ChunkType.IMAGE,
            "image": ChunkType.IMAGE
        }
        return category_mapping.get(category_name.lower(), ChunkType.TEXT)
    
    def _extract_table_text(self, table: Dict) -> str:
        """Extract text content from table structure."""
        if not table.get("cells"):
            return ""
        
        # Simple text extraction from table cells
        cell_texts = []
        for cell in table["cells"]:
            if cell.get("text"):
                cell_texts.append(cell["text"].strip())
        
        return " | ".join(cell_texts)

    def preprocess_text(self, text: str) -> str:
        """Preprocess extracted text for analysis.
        
        Args:
            text: Raw extracted text
            
        Returns:
            Cleaned and preprocessed text
        """
        if not text:
            return ""
        
        # Basic text cleaning
        text = text.strip()
        
        # Remove excessive whitespace
        import re
        text = re.sub(r'\s+', ' ', text)
        
        # Remove special characters that might interfere with analysis
        text = re.sub(r'[^\w\s\u4e00-\u9fff，。；：！？、（）「」『』【】\-\.]', '', text)
        
        return text

    def chunk_text(self, text: str, chunk_size: int = 500, overlap: int = 50) -> list:
        """Split text into overlapping chunks for analysis.
        
        PLACEHOLDER FUNCTION - Will be enhanced with semantic chunking.
        
        Args:
            text: Text to chunk
            chunk_size: Maximum characters per chunk
            overlap: Number of overlapping characters between chunks
            
        Returns:
            List of text chunks
        """
        if not text or len(text) <= chunk_size:
            return [text] if text else []
        
        chunks = []
        start = 0
        
        while start < len(text):
            end = start + chunk_size
            
            # Find a good breaking point (sentence or paragraph end)
            if end < len(text):
                for break_char in ['。', '！', '？', '\n']:
                    break_pos = text.rfind(break_char, start, end)
                    if break_pos > start + chunk_size // 2:
                        end = break_pos + 1
                        break
            
            chunk = text[start:end].strip()
            if chunk:
                chunks.append(chunk)
            
            start = max(start + 1, end - overlap)
        
        return chunks

    def is_available(self) -> bool:
        """Check if OCR service is available."""
        return self.analyzer is not None

    def get_model_info(self) -> Dict:
        """Get information about the OCR model."""
        return {
            "model_name": "deepdoctection",
            "version": "0.28.0+",
            "status": "active" if self.analyzer else "error",
            "supported_languages": ["en", "zh-CN", "zh-TW", "multilingual"],
            "backend": "Detectron2 + LayoutLMv3",
            "capabilities": ["text_extraction", "layout_detection", "table_recognition", "reading_order"]
        }