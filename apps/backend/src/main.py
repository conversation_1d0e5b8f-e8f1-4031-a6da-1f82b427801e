from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import J<PERSON><PERSON>esponse
import logging

from .core.config import settings
from .core.database import create_tables
from .api.routes import router as api_router
from .utils.exceptions import TraceFastException

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(
    title="TraceFast API",
    description="Public Procurement Analysis Platform - Backend API",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# CORS configuration
allowed_origins = [settings.frontend_origin] if settings.frontend_origin else ["*"]
app.add_middleware(
    CORSMiddleware,
    allow_origins=allowed_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# Global exception handler
@app.exception_handler(TraceFastException)
async def tracefast_exception_handler(request: Request, exc: TraceFastException):
    return J<PERSON>NResponse(
        status_code=500,
        content={
            "message": exc.message,
            "details": exc.details,
            "error_code": "TRACEFAST_ERROR"
        }
    )


# Startup event
@app.on_event("startup")
async def startup_event():
    """Initialize database and perform startup tasks."""
    logger.info("Starting TraceFast API...")
    
    # Create database tables
    create_tables()
    logger.info("Database tables created/verified")
    
    # Initialize storage directory
    from pathlib import Path
    Path(settings.storage_path).mkdir(parents=True, exist_ok=True)
    logger.info(f"Storage directory initialized: {settings.storage_path}")
    
    logger.info("TraceFast API startup completed")


# Health check endpoint
@app.get("/health", tags=["health"])
def health() -> dict[str, str]:
    """Basic health check endpoint."""
    return {
        "status": "healthy",
        "service": "TraceFast API",
        "version": "1.0.0"
    }


# API status endpoint
@app.get("/status", tags=["health"])
def status():
    """Detailed status endpoint."""
    return {
        "service": "TraceFast API",
        "status": "running",
        "version": "1.0.0",
        "environment": settings.env,
        "features": {
            "file_upload": True,
            "ocr_processing": True,
            "analysis_engine": True,
            "multi_dimensional_comparison": True
        },
        "models": {
            "ocr": "placeholder (ready for integration)",
            "embeddings": "placeholder (ready for integration)",
            "analysis": "built-in algorithms"
        }
    }


# Mount API routes under configured prefix
app.include_router(api_router, prefix=settings.api_prefix)


# Optional: enable `python apps/backend/src/main.py` direct run
if __name__ == "__main__":
    import uvicorn

    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=settings.port,
        reload=True,
    )


