[project]
name = "tracefast-backend"
version = "0.1.0"
description = "TraceFast Backend - Public Procurement Analysis Platform"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "fastapi[standard]>=0.116.0",
    "uvicorn[standard]>=0.30.0",
    "pydantic>=2.7.0",
    "pydantic-settings>=2.3.3",
    "python-dotenv>=1.0.1",
    "sqlalchemy>=2.0.0",
    "alembic>=1.13.0",
    "python-multipart>=0.0.6",
    "aiofiles>=23.2.0",
    "pypdf>=3.17.0",
    "python-magic>=0.4.27",
    "numpy>=1.24.0",
    "torch==2.2.0",
    "transformers>=4.51.0",
    "sentence-transformers==2.7.0",
    "datasets==2.19.1",
    "einops==0.8.0",
    "deepdoctection[pt]>=0.28.0",
    "pymupdf>=1.23.0",
    "matplotlib>=3.7.0",
    "seaborn>=0.12.0",
    "reportlab>=4.0.0"
]

[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[dependency-groups]
dev = [
    "uvicorn[standard]>=0.30.0",
]

[tool.setuptools]
packages = ["src"]

[tool.uv]
dev-dependencies = [
    "uvicorn[standard]>=0.30.0",
] 


