#!/usr/bin/env python3
"""
Database reset script for development.
This script drops the existing database and recreates it with the current schema.
"""
import os
import sys
from pathlib import Path

# Add src to path so we can import our modules
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.core.database import engine, create_tables
from src.models.database import Base

def reset_database():
    """Drop all tables and recreate them."""
    print("🔄 Resetting database...")
    
    # Drop all tables
    print("📝 Dropping all existing tables...")
    Base.metadata.drop_all(bind=engine)
    
    # Recreate all tables
    print("🏗️  Creating tables with updated schema...")
    create_tables()
    
    print("✅ Database reset completed!")
    print("📍 Database location: tracefast.db")

if __name__ == "__main__":
    reset_database()