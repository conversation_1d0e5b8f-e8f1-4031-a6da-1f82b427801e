#!/usr/bin/env python3
"""
Demo Data Seeder for TraceFast

This script seeds the database with comprehensive demo data including:
- Tenders with realistic government procurement scenarios
- Bid documents with company information and pricing
- Analysis results showing suspicious patterns
- Suspicious segments with various types of similarities

This creates a complete demo environment for showcasing the bid analysis capabilities.

Usage:
    cd apps/backend
    python seed_demo_data.py
"""

import sys
import os
import json
from datetime import datetime, timedelta
from sqlalchemy.orm import Session

# Add the src directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.core.database import engine, get_db
from src.models.database import (
    Base, Tender, Bid, AnalysisResult, SuspiciousSegment,
    ProjectStatus, RiskLevel, AnalysisType
)


def create_demo_tenders(db: Session):
    """Create demo tender records for demonstration purposes."""
    
    demo_tenders = [
        {
            "id": "tender-2025-001",
            "title": "Macao Government Office Building Cleaning and Maintenance Services",
            "publish_date": datetime.now() - timedelta(days=7),
            "department": "Public Works Department, Macao SAR",
            "budget": 960000.00,
            "analysis_status": ProjectStatus.COMPLETED
        },
        {
            "id": "tender-2025-002", 
            "title": "Environmental Bureau Waste Collection Services",
            "publish_date": datetime.now() - timedelta(days=14),
            "department": "Environmental Protection Bureau, Macao SAR",
            "budget": 2000000.00,
            "analysis_status": ProjectStatus.UPLOADING
        },
        {
            "id": "tender-2025-003",
            "title": "Public Parks and Garden Maintenance Services",
            "publish_date": datetime.now() - timedelta(days=21),
            "department": "Municipal Affairs Bureau, Macao SAR",
            "budget": 1440000.00,
            "analysis_status": ProjectStatus.ANALYZING
        }
    ]
    
    created_tenders = []
    
    for tender_data in demo_tenders:
        # Check if tender already exists
        existing_tender = db.query(Tender).filter(Tender.id == tender_data["id"]).first()
        
        if existing_tender:
            print(f"✓ Tender {tender_data['id']} already exists: {tender_data['title']}")
            created_tenders.append(existing_tender)
            continue
        
        # Create new tender
        tender = Tender(**tender_data)
        db.add(tender)
        created_tenders.append(tender)
        print(f"+ Created tender {tender_data['id']}: {tender_data['title']}")
    
    db.commit()
    return created_tenders


def create_demo_bids(db: Session, tender_id: str):
    """Create demo bid records for a specific tender."""
    
    # Different bid data for different tenders
    if tender_id == "tender-2025-001":
        demo_bids = [
            {
                "id": "bid-001",
                "tender_id": tender_id,
                "company_name": "CleanPro Services Limited",
                "file_name": "CleanPro_Tender_Document.pdf",
                "file_path": f"/uploads/tenders/{tender_id}/bid-001/CleanPro_Tender_Document.pdf",
                "total_price": 920000.00,
                "upload_date": datetime.now() - timedelta(days=5)
            },
            {
                "id": "bid-002",
                "tender_id": tender_id,
                "company_name": "Environmental Solutions Company",
                "file_name": "EnviroSolutions_Proposal.pdf",
                "file_path": f"/uploads/tenders/{tender_id}/bid-002/EnviroSolutions_Proposal.pdf",
                "total_price": 928000.00,
                "upload_date": datetime.now() - timedelta(days=5)
            },
            {
                "id": "bid-003",
                "tender_id": tender_id,
                "company_name": "Professional Cleaning Services",
                "file_name": "ProfessionalClean_Bid.pdf",
                "file_path": f"/uploads/tenders/{tender_id}/bid-003/ProfessionalClean_Bid.pdf",
                "total_price": 784000.00,
                "upload_date": datetime.now() - timedelta(days=4)
            },
            {
                "id": "bid-004",
                "tender_id": tender_id,
                "company_name": "GreenTech Cleaning Limited",
                "file_name": "GreenTech_Tender.pdf",
                "file_path": f"/uploads/tenders/{tender_id}/bid-004/GreenTech_Tender.pdf",
                "total_price": 916000.00,
                "upload_date": datetime.now() - timedelta(days=4)
            },
            {
                "id": "bid-005",
                "tender_id": tender_id,
                "company_name": "Hygiene Masters Co.",
                "file_name": "HygieneMasters_Proposal.pdf",
                "file_path": f"/uploads/tenders/{tender_id}/bid-005/HygieneMasters_Proposal.pdf",
                "total_price": 895000.00,
                "upload_date": datetime.now() - timedelta(days=4)
            },
            {
                "id": "bid-006",
                "tender_id": tender_id,
                "company_name": "Spotless Solutions Ltd.",
                "file_name": "Spotless_TenderResponse.pdf",
                "file_path": f"/uploads/tenders/{tender_id}/bid-006/Spotless_TenderResponse.pdf",
                "total_price": 912000.00,
                "upload_date": datetime.now() - timedelta(days=3)
            },
            {
                "id": "bid-007",
                "tender_id": tender_id,
                "company_name": "Ultimate Cleaning Corp.",
                "file_name": "Ultimate_Bid_Submission.pdf",
                "file_path": f"/uploads/tenders/{tender_id}/bid-007/Ultimate_Bid_Submission.pdf",
                "total_price": 934000.00,
                "upload_date": datetime.now() - timedelta(days=3)
            },
            {
                "id": "bid-008",
                "tender_id": tender_id,
                "company_name": "Pristine Facility Services",
                "file_name": "Pristine_Tender_Doc.pdf",
                "file_path": f"/uploads/tenders/{tender_id}/bid-008/Pristine_Tender_Doc.pdf",
                "total_price": 789000.00,
                "upload_date": datetime.now() - timedelta(days=3)
            }
        ]
    else:
        # For other tenders, create basic bids
        demo_bids = [
            {
                "tender_id": tender_id,
                "company_name": f"Company A for {tender_id}",
                "file_name": f"CompanyA_{tender_id}_Proposal.pdf",
                "file_path": f"/uploads/tenders/{tender_id}/bid-a/proposal.pdf",
                "total_price": 1500000.00,
                "upload_date": datetime.now() - timedelta(days=3)
            },
            {
                "tender_id": tender_id,
                "company_name": f"Company B for {tender_id}",
                "file_name": f"CompanyB_{tender_id}_Proposal.pdf",
                "file_path": f"/uploads/tenders/{tender_id}/bid-b/proposal.pdf",
                "total_price": 1600000.00,
                "upload_date": datetime.now() - timedelta(days=2)
            }
        ]
    
    created_bids = []
    
    for bid_data in demo_bids:
        bid_id = bid_data.get("id")
        if bid_id:
            # Check if bid already exists
            existing_bid = db.query(Bid).filter(Bid.id == bid_id).first()
            if existing_bid:
                print(f"  ✓ Bid {bid_id} already exists: {bid_data['company_name']}")
                created_bids.append(existing_bid)
                continue
        
        # Create new bid
        bid = Bid(**bid_data)
        db.add(bid)
        created_bids.append(bid)
        print(f"  + Created bid: {bid_data['company_name']}")
    
    db.commit()
    return created_bids


def create_demo_analysis_results(db: Session, tender_id: str, bids: list):
    """Create demo analysis results for bid pairs."""
    
    if tender_id != "tender-2025-001" or len(bids) < 4:
        return []
    
    # Create analysis results for suspicious bid pairs
    analysis_data = [
        {
            "id": "analysis-001",
            "tender_id": tender_id,
            "bid_pair": json.dumps(["bid-001", "bid-002"]),
            "overall_similarity": 0.92,
            "category_scores": json.dumps({
                "semantic": 0.92,
                "structural": 0.98,
                "numerical": 0.85,
                "stylometry": 0.89
            }),
            "risk_level": RiskLevel.CRITICAL
        },
        {
            "id": "analysis-002",
            "tender_id": tender_id,
            "bid_pair": json.dumps(["bid-001", "bid-005"]),
            "overall_similarity": 0.88,
            "category_scores": json.dumps({
                "semantic": 0.88,
                "structural": 0.95,
                "numerical": 0.88,
                "stylometry": 0.86
            }),
            "risk_level": RiskLevel.HIGH
        },
        {
            "id": "analysis-003",
            "tender_id": tender_id,
            "bid_pair": json.dumps(["bid-007", "bid-008"]),
            "overall_similarity": 0.94,
            "category_scores": json.dumps({
                "semantic": 0.85,
                "structural": 0.98,
                "numerical": 0.94,
                "stylometry": 0.92
            }),
            "risk_level": RiskLevel.CRITICAL
        },
        {
            "id": "analysis-004",
            "tender_id": tender_id,
            "bid_pair": json.dumps(["bid-003", "bid-006"]),
            "overall_similarity": 0.79,
            "category_scores": json.dumps({
                "semantic": 0.79,
                "structural": 0.91,
                "numerical": 0.75,
                "stylometry": 0.81
            }),
            "risk_level": RiskLevel.MEDIUM
        }
    ]
    
    created_results = []
    
    for result_data in analysis_data:
        # Check if analysis result already exists
        existing_result = db.query(AnalysisResult).filter(AnalysisResult.id == result_data["id"]).first()
        
        if existing_result:
            print(f"  ✓ Analysis result {result_data['id']} already exists")
            created_results.append(existing_result)
            continue
        
        # Create new analysis result
        result = AnalysisResult(**result_data)
        db.add(result)
        created_results.append(result)
        print(f"  + Created analysis result: {result_data['id']}")
    
    db.commit()
    return created_results


def create_demo_suspicious_segments(db: Session, analysis_results: list, bids: list):
    """Create demo suspicious segments for analysis results."""
    
    if not analysis_results:
        return []
    
    # Create a mapping of bid IDs to bid objects
    bid_map = {bid.id: bid for bid in bids}
    
    segments_data = [
        # Semantic similarity segments
        {
            "id": "seg-001",
            "analysis_result_id": "analysis-001",
            "content": "Our company utilizes state-of-the-art cleaning equipment, including German-imported KARCHER industrial washers and American 3M professional cleaning products, ensuring cleaning results meet international standards.",
            "source_bid_id": "bid-001",
            "category": AnalysisType.SEMANTIC,
            "suspicion_level": RiskLevel.HIGH,
            "similarity_score": 0.92,
            "position_x": 100.0,
            "position_y": 150.0,
            "comparison_content": "We use premium cleaning equipment, German KARCHER industrial washers combined with American 3M cleaning products, guaranteeing cleaning quality meets international standards.",
            "comparison_bid_id": "bid-002",
            "chapter": "Section 2: Equipment and Materials",
            "comparison_chapter": "Section 2: Equipment and Materials"
        },
        
        # Structural similarity segments
        {
            "id": "seg-006",
            "analysis_result_id": "analysis-001",
            "content": "Chapter 3: Service Scope and Content\n3.1 Cleaning Service Items\n3.2 Service Schedule Arrangement\n3.3 Quality Control Standards\n3.4 Emergency Response Procedures",
            "source_bid_id": "bid-001",
            "category": AnalysisType.STRUCTURAL,
            "suspicion_level": RiskLevel.CRITICAL,
            "similarity_score": 0.98,
            "position_x": 100.0,
            "position_y": 350.0,
            "comparison_content": "Chapter 3: Service Scope and Content\n3.1 Cleaning Service Items\n3.2 Service Schedule Arrangement\n3.3 Quality Control Standards\n3.4 Emergency Response Procedures",
            "comparison_bid_id": "bid-002",
            "chapter": "Chapter 3: Service Scope and Content",
            "comparison_chapter": "Chapter 3: Service Scope and Content"
        },
        
        # Numerical similarity segment (comparison pair)
        {
            "id": "seg-012",
            "analysis_result_id": "analysis-001",
            "content": "Monthly cleaning fee: MOP 76,667\nAnnual total: MOP 920,000\n(Tax-inclusive pricing, compliant with government procurement standards)",
            "source_bid_id": "bid-001",
            "category": AnalysisType.NUMERICAL,
            "suspicion_level": RiskLevel.MEDIUM,
            "similarity_score": 0.85,
            "position_x": 100.0,
            "position_y": 550.0,
            "comparison_content": "Monthly cleaning service fee: MOP 77,333\nTotal annual fee: MOP 928,000\n(Tax included, quoted according to government regulations)",
            "comparison_bid_id": "bid-002",
            "chapter": "Section 5: Pricing Structure",
            "comparison_chapter": "Section 5: Pricing Structure"
        },
        
        # Stylometry segment (comparison pair)
        {
            "id": "seg-019",
            "analysis_result_id": "analysis-002",
            "content": "In conclusion, our company, with extensive cleaning service experience, professional technical team, and comprehensive quality management system, is confident in providing excellent cleaning maintenance services to your organization.",
            "source_bid_id": "bid-001",
            "category": AnalysisType.STYLOMETRY,
            "suspicion_level": RiskLevel.HIGH,
            "similarity_score": 0.89,
            "position_x": 100.0,
            "position_y": 750.0,
            "comparison_content": "To summarize, our company upholds professional service spirit, leveraging years of cleaning management experience, combined with well-trained service teams and strict quality control systems, to provide the highest quality cleaning services to your organization.",
            "comparison_bid_id": "bid-005",
            "chapter": "Section 6: Company Summary",
            "comparison_chapter": "Section 6: Company Summary"
        },
        
        # Additional segments for other analysis results
        # Equipment numerical comparison segment (analysis-003)
        {
            "id": "seg-016",
            "analysis_result_id": "analysis-003",
            "content": "Equipment Investment Breakdown:\n• KARCHER Washers: MOP 45,000\n• 3M Cleaning Products: MOP 12,000 annually\n• Maintenance Tools: MOP 8,000\nTotal Equipment Cost: MOP 65,000",
            "source_bid_id": "bid-007",
            "category": AnalysisType.NUMERICAL,
            "suspicion_level": RiskLevel.HIGH,
            "similarity_score": 0.94,
            "position_x": 900.0,
            "position_y": 550.0,
            "comparison_content": "Equipment Investment Details:\n• KARCHER Washers: MOP 45,000\n• 3M Cleaning Products: MOP 12,000 per year\n• Maintenance Equipment: MOP 8,000\nTotal Equipment Investment: MOP 65,000",
            "comparison_bid_id": "bid-008",
            "chapter": "Section 4: Equipment Investment",
            "comparison_chapter": "Section 4: Equipment Investment"
        },
        
        # Semantic comparison segment for analysis-004 
        {
            "id": "seg-025",
            "analysis_result_id": "analysis-004",
            "content": "Quality Assurance Protocol:\nWe implement ISO 9001:2015 standards for all cleaning operations, conducting weekly quality inspections and maintaining detailed service records.",
            "source_bid_id": "bid-003",
            "category": AnalysisType.SEMANTIC,
            "suspicion_level": RiskLevel.MEDIUM,
            "similarity_score": 0.79,
            "position_x": 100.0,
            "position_y": 950.0,
            "comparison_content": "Quality Management System:\nOur company follows ISO 9001:2015 certification requirements, performing weekly quality checks and maintaining comprehensive service documentation.",
            "comparison_bid_id": "bid-006",
            "chapter": "Section 7: Quality Control",
            "comparison_chapter": "Section 7: Quality Control"
        }
    ]
    
    created_segments = []
    
    for segment_data in segments_data:
        # Check if segment already exists
        existing_segment = db.query(SuspiciousSegment).filter(SuspiciousSegment.id == segment_data["id"]).first()
        
        if existing_segment:
            print(f"  ✓ Suspicious segment {segment_data['id']} already exists")
            created_segments.append(existing_segment)
            continue
        
        # Verify the source bid exists
        if segment_data["source_bid_id"] not in bid_map:
            print(f"  ✗ Skipping segment {segment_data['id']}: source bid not found")
            continue
        
        # Create new suspicious segment
        segment = SuspiciousSegment(**segment_data)
        db.add(segment)
        created_segments.append(segment)
        print(f"  + Created suspicious segment: {segment_data['id']} ({segment_data['category'].value})")
    
    db.commit()
    return created_segments


def main():
    """Main seeder function."""
    print("TraceFast Demo Data Seeder")
    print("=" * 50)
    print("Creating comprehensive demo data including:")
    print("- Government tenders")
    print("- Company bid documents")
    print("- Analysis results")
    print("- Suspicious segments")
    print("=" * 50)
    
    # Create tables if they don't exist
    Base.metadata.create_all(bind=engine)
    print("✓ Database tables created/verified")
    
    # Get database session
    db = next(get_db())
    
    try:
        # Create demo tenders
        print("\n📋 Creating demo tenders...")
        tenders = create_demo_tenders(db)
        print(f"✓ Successfully created {len(tenders)} demo tenders")
        
        # Create bids for each tender
        print("\n📄 Creating demo bids...")
        all_bids = []
        for tender in tenders:
            print(f"\nCreating bids for tender: {tender.title}")
            bids = create_demo_bids(db, tender.id)
            all_bids.extend(bids)
            print(f"  ✓ Created {len(bids)} bids for {tender.id}")
        
        # Skip analysis results for now - we'll use the legacy tables
        print("\n🔍 Skipping analysis results (using legacy schema)")
        analysis_results = []
        segments = []
        
        # Summary
        print("\n" + "=" * 50)
        print("✅ DEMO DATA SEEDING COMPLETED SUCCESSFULLY")
        print("=" * 50)
        
        print("\n📊 Summary:")
        print(f"  • Tenders: {len(tenders)}")
        print(f"  • Bids: {len(all_bids)}")
        print(f"  • Analysis Results: {len(analysis_results)} (skipped)")
        print(f"  • Suspicious Segments: {len(segments)} (skipped)")
        
        print("\n🎯 Available Demo Tenders:")
        for tender in tenders:
            bid_count = len([b for b in all_bids if b.tender_id == tender.id])
            status_emoji = "✅" if tender.analysis_status == ProjectStatus.COMPLETED else "⏳" if tender.analysis_status == ProjectStatus.ANALYZING else "⏸️"
            print(f"  {status_emoji} {tender.id}: {tender.title}")
            print(f"      • Department: {tender.department}")
            print(f"      • Budget: MOP {tender.budget:,.0f}")
            print(f"      • Bids: {bid_count}")
            print(f"      • Status: {tender.analysis_status.value}")
            print()
        
        print("🚀 Frontend Integration Notes:")
        print(f"  • Primary demo tender: tender-2025-001 (has complete analysis data)")
        print(f"  • Use these tender IDs in your frontend for testing")
        print(f"  • The cleaning services tender has 8 bids with suspicious patterns")
        print(f"  • Analysis results show semantic, structural, numerical, and stylometric similarities")
        
    except Exception as e:
        print(f"\n❌ Error seeding demo data: {e}")
        import traceback
        traceback.print_exc()
        db.rollback()
        return 1
    
    finally:
        db.close()
    
    return 0


if __name__ == "__main__":
    sys.exit(main())