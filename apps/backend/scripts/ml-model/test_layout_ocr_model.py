"""
PDF Document Layout Analysis OCR Model Testing Script

This script tests the Docker-based PDF layout analysis microservice
for the TraceFast project. It focuses on testing document element layout
detection and text content extraction capabilities.

Requirements:
- Docker service running on localhost:5060
- A single PDF file for testing
- requests library for HTTP calls

Usage:
- python test_layout_ocr_model.py [pdf_file_path]
- If no path provided, defaults to 'med_2_pages.pdf' in the same directory
"""

import requests
import json
import time
import os
import sys
from pathlib import Path


class LayoutOCRTester:
    """Test class for PDF layout analysis and OCR functionality."""
    
    def __init__(self, base_url="http://localhost:5060"):
        self.base_url = base_url
        self.test_results = []
        
    def check_service_status(self):
        """Check if the PDF layout analysis service is running."""
        try:
            response = requests.get(f"{self.base_url}/info", timeout=10)
            if response.status_code == 200:
                info = response.json()
                print(f"✅ Service is running")
                print(f"📋 Service info: {json.dumps(info, indent=2)}")
                return True
            else:
                print(f"❌ Service returned status {response.status_code}")
                return False
        except requests.exceptions.RequestException as e:
            print(f"❌ Failed to connect to service: {e}")
            return False
    
    def test_basic_layout_analysis(self, pdf_path, use_fast_model=False):
        """Test basic PDF layout analysis."""
        print(f"\n{'='*60}")
        print(f"Testing Layout Analysis: {os.path.basename(pdf_path)}")
        print(f"Model: {'LightGBM (Fast)' if use_fast_model else 'VGT (High Accuracy)'}")
        print(f"{'='*60}")
        
        if not os.path.exists(pdf_path):
            print(f"❌ PDF file not found: {pdf_path}")
            return None
            
        try:
            start_time = time.time()
            
            with open(pdf_path, 'rb') as f:
                files = {'file': f}
                data = {'fast': 'true' if use_fast_model else 'false'}
                
                response = requests.post(
                    f"{self.base_url}/",
                    files=files,
                    data=data,
                    timeout=120
                )
            
            processing_time = time.time() - start_time
            
            if response.status_code == 200:
                segments = response.json()
                print(f"✅ Analysis completed in {processing_time:.2f} seconds")
                print(f"📊 Found {len(segments)} segments")
                
                # Analyze segment types
                segment_types = {}
                total_text_length = 0
                image_segments = []
                
                for segment in segments:
                    seg_type = segment.get('type', 'Unknown')
                    segment_types[seg_type] = segment_types.get(seg_type, 0) + 1
                    
                    # Collect image segments for detailed analysis
                    if seg_type.lower() == 'picture':
                        image_segments.append(segment)
                    
                    text = segment.get('text', '')
                    if text:
                        total_text_length += len(text)
                
                print(f"\n📋 Segment breakdown:")
                for seg_type, count in sorted(segment_types.items()):
                    emoji = "🖼️" if seg_type.lower() == 'picture' else "📄"
                    print(f"   {emoji} {seg_type}: {count}")
                
                print(f"\n📝 Total text extracted: {total_text_length} characters")
                print(f"🖼️  Images found: {len(image_segments)}")
                
                # Show detailed segment information
                print(f"\n🔍 Detailed segment analysis:")
                for i, segment in enumerate(segments[:5]):  # Show first 5 segments
                    text = segment.get('text', '')
                    print(f"   {i+1}. Type: {segment.get('type')}")
                    print(f"      📍 Position: ({segment.get('left'):.1f}, {segment.get('top'):.1f})")
                    print(f"      📏 Size: {segment.get('width'):.1f} x {segment.get('height'):.1f}")
                    print(f"      📄 Page: {segment.get('page_number', 'N/A')}")
                    if text:
                        preview = text[:100].replace('\n', ' ')
                        print(f"      📝 Text: {preview}{'...' if len(text) > 100 else ''}")
                    print()
                
                # Detailed image analysis
                if image_segments:
                    print(f"\n🖼️  Image segment details:")
                    for i, img in enumerate(image_segments):
                        print(f"   Image {i+1}:")
                        print(f"      📍 Position: ({img.get('left'):.1f}, {img.get('top'):.1f})")
                        print(f"      📏 Size: {img.get('width'):.1f} x {img.get('height'):.1f}")
                        print(f"      📄 Page: {img.get('page_number', 'N/A')}")
                        print(f"      📊 Area: {(img.get('width', 0) * img.get('height', 0)):.0f} sq units")
                        print()
                
                return {
                    'success': True,
                    'processing_time': processing_time,
                    'segment_count': len(segments),
                    'segment_types': segment_types,
                    'total_text_length': total_text_length,
                    'image_count': len(image_segments),
                    'image_segments': image_segments,
                    'segments': segments
                }
                
            else:
                print(f"❌ Analysis failed with status {response.status_code}")
                print(f"Response: {response.text}")
                return None
                
        except Exception as e:
            print(f"❌ Error during analysis: {e}")
            return None
    
    def test_text_extraction(self, pdf_path, content_types="all"):
        """Test specific text extraction by content types."""
        print(f"\n{'='*60}")
        print(f"Testing Text Extraction: {os.path.basename(pdf_path)}")
        print(f"Content types: {content_types}")
        print(f"{'='*60}")
        
        try:
            with open(pdf_path, 'rb') as f:
                files = {'file': f}
                data = {'types': content_types}
                
                response = requests.post(
                    f"{self.base_url}/text",
                    files=files,
                    data=data,
                    timeout=120
                )
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ Text extraction completed")
                
                # Count extracted text by type
                if isinstance(result, list):
                    type_text = {}
                    for item in result:
                        item_type = item.get('type', 'Unknown')
                        text = item.get('text', '')
                        if item_type not in type_text:
                            type_text[item_type] = ''
                        type_text[item_type] += text + ' '
                    
                    print(f"\n📝 Extracted text by type:")
                    for text_type, text in type_text.items():
                        print(f"   {text_type}: {len(text)} characters")
                        if text.strip():
                            preview = text.strip()[:150]
                            print(f"      Preview: {preview}{'...' if len(text.strip()) > 150 else ''}")
                
                return result
            else:
                print(f"❌ Text extraction failed with status {response.status_code}")
                return None
                
        except Exception as e:
            print(f"❌ Error during text extraction: {e}")
            return None
    
    def test_table_ocr(self, pdf_path):
        """Test table OCR functionality."""
        print(f"\n{'='*60}")
        print(f"Testing Table OCR: {os.path.basename(pdf_path)}")
        print(f"{'='*60}")
        
        try:
            with open(pdf_path, 'rb') as f:
                files = {'file': f}
                data = {'ocr_tables': 'true'}
                
                response = requests.post(
                    f"{self.base_url}/",
                    files=files,
                    data=data,
                    timeout=120
                )
            
            if response.status_code == 200:
                segments = response.json()
                
                # Find table segments
                table_segments = [s for s in segments if s.get('type') == 'Table']
                
                print(f"✅ Found {len(table_segments)} table segments")
                
                for i, table in enumerate(table_segments):
                    print(f"\n📊 Table {i+1}:")
                    print(f"   Position: ({table.get('left'):.1f}, {table.get('top'):.1f})")
                    print(f"   Size: {table.get('width'):.1f}x{table.get('height'):.1f}")
                    text = table.get('text', '')
                    if text:
                        print(f"   Content preview: {text[:200]}{'...' if len(text) > 200 else ''}")
                
                return table_segments
            else:
                print(f"❌ Table OCR failed with status {response.status_code}")
                return None
                
        except Exception as e:
            print(f"❌ Error during table OCR: {e}")
            return None
    
    def test_image_extraction(self, pdf_path):
        """Test specific image/picture extraction and analysis."""
        print(f"\n{'='*60}")
        print(f"Testing Image Extraction: {os.path.basename(pdf_path)}")
        print(f"{'='*60}")
        
        try:
            with open(pdf_path, 'rb') as f:
                files = {'file': f}
                data = {'types': 'picture'}  # Focus only on images
                
                response = requests.post(
                    f"{self.base_url}/text",
                    files=files,
                    data=data,
                    timeout=120
                )
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ Image extraction completed")
                
                if isinstance(result, list):
                    image_segments = [item for item in result if item.get('type', '').lower() == 'picture']
                    
                    print(f"\n🖼️  Found {len(image_segments)} image segments")
                    
                    if image_segments:
                        total_area = 0
                        for i, img in enumerate(image_segments):
                            area = img.get('width', 0) * img.get('height', 0)
                            total_area += area
                            
                            print(f"\n   📸 Image {i+1}:")
                            print(f"      📍 Position: ({img.get('left'):.1f}, {img.get('top'):.1f})")
                            print(f"      📏 Dimensions: {img.get('width'):.1f} x {img.get('height'):.1f}")
                            print(f"      📄 Page: {img.get('page_number', 'N/A')}")
                            print(f"      📊 Area: {area:.0f} square units")
                            
                            # Calculate relative position on page
                            page_width = img.get('page_width', 1)
                            page_height = img.get('page_height', 1)
                            rel_x = (img.get('left', 0) / page_width) * 100
                            rel_y = (img.get('top', 0) / page_height) * 100
                            print(f"      📐 Relative position: {rel_x:.1f}% from left, {rel_y:.1f}% from top")
                        
                        print(f"\n📊 Image analysis summary:")
                        print(f"   Total images: {len(image_segments)}")
                        print(f"   Total area: {total_area:.0f} square units")
                        print(f"   Average area: {total_area/len(image_segments):.0f} square units")
                    
                    return {
                        'success': True,
                        'image_count': len(image_segments),
                        'image_segments': image_segments,
                        'total_area': total_area if image_segments else 0
                    }
                
                return {'success': True, 'image_count': 0, 'image_segments': []}
            else:
                print(f"❌ Image extraction failed with status {response.status_code}")
                return None
                
        except Exception as e:
            print(f"❌ Error during image extraction: {e}")
            return None
    
    def test_visualization_export(self, pdf_path):
        """Test PDF visualization export with bounding boxes."""
        print(f"\n{'='*60}")
        print(f"Testing Visualization Export: {os.path.basename(pdf_path)}")
        print(f"{'='*60}")
        
        try:
            with open(pdf_path, 'rb') as f:
                files = {'file': f}
                
                response = requests.post(
                    f"{self.base_url}/visualize",
                    files=files,
                    timeout=120
                )
            
            if response.status_code == 200:
                # Save visualization PDF with timestamp
                timestamp = int(time.time())
                output_path = f"visualization_{timestamp}_{os.path.basename(pdf_path)}"
                with open(output_path, 'wb') as f:
                    f.write(response.content)
                
                print(f"✅ Visualization saved to: {output_path}")
                print(f"📄 File size: {len(response.content)} bytes")
                print(f"👁️  This PDF shows bounding boxes around all detected segments")
                print(f"🖼️  Images should be highlighted with colored boxes")
                
                return {
                    'success': True,
                    'output_path': output_path,
                    'file_size': len(response.content)
                }
            else:
                print(f"❌ Visualization export failed with status {response.status_code}")
                return None
                
        except Exception as e:
            print(f"❌ Error during visualization export: {e}")
            return None
    
    def create_text_visualization(self, segments, page_width=595, page_height=842):
        """Create a simple text-based visualization of segment positions."""
        if not segments:
            return "No segments to visualize"
        
        # Create a grid representation (40x20 characters)
        grid_width, grid_height = 40, 20
        grid = [[' ' for _ in range(grid_width)] for _ in range(grid_height)]
        
        # Map segments to grid positions
        for i, segment in enumerate(segments[:10]):  # Limit to first 10 segments
            left = segment.get('left', 0)
            top = segment.get('top', 0)
            width = segment.get('width', 0)
            height = segment.get('height', 0)
            seg_type = segment.get('type', 'Unknown')
            
            # Convert PDF coordinates to grid coordinates
            grid_x = int((left / page_width) * grid_width)
            grid_y = int((top / page_height) * grid_height)
            grid_w = max(1, int((width / page_width) * grid_width))
            grid_h = max(1, int((height / page_height) * grid_height))
            
            # Choose character based on segment type
            char_map = {
                'title': 'T',
                'text': '.',
                'picture': '#',
                'table': '=',
                'formula': 'F',
                'caption': 'C',
                'header': 'H',
                'footer': 'f'
            }
            char = char_map.get(seg_type.lower(), str(i % 10))
            
            # Fill grid area
            for y in range(min(grid_y, grid_height-1), min(grid_y + grid_h, grid_height)):
                for x in range(min(grid_x, grid_width-1), min(grid_x + grid_w, grid_width)):
                    if 0 <= y < grid_height and 0 <= x < grid_width:
                        grid[y][x] = char
        
        # Convert grid to string
        visualization = []
        visualization.append("📋 Page Layout Visualization (ASCII):")
        visualization.append("+" + "-" * grid_width + "+")
        for row in grid:
            visualization.append("|" + "".join(row) + "|")
        visualization.append("+" + "-" * grid_width + "+")
        
        # Add legend
        visualization.append("\n🗂️  Legend:")
        visualization.append("   T = Title    . = Text     # = Picture")
        visualization.append("   = = Table    F = Formula  C = Caption")
        visualization.append("   H = Header   f = Footer   0-9 = Other")
        
        return "\n".join(visualization)
    
    def run_single_file_tests(self, pdf_path):
        """Run comprehensive tests on a single PDF file."""
        print(f"\n{'='*80}")
        print("TraceFast - PDF Layout Analysis OCR Model Testing")
        print(f"{'='*80}")
        print(f"📄 Testing file: {os.path.basename(pdf_path)}")
        print(f"📁 Full path: {pdf_path}")
        
        # Check service status
        if not self.check_service_status():
            print("❌ Service is not available. Please ensure Docker container is running.")
            return False
        
        # Check if file exists
        if not os.path.exists(pdf_path):
            print(f"❌ PDF file not found: {pdf_path}")
            return False
        
        test_results = {
            'file': pdf_path,
            'tests': {},
            'all_passed': False
        }
        
        print(f"\n🔄 Processing: {os.path.basename(pdf_path)}")
        
        # Test 1: Basic layout analysis (VGT model)
        print(f"\n📋 Running Test 1/6: Layout Analysis (VGT High Accuracy Model)")
        vgt_result = self.test_basic_layout_analysis(pdf_path, use_fast_model=False)
        test_results['tests']['layout_vgt'] = vgt_result is not None
        test_results['detailed_results'] = {'vgt_analysis': vgt_result}
        
        # Test 2: Fast layout analysis (LightGBM model)
        print(f"\n📋 Running Test 2/6: Layout Analysis (LightGBM Fast Model)")
        fast_result = self.test_basic_layout_analysis(pdf_path, use_fast_model=True)
        test_results['tests']['layout_fast'] = fast_result is not None
        test_results['detailed_results']['fast_analysis'] = fast_result
        
        # Test 3: Image extraction (specific focus)
        print(f"\n📋 Running Test 3/6: Image Extraction")
        image_result = self.test_image_extraction(pdf_path)
        test_results['tests']['image_extraction'] = image_result is not None
        test_results['detailed_results']['image_extraction'] = image_result
        
        # Test 4: Text extraction
        print(f"\n📋 Running Test 4/6: Text Extraction")
        text_result = self.test_text_extraction(pdf_path, "title,text,table")
        test_results['tests']['text_extraction'] = text_result is not None
        test_results['detailed_results']['text_extraction'] = text_result
        
        # Test 5: Table OCR
        print(f"\n📋 Running Test 5/6: Table OCR")
        table_result = self.test_table_ocr(pdf_path)
        test_results['tests']['table_ocr'] = table_result is not None
        test_results['detailed_results']['table_ocr'] = table_result
        
        # Test 6: Visualization export
        print(f"\n📋 Running Test 6/6: Visualization Export")
        viz_result = self.test_visualization_export(pdf_path)
        test_results['tests']['visualization'] = viz_result is not None
        test_results['detailed_results']['visualization'] = viz_result
        
        # Check if all tests passed
        test_results['all_passed'] = all(test_results['tests'].values())
        
        # Print final summary
        print(f"\n{'='*80}")
        print("TEST SUMMARY")
        print(f"{'='*80}")
        print(f"📄 File tested: {os.path.basename(pdf_path)}")
        print(f"📊 Test Results:")
        
        for test_name, passed in test_results['tests'].items():
            status = "✅ PASSED" if passed else "❌ FAILED"
            print(f"   {test_name}: {status}")
        
        # Print key statistics from analysis
        if vgt_result and vgt_result.get('success'):
            print(f"\n📈 Analysis Statistics:")
            print(f"   📊 Total segments found: {vgt_result.get('segment_count', 0)}")
            print(f"   🖼️  Images detected: {vgt_result.get('image_count', 0)}")
            print(f"   📝 Total text length: {vgt_result.get('total_text_length', 0)} characters")
            
            # Show segment types breakdown
            segment_types = vgt_result.get('segment_types', {})
            if segment_types:
                print(f"   📋 Content types found:")
                for seg_type, count in sorted(segment_types.items()):
                    emoji = "🖼️" if seg_type.lower() == 'picture' else "📄"
                    print(f"      {emoji} {seg_type}: {count}")
        
        # Show text-based layout visualization
        if vgt_result and vgt_result.get('success') and vgt_result.get('segments'):
            page_width = vgt_result['segments'][0].get('page_width', 595) if vgt_result['segments'] else 595
            page_height = vgt_result['segments'][0].get('page_height', 842) if vgt_result['segments'] else 842
            text_viz = self.create_text_visualization(vgt_result['segments'], page_width, page_height)
            print(f"\n{text_viz}")
        
        # Highlight visualization output
        if viz_result and viz_result.get('success'):
            print(f"\n👁️  Detailed Visualization:")
            print(f"   📁 File: {viz_result.get('output_path')}")
            print(f"   💡 Open this PDF to see precise bounding boxes around detected elements")
            print(f"   🖼️  Images will be highlighted with colored boxes")
        
        if test_results['all_passed']:
            print(f"\n🎉 SUCCESS! All tests passed for {os.path.basename(pdf_path)}")
            print(f"🚀 PDF Layout Analysis OCR is working correctly!")
            print(f"📋 Ready for integration into TraceFast data processing pipeline")
            if vgt_result and vgt_result.get('image_count', 0) > 0:
                print(f"🖼️  Image extraction is working - {vgt_result.get('image_count')} images detected!")
        else:
            failed_tests = [name for name, passed in test_results['tests'].items() if not passed]
            print(f"\n⚠️  Some tests failed: {', '.join(failed_tests)}")
            print(f"💡 Check Docker service and PDF file format")
        
        return test_results


def main():
    """Main function to run the layout OCR tests."""
    
    script_dir = Path(__file__).parent
    
    # Check for command line argument
    if len(sys.argv) > 1:
        pdf_path = sys.argv[1]
        # Convert to absolute path if relative
        if not os.path.isabs(pdf_path):
            pdf_path = script_dir / pdf_path
        pdf_path = str(pdf_path)
    else:
        # Default to med_2_pages.pdf in the same directory
        default_file = script_dir / "med_2_pages.pdf"
        pdf_path = str(default_file)
    
    print(f"🔍 Looking for PDF file: {pdf_path}")
    
    # Check if the file exists
    if not os.path.exists(pdf_path):
        print(f"❌ PDF file not found: {pdf_path}")
        print(f"\nUsage:")
        print(f"  python {os.path.basename(__file__)} [pdf_file_path]")
        print(f"\nExamples:")
        print(f"  python {os.path.basename(__file__)} med_2_pages.pdf")
        print(f"  python {os.path.basename(__file__)} /path/to/your/document.pdf")
        print(f"\nDefault file: med_2_pages.pdf (if no argument provided)")
        return
    
    # Initialize tester and run tests
    tester = LayoutOCRTester()
    results = tester.run_single_file_tests(pdf_path)
    
    if results:
        # Save results for pipeline integration
        results_file = script_dir / "layout_ocr_test_results.json"
        with open(results_file, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        print(f"\n💾 Test results saved to: {results_file}")
    else:
        print(f"\n💥 Tests failed - no results to save")


if __name__ == "__main__":
    main()