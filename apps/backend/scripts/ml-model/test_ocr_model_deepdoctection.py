import deepdoctection as dd
import json
import os
from datetime import datetime

# Configuration
DEBUG_MODE = True  # Set to False for production mode (no visualization)
# DEBUG_MODE = True:  Shows interactive visualization + detailed console output
# DEBUG_MODE = False: Fast processing, only shows progress + saves JSON data

FILTER_EMPTY_TEXT = False  # Set to True to exclude layout items with no text content
# FILTER_EMPTY_TEXT = True:  Only includes layouts with actual text content
# FILTER_EMPTY_TEXT = False: Includes all layouts (visual elements like lines, empty lists, etc.)

analyzer = dd.get_dd_analyzer()

path ="/Users/<USER>/Development/Competition/tracefast/apps/backend/scripts/ml-model/inputs/bid_1.pdf"
# path ="/Users/<USER>/Development/Competition/tracefast/apps/backend/scripts/ml-model/inputs/book1_1.pdf"

df = analyzer.analyze(path=path)
df.reset_state()

# Initialize data structure to store all OCR information
document_data = {
    "metadata": {
        "source_file": path,
        "extraction_timestamp": datetime.now().isoformat(),
        "total_pages": 0,
        "analyzer": "deepdoctection"
    },
    "pages": []
}

# Process all pages in the PDF
for page_num, page in enumerate(df, 1):
    if DEBUG_MODE:
        print(f"=== PAGE {page_num} ===")
        print(f"""height: {page.height}
         width: {page.width}
         file_name: {page.file_name}
         document_id: {page.document_id}
         image_id: {page.image_id}
""")
    else:
        print(f"Processing page {page_num}...")
    
    # Extract page data
    page_data = {
        "page_number": page_num,
        "dimensions": {
            "height": page.height,
            "width": page.width
        },
        "file_name": page.file_name,
        "document_id": page.document_id,
        "image_id": page.image_id,
        "layouts": [],
        "tables": [],
        "figures": []
    }
    
    # Extract layout information
    for layout in page.layouts:
        # Check if text is empty or None and mark accordingly
        text_content = layout.text if layout.text else ""
        has_text = bool(text_content.strip())
        
        layout_data = {
            "category_name": layout.category_name,
            "score": float(layout.score) if layout.score else None,
            "reading_order": layout.reading_order,
            "bounding_box": {
                "x1": layout.bounding_box.ulx,
                "y1": layout.bounding_box.uly,
                "x2": layout.bounding_box.lrx,
                "y2": layout.bounding_box.lry,
                "width": layout.bounding_box.width,
                "height": layout.bounding_box.height
            },
            "annotation_id": layout.annotation_id,
            "text": text_content,
            "has_text": has_text,
            "text_length": len(text_content.strip()) if text_content else 0
        }
        
        # Apply filtering if enabled
        if FILTER_EMPTY_TEXT and not has_text:
            if DEBUG_MODE:
                print(f"  Skipping empty text layout: {layout.category_name} (reading_order: {layout.reading_order})")
            continue
            
        page_data["layouts"].append(layout_data)
    
    # Extract table information if available
    for table in page.tables:
        table_data = {
            "bounding_box": {
                "x1": table.bounding_box.ulx,
                "y1": table.bounding_box.uly,
                "x2": table.bounding_box.lrx,
                "y2": table.bounding_box.lry,
                "width": table.bounding_box.width,
                "height": table.bounding_box.height
            },
            "confidence": float(table.score) if table.score else None,
            "cells": []
        }
        
        # Extract table cells if available
        if hasattr(table, 'cells'):
            for cell in table.cells:
                cell_data = {
                    "text": cell.text if hasattr(cell, 'text') else "",
                    "row": cell.row_number if hasattr(cell, 'row_number') else None,
                    "column": cell.column_number if hasattr(cell, 'column_number') else None,
                    "bounding_box": {
                        "x1": cell.bounding_box.ulx,
                        "y1": cell.bounding_box.uly,
                        "x2": cell.bounding_box.lrx,
                        "y2": cell.bounding_box.lry,
                        "width": cell.bounding_box.width,
                        "height": cell.bounding_box.height
                    }
                }
                table_data["cells"].append(cell_data)
        
        page_data["tables"].append(table_data)
    
    # Extract figure information if available
    if hasattr(page, 'figures'):
        for figure in page.figures:
            figure_data = {
                "category_name": getattr(figure, 'category_name', 'figure'),
                "score": float(figure.score) if hasattr(figure, 'score') and figure.score else None,
                "bounding_box": {
                    "x1": figure.bounding_box.ulx,
                    "y1": figure.bounding_box.uly,
                    "x2": figure.bounding_box.lrx,
                    "y2": figure.bounding_box.lry,
                    "width": figure.bounding_box.width,
                    "height": figure.bounding_box.height
                },
                "annotation_id": getattr(figure, 'annotation_id', None),
                "text": getattr(figure, 'text', '') or '',  # Some figures might have captions
                "figure_type": getattr(figure, 'category_name', 'unknown')
            }
            page_data["figures"].append(figure_data)
        
        if DEBUG_MODE and page.figures:
            print(f"=== PAGE {page_num} FIGURES ===")
            for figure in page.figures:
                print(f"""Figure: {getattr(figure, 'category_name', 'figure')}, \n 
                        score: {getattr(figure, 'score', 'N/A')}, \n
                        bounding_box: {figure.bounding_box}, \n 
                        annotation_id: {getattr(figure, 'annotation_id', 'N/A')} \n 
                        text/caption: {getattr(figure, 'text', 'No caption')} \n""")
    
    document_data["pages"].append(page_data)
    
    # Show visualization only in debug mode
    if DEBUG_MODE:
        # Visualize the current page
        page.viz(interactive=True,
                 show_tables=True,
                 show_layouts=True,
                 show_figures=True,
                 show_residual_layouts=True)
        
        print(f"=== PAGE {page_num} LAYOUTS ===")
        for layout in page.layouts:
            print(f"""Layout segment: {layout.category_name}, \n 
                    score: {layout.score}, \n 
                    reading_order: {layout.reading_order}, \n
                    bounding_box: {layout.bounding_box}, \n 
                    annotation_id: {layout.annotation_id} \n \n 
                    text: {layout.text} \n""")
        
        print(f"--- End of Page {page_num} ---\n")

# Update total pages count
document_data["metadata"]["total_pages"] = len(document_data["pages"])

# Save to JSON file
output_filename = f"ocr_extraction_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
output_path = os.path.join(os.path.dirname(path), "..", "outputs", output_filename)

# Ensure output directory exists
os.makedirs(os.path.dirname(output_path), exist_ok=True)

# Save the data
with open(output_path, 'w', encoding='utf-8') as f:
    json.dump(document_data, f, indent=2, ensure_ascii=False)

mode_text = "Debug mode (with visualization)" if DEBUG_MODE else "Production mode (no visualization)"
print(f"\n🎉 OCR extraction completed in {mode_text}")
print(f"💾 Data saved to: {output_path}")
print(f"📊 Extracted data summary:")
print(f"   - Total pages: {document_data['metadata']['total_pages']}")
print(f"   - Total layouts: {sum(len(page['layouts']) for page in document_data['pages'])}")
print(f"   - Total tables: {sum(len(page['tables']) for page in document_data['pages'])}")
print(f"   - Total figures: {sum(len(page['figures']) for page in document_data['pages'])}")
print(f"   - Layouts with text: {sum(len([l for l in page['layouts'] if l.get('has_text', False)]) for page in document_data['pages'])}")
print(f"   - Layouts without text: {sum(len([l for l in page['layouts'] if not l.get('has_text', True)]) for page in document_data['pages'])}")