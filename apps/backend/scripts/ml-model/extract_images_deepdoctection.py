import deepdoctection as dd
import matplotlib.pyplot as plt
import os

# Initialize the analyzer
analyzer = dd.get_dd_analyzer()

# Path to the PDF file
path = "/Users/<USER>/Development/Competition/tracefast/apps/backend/scripts/ml-model/inputs/book1_1.pdf"

# Output directory for extracted images
output_dir = "/Users/<USER>/Development/Competition/tracefast/apps/backend/scripts/ml-model/outputs"

# Create output directory if it doesn't exist
os.makedirs(output_dir, exist_ok=True)

# Analyze the document
df = analyzer.analyze(path=path)
df.reset_state()

# Process each page in the document
doc = iter(df)
page_count = 0

for page in doc:
    page_count += 1
    print(f"Processing page {page_count}...")
    
    # Check if there are any figures on this page
    if page.figures:
        print(f"Found {len(page.figures)} figure(s) on page {page_count}")
        
        # Extract each figure
        for i, figure in enumerate(page.figures):
            try:
                # Generate filename for the extracted image
                filename = f"page_{page_count}_figure_{i+1}_{figure.annotation_id}.png"
                output_path = os.path.join(output_dir, filename)
                
                # Save the figure image
                dd.viz_handler.write_image(output_path, figure.image.image)
                
                print(f"Saved figure to: {output_path}")
                print(f"  - Category: {figure.category_name}")
                print(f"  - Score: {figure.score}")
                print(f"  - Bounding box: {figure.bounding_box}")
                print(f"  - Annotation ID: {figure.annotation_id}")
                print()
                
            except Exception as e:
                print(f"Error saving figure {i+1} on page {page_count}: {str(e)}")
    else:
        print(f"No figures found on page {page_count}")
    
    print("-" * 50)

print(f"Image extraction completed. Check the output directory: {output_dir}")
