# ML Model Testing Scripts

This directory contains testing scripts for various machine learning models used in the TraceFast project. These scripts help validate and benchmark the performance of different AI/ML components before integration into the main application.

## Overview

The TraceFast project uses several ML models for document analysis, text embedding, and procurement analysis:

- **Text Embedding Models**: For semantic similarity and document matching
- **OCR & Layout Analysis**: For PDF document processing and structure extraction  
- **Document Detection**: For comprehensive document analysis and visualization

## Scripts

### 1. `test_embedding_model.py`

**Purpose**: Tests the Qwen/Qwen3-Embedding-0.6B model for text similarity and semantic search in procurement contexts.

**Features**:
- Text embedding generation with query and document prompts
- Cosine similarity calculation between queries and documents
- Procurement-specific query testing
- Performance benchmarking and timing analysis
- Support for CUDA, MPS (Apple Silicon), and CPU

**Requirements**:
```bash
transformers>=4.51.0
sentence-transformers>=2.7.0
torch
```

**Usage**:
```bash
# Run from the backend directory
cd apps/backend
uv run python scripts/ml-model/test_embedding_model.py
```

**What it tests**:
- Documentation examples (capital of China, gravity explanation)
- Procurement queries (evaluation criteria, budget allocation, qualified bidders, technical requirements, deadlines)
- Performance metrics (loading time, embedding generation time)
- Similarity matrix generation

**Expected Output**:
- Model loading confirmation and timing
- Similarity scores between queries and documents
- Procurement-specific matching results
- Performance summary with timing metrics

---

### 2. `test_layout_ocr_model.py` 

**Purpose**: Tests a Docker-based PDF layout analysis microservice for document structure detection and text extraction.

**Features**:
- PDF layout analysis with VGT (high accuracy) and LightGBM (fast) models
- Text extraction by content type (title, text, table)
- Table OCR functionality
- PDF visualization export
- Single file focused testing with detailed progress tracking

**Requirements**:
```bash
requests
```

**Prerequisites**:
- Docker service running on `localhost:5060`
- A PDF file for testing

**Usage**:
```bash
# Ensure Docker service is running first
# Then run from the backend directory
cd apps/backend

# Test with specific file
uv run python scripts/ml-model/test_layout_ocr_model.py your_document.pdf

# Test with absolute path
uv run python scripts/ml-model/test_layout_ocr_model.py /path/to/your/document.pdf

# Test with default file (med_2_pages.pdf)
uv run python scripts/ml-model/test_layout_ocr_model.py
```

**What it tests**:
- Service connectivity and status
- Basic layout analysis (both fast and accurate models)
- **Image/picture extraction and analysis** (dedicated test)
- Specific text extraction by content types
- Table recognition and OCR
- PDF visualization generation with bounding boxes

**Expected Output**:
- Service status confirmation
- Step-by-step test progress (1/6, 2/6, etc.)
- **Detailed segment analysis** with coordinates, dimensions, and content
- **Image detection statistics** (count, positions, areas)
- **ASCII visualization** of document layout in console
- Text extraction results by content type
- Table detection and content preview
- **Visual PDF with bounding boxes** around all detected elements
- **Comprehensive JSON results** with all extracted data
- Final test summary with detailed statistics

**Default Test File**:
If no file path is provided, the script defaults to `med_2_pages.pdf` in the same directory.

**New Visualization Features**:
1. **Console ASCII Layout**: Real-time text-based visualization showing document structure
   - `#` = Images/Pictures, `T` = Titles, `.` = Text, `=` = Tables
   - Shows relative positions and sizes of elements
2. **Enhanced Image Analysis**: Dedicated image extraction with detailed statistics
   - Position coordinates and dimensions
   - Relative positioning on page (percentages)
   - Total area calculations
3. **Visual PDF Output**: Generated PDF with colored bounding boxes
   - All segments highlighted with different colors
   - Easy to verify detection accuracy
   - Timestamped filenames to avoid overwrites

---

### 3. `test_ocr_model_deepdoctection.py`

**Purpose**: Tests the deepdoctection library for PDF document analysis and layout detection.

**Features**:
- Document structure analysis
- Layout segment detection and categorization
- Interactive visualization
- Bounding box and reading order detection
- Text extraction from layout segments

**Requirements**:
```bash
deepdoctection
```

**Usage**:
```bash
# Run from the backend directory
cd apps/backend
uv run python scripts/ml-model/test_ocr_model_deepdoctection.py
```

**What it tests**:
- PDF document loading and analysis
- Page dimensions and metadata extraction
- Layout segment detection (titles, text blocks, tables, figures)
- Interactive visualization
- Text content extraction from each segment

**Expected Output**:
- Page metadata (height, width, file name, document ID)
- Interactive visualization window
- Layout segment details with scores, positions, and text content

**Note**: This script is currently configured to analyze `med_2_pages.pdf` and will open an interactive visualization window.

## Installation

To install all required dependencies for the ML model testing scripts:

```bash
# Navigate to the backend directory
cd apps/backend

# Install dependencies using uv
uv add transformers sentence-transformers torch requests deepdoctection
```

## Running All Tests

To test all ML models in sequence:

```bash
cd apps/backend

# Test embedding model
echo "Testing Embedding Model..."
uv run python scripts/ml-model/test_embedding_model.py

# Test layout OCR with default file (ensure Docker service is running)
echo "Testing Layout OCR Model..."
uv run python scripts/ml-model/test_layout_ocr_model.py

# Or test layout OCR with specific file
echo "Testing Layout OCR Model with custom file..."
uv run python scripts/ml-model/test_layout_ocr_model.py your_document.pdf

# Test deepdoctection
echo "Testing DeepDoctection Model..."
uv run python scripts/ml-model/test_ocr_model_deepdoctection.py
```

## Test Data

For optimal testing, ensure you have at least one sample PDF file available:

- **Procurement documents**: Tender documents, bid proposals, evaluation criteria
- **Multi-page documents**: Complex layouts with tables and figures
- **Various formats**: Different PDF structures and content types

**Default test file**: The layout OCR script defaults to `med_2_pages.pdf` in the `scripts/ml-model/` directory if no file is specified.

## Integration Notes

These scripts are designed to validate ML model functionality before integration into the main TraceFast application:

1. **Embedding Model**: Used for document similarity analysis in bid comparison
2. **Layout OCR**: Used for PDF document processing and content extraction
3. **DeepDoctection**: Used for comprehensive document analysis and structure detection

## Troubleshooting

### Common Issues

**Embedding Model**:
- Ensure transformers and sentence-transformers are installed
- Check CUDA/MPS availability for GPU acceleration
- Verify internet connection for model downloading

**Layout OCR**:
- Ensure Docker service is running on localhost:5060
- Check PDF file permissions and existence
- Verify network connectivity to Docker container

**DeepDoctection**:
- Install all deepdoctection dependencies
- Ensure PDF file path is correct
- Check display environment for interactive visualization

### Performance Tips

- Use CUDA or MPS for faster embedding generation
- Use fast model option for layout analysis during development
- Process smaller PDF files first to validate functionality
- Monitor memory usage with large documents

## Output Files

Scripts may generate output files:

- `layout_ocr_test_results.json`: **Enhanced test results** with detailed segment data
  - Pass/fail status for each test
  - Complete segment data with coordinates and content
  - Image extraction statistics
  - Performance metrics
- `visualization_[timestamp]_[filename].pdf`: **Visual PDF** with colored bounding boxes around detected elements
- Console output with **ASCII layout visualization** for immediate feedback

## Contributing

When adding new ML model testing scripts:

1. Follow the existing naming convention: `test_*_model.py`
2. Include comprehensive documentation and usage examples
3. Add error handling and performance metrics
4. Update this README with script details
5. Ensure compatibility with the uv package manager
