"""
Qwen3-Embedding-0.6B Model Testing Script

This script focuses specifically on testing the Qwen/Qwen3-Embedding-0.6B model
for the TraceFast project.

Requirements:
- transformers>=4.51.0
- sentence-transformers>=2.7.0
"""

import torch
import torch.nn.functional as F
from sentence_transformers import SentenceTransformer
import time


def test_qwen3_model():
    """Test the Qwen/Qwen3-Embedding-0.6B model with both examples and procurement data."""
    
    print("=" * 60)
    print("Testing Qwen/Qwen3-Embedding-0.6B Model")
    print("=" * 60)
    
    try:
        print("Loading Qwen3 embedding model...")
        start_time = time.time()
        
        # Load the Qwen3 model
        model = SentenceTransformer("Qwen/Qwen3-Embedding-0.6B")
        
        load_time = time.time() - start_time
        print(f"✅ Model loaded in {load_time:.2f} seconds")
        
        # Test 1: Original example from documentation
        print(f"\n{'='*20} Test 1: Documentation Example {'='*20}")
        
        queries = [
            "What is the capital of China?",
            "Explain gravity",
        ]
        documents = [
            "The capital of China is Beijing.",
            "Gravity is a force that attracts two bodies towards each other. It gives weight to physical objects and is responsible for the movement of planets around the sun.",
        ]
        
        embedding_start = time.time()
        
        # Encode queries with prompt and documents without
        query_embeddings = model.encode(queries, prompt_name="query")
        document_embeddings = model.encode(documents)
        
        embedding_time = time.time() - embedding_start
        print(f"✅ Embeddings generated in {embedding_time:.2f} seconds")
        
        # Convert to tensors and compute similarity using cosine similarity
        query_tensor = torch.from_numpy(query_embeddings)
        doc_tensor = torch.from_numpy(document_embeddings)
        
        similarity = F.cosine_similarity(
            query_tensor.unsqueeze(1), 
            doc_tensor.unsqueeze(0), 
            dim=2
        )
        
        print("\nSimilarity Matrix:")
        print(similarity.numpy())
        
        print("\nDetailed Results:")
        for i, query in enumerate(queries):
            print(f"\n🔍 Query {i+1}: {query}")
            for j, document in enumerate(documents):
                score = similarity[i][j].item()
                print(f"   Document {j+1} similarity: {score:.4f} ({score*100:.2f}%)")
                print(f"   Document: {document[:60]}...")
        
        # Test 2: Procurement-specific queries
        print(f"\n{'='*20} Test 2: Procurement Analysis {'='*20}")
        
        procurement_queries = [
            "What are the evaluation criteria for this tender?",
            "What is the budget allocation for IT services?", 
            "Who are the qualified bidders for construction projects?",
            "What are the technical requirements for this project?",
            "When is the submission deadline?"
        ]
        
        procurement_documents = [
            "The evaluation criteria for this tender include: 1) Technical capability (40%), 2) Financial stability (30%), 3) Past performance (20%), and 4) Innovation and sustainability (10%). Bidders must demonstrate compliance with all technical specifications and provide evidence of successful completion of similar projects within the last 5 years.",
            "The total budget allocation for the current fiscal year is $2.5 million, with IT services receiving $800,000 (32%), infrastructure development $1.2 million (48%), and consulting services $500,000 (20%). All procurements must adhere to competitive bidding processes as per government procurement regulations.",
            "Qualified bidders for construction projects must meet the following criteria: valid construction license, minimum 10 years experience, financial capacity of at least $5 million, safety certification, and demonstrated experience in public sector projects. Current pre-qualified contractors include ABC Construction Ltd, BuildRight Corp, and Metropolitan Builders Inc.",
            "Technical requirements include: compatibility with existing systems, 99.9% uptime guarantee, 24/7 technical support, compliance with ISO 27001 security standards, scalability to handle 10,000+ concurrent users, and integration with legacy databases. All solutions must be deployed on-premises with optional cloud backup.",
            "The submission deadline for all proposals is December 15th, 2024 at 5:00 PM EST. Late submissions will not be accepted. All documents must be submitted electronically through the procurement portal. Bidders are required to attend the mandatory pre-bid conference on November 20th, 2024."
        ]
        
        proc_start = time.time()
        proc_query_embeddings = model.encode(procurement_queries, prompt_name="query")
        proc_doc_embeddings = model.encode(procurement_documents)
        proc_time = time.time() - proc_start
        
        # Convert to tensors for similarity calculation
        proc_query_tensor = torch.from_numpy(proc_query_embeddings)
        proc_doc_tensor = torch.from_numpy(proc_doc_embeddings)
        
        proc_similarity = F.cosine_similarity(
            proc_query_tensor.unsqueeze(1), 
            proc_doc_tensor.unsqueeze(0), 
            dim=2
        )
        
        print(f"✅ Procurement embeddings generated in {proc_time:.2f} seconds")
        
        print("\nProcurement Query-Document Matching:")
        print("-" * 50)
        
        for i, query in enumerate(procurement_queries):
            best_match_idx = proc_similarity[i].argmax().item()
            best_score = proc_similarity[i][best_match_idx].item()
            
            print(f"\n🔍 Query: {query}")
            print(f"   ✅ Best match (Score: {best_score:.4f} / {best_score*100:.2f}%):")
            print(f"   📄 {procurement_documents[best_match_idx][:120]}...")
            
            # Show all scores for this query
            print(f"   📊 All scores:")
            for j, doc_score in enumerate(proc_similarity[i]):
                print(f"      Doc {j+1}: {doc_score.item():.4f}")
        
        # Test 3: Performance summary
        print(f"\n{'='*20} Performance Summary {'='*20}")
        total_time = load_time + embedding_time + proc_time
        
        print(f"📈 Model: Qwen/Qwen3-Embedding-0.6B")
        print(f"📈 Model loading time: {load_time:.2f}s")
        print(f"📈 Example embedding time: {embedding_time:.2f}s")
        print(f"📈 Procurement embedding time: {proc_time:.2f}s")
        print(f"📈 Total test time: {total_time:.2f}s")
        print(f"📈 Device: {'CUDA' if torch.cuda.is_available() else 'CPU'}")
        
        return {
            "model_name": "Qwen/Qwen3-Embedding-0.6B",
            "load_time": load_time,
            "embedding_time": embedding_time,
            "procurement_time": proc_time,
            "total_time": total_time,
            "example_similarity": similarity.numpy().tolist(),
            "procurement_similarity": proc_similarity.numpy().tolist(),
            "success": True
        }
        
    except Exception as e:
        print(f"❌ Error testing Qwen3 embedding model: {str(e)}")
        import traceback
        traceback.print_exc()
        return {
            "model_name": "Qwen/Qwen3-Embedding-0.6B",
            "error": str(e),
            "success": False
        }


def main():
    """Main function to test the Qwen3 model."""
    
    print("TraceFast - Qwen3 Embedding Model Testing")
    print("Testing Qwen/Qwen3-Embedding-0.6B for procurement analysis")
    print(f"PyTorch version: {torch.__version__}")
    
    # Check device availability
    if torch.cuda.is_available():
        print(f"🚀 CUDA available: {torch.cuda.get_device_name(0)}")
    elif hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
        print("🚀 MPS (Apple Silicon) available")
    else:
        print("💻 Using CPU")
    
    # Test the model
    results = test_qwen3_model()
    
    if results["success"]:
        print(f"\n🎉 SUCCESS! Qwen3 model testing completed successfully!")
        print(f"🎯 The model is ready for use in your TraceFast project")
        print(f"⚡ Total processing time: {results['total_time']:.2f} seconds")
    else:
        print(f"\n💥 FAILED! Error: {results['error']}")
        print("Please check the error details above and ensure all dependencies are installed correctly.")


if __name__ == "__main__":
    main()
