# claude
.claude/
logs/

node_modules
.DS_Store
dist
dist-ssr
*.local
count.txt
.env
.nitro
.tanstack

# Python
__pycache__/
*.py[cod]
*$py.class

# Virtual environments
.venv/
venv/
env/
.python-version

# Test and coverage
.pytest_cache/
.mypy_cache/
.ruff_cache/
.coverage
.coverage.*
coverage.xml
htmlcov/

# Build artifacts
build/
develop-eggs/
dist/
downloads/
eggs/
*.egg-info/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/

# Database
*.db

# Storage
storage/

# Logs
*.log

# IDE/editor
.idea/
.vscode/

# uploads
uploads/
inputs/
outputs/