
# TraceFast 公共採購標案洞察平台

## —— 專為新世代審計而生的智能仪表板

**文件版本：** 3.0  
**最後更新日期：** 2025年8月11日

---

## 一、專案摘要 (Executive Summary)

公共採購是政府運作的核心，亦是貪腐與利益輸送的高風險領域。傳統的人工審查方式在面對海量、複雜的標案文件時，不僅耗時耗力，且難以發現隱藏在字裡行間的圍標、抄襲等舞弊行為。「TraceFast 洞察平台」是一個專為解決此痛點而設計的 AI 輔助工具。我們**並非要取代**專業的審計人員，而是旨在**賦能**他們。本平台透過多維度的文件比對引擎，自動分析文件間的相似性，並通過直觀的**儀表板界面**呈現分析結果，包含熱力圖矩陣、統計面板、詳細比較頁面和智能報告生成功能。使用者可以從全局視角快速識別可疑模式，深入特定文件對比，並生成專業的審計報告，最終由人類專家做出判斷。我們的目標是大幅提升審查效率，增強洞察力，讓廉潔的陽光能照進更深的角落。

## 二、背景與問題陳述 (Background & Problem Statement)

### 問題的嚴重性
每年，全球有數以萬億計的公共資金投入到基礎建設、服務採購等項目中。根據統計，其中有相當比例因貪腐或效率不彰而流失，直接損害公眾利益。

### 傳統審查的困境
- **人力與時間成本高**：審計人員需閱讀成千上萬頁的文件，進行枯燥的比對，如同大海撈針。
- **隱蔽性高**：圍標或合謀者常透過「換句話說」、使用相同模板等方式抄襲標書，傳統的關鍵字比對難以奏效。
- **缺乏全局視角**：在獨立的文件之間建立關聯性極其困難，審計人員容易陷入細節，錯失宏觀的關係網絡。
- **專業門檻高**：一般公眾或調查記者難以有效監督，資訊的不對稱成為貪腐的溫床。

## 三、我們的理念與目標 (Our Philosophy & Goals)

### 核心理念：賦能，而非取代 (Empower, Not Replace)

我們深刻理解，反貪腐的最後一哩路，永遠需要人類的專業、直覺與判斷力。AI 的角色是成為最強大的**「輔助駕駛」**，而非「自動駕駛」。因此，我們的所有設計都圍繞此核心理念展開。

### 我們的目標
- **提升效率 (Increase Efficiency)**：將審計人員從重複、繁瑣的文件比對工作中解放出來，讓他們能專注於更高層次的分析與決策。
- **增強洞察 (Enhance Insight)**：透過 AI 揭示肉眼難以察覺的深度關聯，例如寫作風格的相似性、文件結構的雷同等。
- **降低監督門檻 (Lower the Barrier to Entry)**：提供一個直觀易用的工具，讓更多非傳統審計背景的監督力量（如記者、公民團體）也能參與進來。

## 四、解決方案：功能詳述 (Solution: Detailed Features)

### A. 智慧文件比對引擎 (Intelligent Document Comparison Engine)

這是平台的分析核心（這些算法可以日後再加上去，所以系統需要 scalable），它從多個維度解析並比對文件：

1. **語義相似度分析 (Semantic Similarity)**  
   先使用 semantic chunking，按不同文字段落去分段，然後採用先進的 embedding model 去轉換成 vector，然後進行 vector similarity comparison。

2. **寫作風格分析 (Stylometry)**  
   分析文件的「寫作指紋」，如句長、標點、用詞習慣，識別不同標書是否可能出自同一作者。

3. **結構與模板分析 (Structural Analysis)**  
   **使用 Layout OCR 技術，從視覺層面判斷整個文檔的重合性**。比對文件的章節順序、編號方式、表格設計、版面配置等，找出使用相同「母檔」的證據。

4. **關鍵字與數值分析 (Keyword & Numerical Analysis)**  
   專門提取並比對價格、規格參數、專有名詞等，標示出不尋常的巧合。

### B. 智能儀表板分析系統 (Intelligent Dashboard Analysis System)

這是平台的視覺化與人機互動核心：

1. **主儀表板與熱力圖矩陣 (Main Dashboard & Heatmap Matrix)**  
   系統提供一個結構化的儀表板主頁，核心是熱力圖矩陣，直觀顯示所有公司文件間的相似度分數。使用者可快速識別高風險的文件對，並配有統計面板顯示項目總覽、公司數量、分析進度等關鍵指標。

2. **詳細比較頁面 (Detailed Comparison Pages)**  
   點擊熱力圖中的任意單元格，即可進入該文件對的詳細比較頁面。頁面採用算法標籤分類（語義相似、結構相似、數值相似、文體風格等），每個標籤下顯示具體的比較卡片，包含證據截圖、相似度分數和AI分析摘要。

3. **並排PDF查看器 (Side-by-Side PDF Viewer)**  
   增強的文件查看器支援並排顯示兩個PDF文件，並能根據分析結果自動高亮可疑的文本區塊（chunks）。使用者可以點擊比較卡片直接跳轉到對應的PDF位置，實現無縫的證據驗證體驗。

4. **智能報告生成系統 (Intelligent Report Generation)**  
   使用者可從詳細比較頁面選擇關鍵發現，系統運用AI自動生成結構化的審計報告草稿。內建富文本編輯器支援報告的進一步編輯，並可匯出為PDF、Word、HTML等多種格式，滿足不同的審計報告需求。

5. **項目與公司側邊欄 (Project & Company Sidebars)**  
   左側邊欄顯示項目列表，支援快速切換分析對象；右側邊欄展示公司資訊，包含基本資料、歷史參標記錄等背景信息，為審計人員提供更全面的分析脈絡。

## 五、目標用戶 (Target Users)

- **主要用戶**：政府審計單位、監察機構、司法調查單位的專業人員。
- **次要用戶**：進行調查報導的記者、公民監督團體、學術研究機構。

## 六、創新與獨特價值 (Innovation & Unique Value)

1. **結構化分析工作流程**  
   我們的核心創新在於「儀表板→詳細比較→報告生成」的結構化工作流程，從宏觀的熱力圖矩陣快速識別可疑模式，到微觀的證據分析和專業報告生成，完美結合了 AI 的計算能力與人類的專業判斷。

2. **從「找相似」到「解釋為什麼」**  
   我們不只告訴用戶「哪裡像」，更透過 AI 嘗試解釋「為什麼像」，並在詳細比較頁面中提供完整的證據鏈和分析脈絡。

3. **多維度證據整合**  
   融合四種以上的分析維度，每個維度都有專屬的算法標籤和比較卡片，為每一個疑點提供更立體、更可信的證據支持。

4. **熱力圖全局視角**  
   通過熱力圖矩陣提供項目級別的全局視角，讓審計人員能快速識別高風險的公司組合，大幅提升審查效率和覆蓋範圍。

5. **無縫證據驗證**  
   並排PDF查看器與比較卡片的聯動設計，使用者可以一鍵從分析結果跳轉到原始文件的具體位置，實現快速、準確的證據驗證。

## 七、技術架構概覽 (Technical Architecture Overview)

### A. 數據庫設計 (Database Design)

本平台採用PostgreSQL數據庫，配合pgvector擴展支援向量相似度計算：

1. **項目表 (Projects)**：儲存審計項目基本信息，包含項目名稱、狀態、創建時間等。

2. **文件表 (Documents)**：記錄每個參標公司的文件信息，包含公司名稱、原始檔名、儲存路徑、頁數等。

3. **文件區塊表 (Document Chunks)**：儲存文件解析後的文本區塊，包含頁碼、區塊類型、OCR內容、位置座標和語義向量。

4. **分析結果表 (Analysis Results)**：核心數據表，使用JSONB格式儲存各算法的分析分數和詳細發現，支援靈活的算法擴展。

### B. 系統架構 (System Architecture)

1. **前端路由架構**：
   - `/` - 文件上傳頁面
   - `/dashboard` - 主儀表板（熱力圖和統計）
   - `/comparison/:doc1Id/:doc2Id` - 詳細比較頁面
   - `/report/:projectId` - 報告生成頁面

2. **核心組件**：
   - `HeatmapMatrix` - 互動式相似度矩陣視覺化
   - `StatisticsPanel` - 項目和分析統計儀表板
   - `ComparisonCard` - 個別發現卡片與證據展示
   - `ReportEditor` - 富文本編輯器，支援審計報告生成

3. **API端點設計**：
   - `GET /api/projects/:id/heatmap` - 熱力圖數據
   - `GET /api/projects/:id/statistics` - 儀表板統計
   - `GET /api/analysis/:doc1/:doc2/findings` - 詳細分析發現
   - `POST /api/reports/generate` - AI報告生成
   - `GET /api/documents/:id/chunks` - 文件區塊數據

### C. 數據流程 (Data Flow)

1. **儀表板流程**：項目選擇 → 載入熱力圖數據 → 顯示相似性矩陣 → 統計面板更新
2. **比較流程**：熱力圖點擊 → 載入詳細發現 → 解析為比較卡片 → 啟用PDF並排查看
3. **報告流程**：選擇關鍵發現 → AI生成報告草稿 → 富文本編輯 → 多格式匯出

## 八、團隊願景 (Team Vision)

作為一個年輕的學生團隊，我們無意宣稱能徹底解決貪腐問題。我們的願景是，透過「TraceFast」這個專案，**點燃一個創新的火花**，證明新技術能為古老的反貪腐事業帶來全新的可能性。我們希望播下一顆種子，讓「效率」與「透明」成為未來公共採購領域的常態，並以我們的熱情與創意，為建立一個更廉潔、更公正的社會貢獻一份微薄但堅實的力量。