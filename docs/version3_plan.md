# TraceFast Version 3: Dashboard-Based Presentation Plan

## Overview
Pivot from the current whiteboard ReactFlow interface to a structured dashboard presentation with heatmaps, statistics, PDF comparisons, and AI-generated reports.

## Database Schema Design

Based on the provided database structure, we have four core entities:

### 1. Projects Table
```sql
CREATE TABLE projects (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,                    -- Project name, e.g., "Macau LRT East Line Construction Tender"
    status VARCHAR(50) NOT NULL,                   -- Project status: 'uploading', 'analyzing', 'completed', 'error'
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### 2. Documents Table
```sql
CREATE TABLE documents (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    project_id UUID NOT NULL REFERENCES projects(id),
    company_name VA<PERSON>HA<PERSON>(255),                         -- Company name, e.g., "NewForce Construction"
    original_filename VARCHAR(255) NOT NULL,           -- Original filename
    storage_path VARCHAR(1024) NOT NULL,               -- File path in cloud storage (e.g., S3)
    page_count INT,                                    -- Total page count
    status VARCHAR(50) NOT NULL,                       -- Processing status: 'pending', 'processing', 'processed', 'error'
    uploaded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### 3. Document Chunks Table
```sql
CREATE TABLE document_chunks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    document_id UUID NOT NULL REFERENCES documents(id),
    page_number INT NOT NULL,                          -- Page number where chunk is located
    chunk_type VARCHAR(50) NOT NULL,                   -- Chunk type: 'text', 'image', 'table'
    content TEXT,                                      -- OCR text content for text chunks
    bounding_box JSONB NOT NULL,                       -- Chunk position coordinates: {'x1': 100, 'y1': 200, 'x2': 500, 'y2': 250}
    embedding VECTOR(1536)                             -- Semantic vector for semantic comparison (requires pgvector)
);
```

### 4. Analysis Results Table (Core Table)
```sql
CREATE TABLE analysis_results (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    project_id UUID NOT NULL REFERENCES projects(id),
    document_1_id UUID NOT NULL REFERENCES documents(id),
    document_2_id UUID NOT NULL REFERENCES documents(id),
    
    -- JSONB field for algorithm extensibility
    overall_scores JSONB NOT NULL, 
    
    -- JSONB field storing detailed chunk comparison evidence
    detailed_findings JSONB,
    
    analysis_version VARCHAR(50),                        -- Algorithm version used for analysis
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Ensure only one analysis result per document pair
    UNIQUE(project_id, document_1_id, document_2_id)
);
```

### JSONB Field Structures

#### overall_scores Example:
```json
{
  "overall_risk": 0.85,
  "text_semantic_similarity": 0.92,
  "layout_similarity": 0.98,
  "pricing_model_similarity": 0.75,
  "new_future_algorithm_score": 0.60
}
```

#### detailed_findings Example:
```json
[
  {
    "finding_type": "text_semantic_similarity",
    "risk_level": "high",
    "summary": "High similarity found in 'Construction Safety Plan' section with identical typos.",
    "evidence": {
      "chunk_id_1": "uuid-of-chunk-from-doc1",
      "chunk_id_2": "uuid-of-chunk-from-doc2",
      "score": 0.92,
      "highlight_details": {
          "text_1": "...All scaffold nodes must use M16 fixing screws...",
          "text_2": "...All scaffold nodes must use M16 fixing screws..."
      }
    }
  },
  {
    "finding_type": "layout_similarity",
    "risk_level": "high",
    "summary": "Pages 5-8 layout structure similarity reaches 98%",
    "evidence": {
        "page_1": 5,
        "page_2": 5,
        "score": 0.98,
        "overlay_data": { }
    }
  }
]
```

## Implementation Plan

### Phase 1: Dashboard Page Structure
- **Create new Dashboard component** replacing the whiteboard as the main view
- **Implement heatmap visualization** showing similarity scores between company pairs in a matrix format
- **Add overall statistics panel** displaying project counts, company counts, analysis summaries
- **Restructure sidebars**: Project list (left) and Company list with basic info (right)

#### Key Queries for Dashboard:
```sql
-- Heatmap data
SELECT document_1_id, document_2_id, overall_scores ->> 'overall_risk' AS risk_score 
FROM analysis_results WHERE project_id = 'your-project-id';

-- Project statistics
SELECT COUNT(*) as total_projects, 
       COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_projects
FROM projects;
```

### Phase 2: Detail Pages & Comparison Cards
- **Build comparison cards component** showing pairwise analysis with algorithm-based tabs (semantic, structural, numerical, stylometry)
- **Create detail view routing** for in-depth analysis of specific company pairs
- **Implement PDF viewer integration** leveraging existing DocumentViewer component

#### Key Queries for Detail Pages:
```sql
-- Get detailed findings for comparison page
SELECT detailed_findings FROM analysis_results 
WHERE document_1_id = 'doc-A-id' AND document_2_id = 'doc-B-id';

-- Get chunk content for evidence display
SELECT content, bounding_box FROM document_chunks 
WHERE id IN ('chunk_id_1', 'chunk_id_2');
```

### Phase 3: PDF Comparison Dialog
- **Enhance DocumentViewer** for side-by-side PDF display
- **Add chunk boundary highlighting** using existing layout.json data from backend
- **Implement click-to-scroll functionality** linking comparison cards to specific PDF sections
- **Build report card selection system** for users to add findings to audit reports

### Phase 4: Report Generation Page
- **Create AI-powered report generator** using selected comparison analyses
- **Build rich text editor** for report editing before export
- **Implement export functionality** (PDF, Word, HTML formats)
- **Add report templates** following audit report structure from designs

### Phase 5: Data Architecture Updates
- **Update backend models** to match new database schema
- **Enhance API endpoints** for heatmap data and statistics
- **Update state management** for new dashboard-focused workflows  
- **Integrate with existing analysis pipeline**

## Technical Implementation Details

### New Routes
- `/` - Upload Page (existing, keep same)
- `/dashboard` - Main dashboard with heatmap and statistics
- `/comparison/:doc1Id/:doc2Id` - Detail comparison page
- `/report/:projectId` - Report generation and editing page

### New Components
- `HeatmapMatrix` - Interactive similarity matrix visualization
- `StatisticsPanel` - Project and analysis statistics dashboard
- `ComparisonCard` - Individual finding cards with evidence
- `ReportEditor` - Rich text editor for audit report generation
- `CompanyProfileSidebar` - Right sidebar with company information
- `ProjectSidebar` - Left sidebar with project list

### Enhanced Components
- `DocumentViewer` - Enhanced for side-by-side PDF display with chunk highlighting
- `MainLayout` - Updated to support new dashboard layout requirements

### Backend Updates
- **New API endpoints**:
  - `GET /api/projects/:id/heatmap` - Heatmap data
  - `GET /api/projects/:id/statistics` - Dashboard statistics
  - `GET /api/analysis/:doc1/:doc2/findings` - Detailed findings
  - `POST /api/reports/generate` - AI report generation
  - `GET /api/documents/:id/chunks` - Document chunk data

### Database Migration Strategy
1. **Phase 1**: Create new tables alongside existing ones
2. **Phase 2**: Migrate existing data to new schema
3. **Phase 3**: Update application to use new schema
4. **Phase 4**: Remove old tables after verification

### Data Flow Architecture

#### Dashboard Flow:
1. User selects project → Load heatmap data from `analysis_results`
2. Display similarity matrix between all document pairs
3. Show statistics panel with project metrics
4. Populate sidebars with projects and companies

#### Comparison Flow:
1. User clicks heatmap cell → Navigate to comparison page
2. Load `detailed_findings` from `analysis_results`
3. Parse JSON findings into comparison cards
4. Load chunk content for evidence display
5. Enable PDF side-by-side viewing with highlighting

#### Report Flow:
1. User selects findings from comparison pages
2. AI generates structured report from selected evidence
3. User edits report in rich text editor
4. Export to various formats (PDF, Word, HTML)

## Migration from Current Whiteboard System

### Data Mapping
- Current `SuspiciousSegment` → New `detailed_findings` JSON structure
- Current `AnalysisResult` → New `analysis_results` table
- Current `Tender` → New `projects` table
- Current `Bid` → New `documents` table

### Component Migration
- `DetectiveWhiteboard` → `Dashboard` (main view change)
- `SegmentCard` → `ComparisonCard` (restructured for new layout)
- `UnifiedSidebar` → `ProjectSidebar` + `CompanyProfileSidebar`
- `DocumentViewer` → Enhanced version with side-by-side support

### Progressive Migration Strategy
1. Keep existing whiteboard accessible via feature flag
2. Implement new dashboard alongside existing system
3. Migrate data gradually with validation
4. Switch default view to new dashboard
5. Remove old whiteboard after user acceptance

## Success Metrics
- Improved user workflow efficiency (dashboard → detail → report)
- Better evidence presentation with PDF highlighting
- Scalable analysis algorithm integration via JSONB
- Professional audit report generation capability