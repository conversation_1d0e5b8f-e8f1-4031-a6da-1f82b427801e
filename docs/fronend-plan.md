# TraceFast Frontend Implementation Plan

**Version:** 1.0  
**Date:** August 11, 2025

---

## Overview

This document outlines the implementation plan for the TraceFast frontend demo UI, focusing on the **互動式偵探線索板** (Interactive Detective Whiteboard) - the core feature for analyzing government tender bids to detect potential collusion and bid-rigging.

## Architecture

### Tech Stack
- **React 19** with TypeScript
- **TanStack Router** for client-side routing
- **TanStack Query** for state management and data fetching
- **Tailwind CSS 4** for styling
- **Lucide React** for icons
- **Vite** for development and build tooling

### Project Structure
```
apps/frontend/src/
├── components/
│   ├── ui/                    # Reusable UI components
│   ├── layout/                # Layout components
│   ├── whiteboard/            # Detective whiteboard components
│   ├── tender/                # Tender management components
│   └── analysis/              # Analysis result components
├── hooks/                     # Custom React hooks
├── lib/                       # Utilities and configurations
├── types/                     # TypeScript type definitions
├── data/                      # Mock data for demo
└── routes/                    # Route components
```

## Core Features Implementation

### 1. Main Layout System

#### Header Component
```typescript
// Header with branding and navigation
interface HeaderProps {
  currentTender?: Tender
  onTenderChange: (tender: Tender) => void
}
```

#### Tender-based Sidebar
```typescript
interface TenderSidebarProps {
  tenders: Tender[]
  activeTender: string | null
  onTenderSelect: (tenderId: string) => void
}

interface Tender {
  id: string
  title: string
  publishDate: string
  department: string
  bids: Bid[]
  analysisStatus: 'pending' | 'analyzing' | 'completed'
}
```

### 2. Interactive Detective Whiteboard

#### Canvas Component Architecture
```typescript
interface WhiteboardCanvas {
  // Main canvas for drag-and-drop interface
  suspiciousSegments: SuspiciousSegment[]
  categories: AnalysisCategory[]
  connections: Connection[]
  onSegmentMove: (segmentId: string, position: Position) => void
  onGroupCreate: (segments: SuspiciousSegment[]) => void
}

interface SuspiciousSegment {
  id: string
  content: string
  sourceBid: string
  category: AnalysisType
  suspicionLevel: 'low' | 'medium' | 'high'
  position: Position
  similarSegments: string[] // Related segment IDs
}

type AnalysisType = 'semantic' | 'stylometry' | 'structural' | 'numerical'
```

#### Analysis Categories Layout
- **語義相似度 (Semantic Similarity)**: Text content comparisons using embedding similarity
- **寫作風格 (Stylometry)**: Writing pattern analysis showing similar sentence structures, vocabulary usage
- **結構相似 (Structural)**: Document layout, section organization, template similarities
- **關鍵字數值 (Keyword/Numerical)**: Price points, technical specifications, suspicious numerical coincidences

### 3. Bid Management Interface

#### Batch Upload System
```typescript
interface BidUploadProps {
  tenderId: string
  onUploadComplete: (bids: Bid[]) => void
  acceptedFormats: string[] // PDF, DOCX, etc.
}

interface Bid {
  id: string
  companyName: string
  fileName: string
  uploadDate: string
  totalPrice: number
  analysisResults: AnalysisResult[]
}
```

#### Bid Comparison View
- Side-by-side text comparison
- Highlighted suspicious sections
- Similarity scores between bid pairs
- Cross-references to whiteboard segments

### 4. Analysis Results Display

#### Similarity Detection System
```typescript
interface AnalysisResult {
  bidPair: [string, string] // Bid IDs being compared
  overallSimilarity: number // 0-100%
  categoryScores: {
    semantic: number
    stylometry: number
    structural: number
    numerical: number
  }
  suspiciousSegments: SuspiciousSegment[]
  riskLevel: 'low' | 'medium' | 'high' | 'critical'
}
```

#### Visual Indicators
- Color-coded risk levels (green/yellow/orange/red)
- Progress bars for similarity scores
- Network diagrams showing bid relationships
- Heat maps for document section comparisons

### 5. AI Insights & Reporting

#### AI Explanation System
```typescript
interface AIInsight {
  groupId: string
  segments: SuspiciousSegment[]
  explanation: string
  evidenceStrength: number
  recommendedAction: string
}
```

#### Report Generation
- Executive summary of findings
- Evidence compilation with screenshots
- Risk assessment matrix
- Recommended investigation actions

## Mock Data Structure

### Demo Tender Setup
```typescript
const demoTender: Tender = {
  id: "tender-2025-001",
  title: "台北市政府辦公大樓清潔維護服務採購案",
  publishDate: "2025-01-15",
  department: "台北市政府總務處",
  budget: 12000000,
  bids: [
    {
      id: "bid-001",
      companyName: "清潔王股份有限公司",
      totalPrice: 11500000,
      // Suspicious: Very similar structure to bid-002
    },
    {
      id: "bid-002", 
      companyName: "環境服務企業有限公司",
      totalPrice: 11600000,
      // Suspicious: Similar price point and identical technical specs
    },
    {
      id: "bid-003",
      companyName: "專業清潔服務公司",
      totalPrice: 9800000,
      // Legitimate bid with different approach
    }
  ]
}
```

### Suspicious Patterns to Demonstrate
1. **Template Reuse**: Multiple bids using identical document structure
2. **Price Coordination**: Suspiciously similar pricing strategies
3. **Copy-Paste Evidence**: Identical technical specifications or methodology descriptions
4. **Writing Style**: Same author potentially writing multiple bids

## UI/UX Design Guidelines

### Color Scheme (Investigation Theme)
- **Primary**: Deep blue (#1e3a8a) - Professional, trustworthy
- **Accent**: Amber (#f59e0b) - Highlights, warnings
- **Success**: Emerald (#10b981) - Safe, verified content
- **Warning**: Orange (#ea580c) - Medium risk
- **Danger**: Red (#dc2626) - High risk, critical findings
- **Background**: Slate gray (#f8fafc) - Clean, focused workspace

### Typography
- **Headers**: Inter font, bold weights
- **Body**: Inter font, regular weights  
- **Code/Data**: JetBrains Mono for technical content

### Interaction Patterns
- **Drag & Drop**: Smooth animations, visual feedback
- **Hover States**: Subtle elevations, color transitions
- **Loading States**: Skeleton screens, progress indicators
- **Responsive**: Mobile-first approach with desktop optimizations

## Implementation Phases

### Phase 1: Core Layout (Week 1)
- Header and navigation
- Tender sidebar with mock data
- Basic routing setup

### Phase 2: Whiteboard Foundation (Week 1-2)
- Canvas component with drag-and-drop
- Segment cards with basic styling
- Category sections layout

### Phase 3: Analysis Features (Week 2)
- Mock analysis results integration
- Similarity scoring display
- AI insights panels

### Phase 4: Polish & Demo Prep (Week 3)
- Responsive design improvements
- Animation and micro-interactions
- Demo data refinement
- Performance optimizations

## Success Metrics

### Demo Effectiveness
- [ ] Clearly demonstrates bid-rigging detection capabilities
- [ ] Intuitive user experience for non-technical users
- [ ] Compelling visual presentation of analysis results
- [ ] Scalable architecture for future feature additions

### Technical Quality
- [ ] Type-safe implementation with TypeScript
- [ ] Responsive design across devices
- [ ] Smooth performance with large datasets
- [ ] Clean, maintainable code structure

---

This plan provides the foundation for building a compelling demo that showcases TraceFast's core value proposition in detecting procurement fraud through innovative AI-powered analysis and human-centered design.