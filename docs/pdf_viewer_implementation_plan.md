# PDF Viewer with OCR Chunk Visualization - Implementation Plan

## Overview
This document outlines the implementation plan for enhancing the TraceFast frontend with a comprehensive PDF viewer that can visualize OCR chunks as interactive bounding boxes overlaid on the rendered PDF.

## Current State Analysis

### Existing DocumentViewer Component
- **Location**: `apps/frontend/src/components/whiteboard/DocumentViewer.tsx`
- **Current Implementation**: Simple iframe for PDF display
- **Limitations**: 
  - No interactive overlays
  - No chunk visualization
  - Limited control over PDF rendering
  - No coordinate system access

### Technology Stack
- **React 19** with TypeScript
- **Vite** build system
- **TailwindCSS** for styling
- **Radix UI** components
- **@tanstack/react-query** for data fetching
- **@tanstack/react-router** for routing

## Library Selection: react-pdf

### Why react-pdf?
1. **React Integration**: Native React component with hooks
2. **PDF.js Wrapper**: Built on Mozilla's robust PDF.js library
3. **Customizable**: Allows custom overlays and interactions
4. **Page Access**: Provides access to page dimensions and rendering
5. **Performance**: Efficient rendering with lazy loading
6. **TypeScript Support**: Full TypeScript definitions

### Alternative Considerations
- **react-pdf-viewer**: Feature-rich but heavier (600kb+)
- **PDF.js Direct**: More control but complex integration
- **react-file-viewer**: Limited PDF-specific features

## Architecture Design

### Component Hierarchy
```
EnhancedDocumentViewer
├── PDFViewerCore (react-pdf wrapper)
├── ChunkOverlayLayer (bounding box overlays)
├── ChunkInfoTooltip (hover information)
├── DocumentControls (zoom, page navigation)
├── ChunkFilterPanel (filter by type/similarity)
└── SideBySideComparison (comparison mode)
```

### Data Flow
```
Backend OCR API → Document Chunks → Coordinate Mapping → Overlay Rendering → User Interaction
```

## Implementation Phases

### Phase 1: Setup and Basic PDF Rendering

#### 1.1 Install Dependencies
```bash
npm install react-pdf pdfjs-dist
npm install -D @types/react-pdf
```

#### 1.2 Create Core PDF Viewer Component
**File**: `components/pdf/PDFViewerCore.tsx`

**Features**:
- PDF loading and error handling
- Page navigation
- Zoom controls
- Responsive rendering

#### 1.3 Replace Existing DocumentViewer
**File**: `components/whiteboard/EnhancedDocumentViewer.tsx`

**Enhancements**:
- Backward compatibility with existing props
- Progressive enhancement approach
- Fallback to iframe for unsupported cases

### Phase 2: OCR Chunk Integration

#### 2.1 API Integration
**Service**: `services/documentService.ts`

**New Endpoints**:
```typescript
// Get document chunks with bounding boxes
GET /api/v1/documents/{id}/chunks

// Get chunk details
GET /api/v1/documents/{id}/chunks/{chunkId}

// Search similar chunks
POST /api/v1/documents/{id}/chunks/search
```

#### 2.2 Chunk Data Models
**File**: `types/document.ts`

```typescript
interface DocumentChunk {
  id: string;
  page_number: number;
  chunk_type: 'text' | 'image' | 'table';
  content: string;
  bounding_box: {
    x1: number;
    y1: number;
    x2: number;
    y2: number;
    width: number;
    height: number;
  };
}

interface ChunkOverlay {
  chunk: DocumentChunk;
  displayCoords: Rectangle;
  isHighlighted: boolean;
  similarity?: number;
}
```

### Phase 3: Coordinate System Mapping

#### 3.1 PDF Coordinate System
- **PDF Origin**: Bottom-left corner (0,0)
- **Y-axis**: Upward direction
- **Units**: Points (1/72 inch)

#### 3.2 Display Coordinate System
- **HTML Origin**: Top-left corner (0,0)
- **Y-axis**: Downward direction
- **Units**: Pixels

#### 3.3 Coordinate Conversion
**File**: `utils/coordinateMapper.ts`

```typescript
interface CoordinateMapper {
  // Convert PDF coordinates to display coordinates
  pdfToDisplay(
    pdfBox: BoundingBox,
    pageWidth: number,
    pageHeight: number,
    scale: number
  ): Rectangle;
  
  // Convert display coordinates back to PDF coordinates
  displayToPdf(
    displayRect: Rectangle,
    pageWidth: number,
    pageHeight: number,
    scale: number
  ): BoundingBox;
}
```

### Phase 4: Bounding Box Overlay System

#### 4.1 Overlay Component Architecture
**File**: `components/pdf/ChunkOverlayLayer.tsx`

**Features**:
- Absolute positioned overlays
- Scale-aware positioning
- Chunk type-based styling
- Interactive hover/click handlers

#### 4.2 Chunk Type Styling
```scss
.chunk-overlay {
  &.text {
    @apply border-blue-500 bg-blue-500/10 hover:bg-blue-500/20;
  }
  
  &.table {
    @apply border-green-500 bg-green-500/10 hover:bg-green-500/20;
  }
  
  &.image {
    @apply border-purple-500 bg-purple-500/10 hover:bg-purple-500/20;
  }
  
  &.title {
    @apply border-red-500 bg-red-500/10 hover:bg-red-500/20;
  }
  
  &.similar {
    @apply border-orange-500 bg-orange-500/20 animate-pulse;
  }
}
```

#### 4.3 Overlay Positioning Logic
```typescript
const calculateOverlayStyle = (
  chunk: DocumentChunk,
  pageRect: DOMRect,
  scale: number
): CSSProperties => {
  const displayCoords = coordinateMapper.pdfToDisplay(
    chunk.bounding_box,
    pageRect.width,
    pageRect.height,
    scale
  );
  
  return {
    position: 'absolute',
    left: displayCoords.x,
    top: displayCoords.y,
    width: displayCoords.width,
    height: displayCoords.height,
    pointerEvents: 'all',
    zIndex: 10
  };
};
```

### Phase 5: Interactive Features

#### 5.1 Chunk Information Tooltip
**File**: `components/pdf/ChunkInfoTooltip.tsx`

**Content**:
- Chunk type and confidence
- Text content preview
- Page number and coordinates
- Similarity score (if comparing)

#### 5.2 Chunk Selection and Highlighting
**Features**:
- Click to select chunks
- Multi-select with Ctrl/Cmd
- Highlight similar chunks
- Export selected chunks

#### 5.3 Search and Filter
**File**: `components/pdf/ChunkFilterPanel.tsx`

**Filters**:
- Chunk type (text, table, image, title)
- Similarity threshold
- Page range
- Text content search

### Phase 6: Side-by-Side Comparison

#### 6.1 Dual PDF Viewer
**File**: `components/comparison/SideBySideViewer.tsx`

**Features**:
- Synchronized scrolling
- Aligned page display
- Cross-document chunk comparison
- Similarity highlighting

#### 6.2 Comparison Visualization
```typescript
interface ComparisonChunk {
  sourceChunk: DocumentChunk;
  targetChunk: DocumentChunk;
  similarity: number;
  analysisType: 'semantic' | 'layout' | 'textual';
}
```

**Visual Indicators**:
- Connected lines between similar chunks
- Color-coded similarity levels
- Animation for highlighting matches

## Technical Implementation Details

### 6.1 Performance Optimization

#### Lazy Loading
```typescript
// Only load chunks for visible pages
const useVisibleChunks = (
  allChunks: DocumentChunk[],
  visiblePages: number[]
) => {
  return useMemo(() =>
    allChunks.filter(chunk => 
      visiblePages.includes(chunk.page_number)
    ), [allChunks, visiblePages]
  );
};
```

#### Virtual Scrolling
- Render only visible pages
- Unload off-screen page resources
- Optimize overlay calculations

#### Debounced Updates
```typescript
// Debounce coordinate calculations on zoom/scroll
const debouncedCalculateOverlays = useCallback(
  debounce(calculateOverlays, 50),
  [chunks, scale]
);
```

### 6.2 Error Handling

#### PDF Loading Errors
- Fallback to iframe
- User-friendly error messages
- Retry mechanisms

#### Chunk Loading Errors
- Graceful degradation
- Partial chunk display
- Background retry logic

### 6.3 Accessibility

#### Keyboard Navigation
- Tab through chunks
- Arrow key navigation
- Enter/Space for selection

#### Screen Reader Support
```typescript
<div
  role="region"
  aria-label={`${chunk.chunk_type} chunk on page ${chunk.page_number}`}
  aria-describedby={`chunk-${chunk.id}-tooltip`}
>
```

## API Enhancements

### New Backend Endpoints

#### Document Processing Status
```typescript
GET /api/v1/documents/{id}/processing-status
Response: {
  status: 'pending' | 'processing' | 'processed' | 'error',
  progress: number,
  total_chunks: number,
  processed_chunks: number
}
```

#### Bulk Chunk Operations
```typescript
POST /api/v1/documents/chunks/compare
Body: {
  chunk_ids: string[],
  comparison_type: 'semantic' | 'layout'
}
```

#### Document Metadata
```typescript
GET /api/v1/documents/{id}/metadata
Response: {
  page_count: number,
  file_size: number,
  ocr_confidence: number,
  processing_time: number,
  chunk_statistics: {
    text_chunks: number,
    table_chunks: number,
    image_chunks: number
  }
}
```

## User Experience Design

### 7.1 Loading States
- Skeleton loaders for PDF pages
- Progress indicators for chunk loading
- Smooth transitions between states

### 7.2 Interactive Feedback
```typescript
// Hover animations
.chunk-overlay {
  transition: all 0.2s ease;
  
  &:hover {
    transform: scale(1.02);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
  }
}
```

### 7.3 Mobile Responsiveness
- Touch-friendly overlays
- Responsive layout adjustments
- Gesture support for zoom/pan

## Testing Strategy

### 8.1 Unit Tests
```typescript
// Test coordinate conversion
describe('CoordinateMapper', () => {
  it('converts PDF coordinates to display coordinates', () => {
    const pdfBox = { x1: 100, y1: 500, x2: 200, y2: 550 };
    const result = mapper.pdfToDisplay(pdfBox, 800, 1000, 1.0);
    expect(result.x).toBe(100);
    expect(result.y).toBe(450); // Y-axis flipped
  });
});
```

### 8.2 Integration Tests
- PDF loading with real documents
- Chunk overlay positioning accuracy
- Side-by-side comparison functionality

### 8.3 Performance Tests
- Large document handling
- Memory usage optimization
- Rendering performance metrics

## Migration Strategy

### 9.1 Backward Compatibility
- Feature flag for new viewer
- Graceful fallback to existing implementation
- A/B testing support

### 9.2 Progressive Enhancement
1. **Phase 1**: Deploy new viewer alongside existing
2. **Phase 2**: Default to new viewer with fallback
3. **Phase 3**: Replace existing implementation

### 9.3 User Training
- Interactive tutorials
- Feature highlights
- Documentation updates

## Success Metrics

### 10.1 Performance Metrics
- PDF loading time < 2 seconds
- Chunk overlay rendering < 100ms
- Memory usage < 200MB for large documents

### 10.2 User Experience Metrics
- User engagement with chunk overlays
- Time spent in document comparison
- Feature adoption rates

### 10.3 Technical Metrics
- Error rates < 1%
- API response times < 500ms
- Cross-browser compatibility > 95%

## Timeline Estimation

- **Phase 1**: PDF Setup (3-4 days)
- **Phase 2**: OCR Integration (2-3 days)
- **Phase 3**: Coordinate Mapping (2 days)
- **Phase 4**: Overlay System (3-4 days)
- **Phase 5**: Interactive Features (2-3 days)
- **Phase 6**: Side-by-Side Comparison (3-4 days)

**Total Estimated Time**: 15-20 days

## Risk Assessment

### High Risk
- **Coordinate system accuracy**: Critical for overlay positioning
- **Performance with large documents**: Memory and rendering optimization
- **Cross-browser PDF compatibility**: Different PDF.js behavior

### Medium Risk
- **API integration complexity**: New endpoints and data structures
- **Mobile responsiveness**: Touch interactions and gestures

### Low Risk
- **UI/UX implementation**: Standard React component patterns
- **Testing and deployment**: Existing CI/CD pipeline

## Conclusion

This implementation will transform TraceFast's document viewing capabilities from basic iframe display to a sophisticated, interactive PDF viewer with intelligent OCR chunk visualization. The modular architecture ensures maintainability while the progressive enhancement approach minimizes deployment risks.

The new viewer will significantly enhance the user experience for procurement document analysis, enabling users to:
- Visually identify suspicious document sections
- Compare documents with precise chunk-level analysis
- Navigate efficiently through large documents
- Export and share analysis findings

This implementation positions TraceFast as a cutting-edge document analysis platform with industry-leading visualization capabilities.