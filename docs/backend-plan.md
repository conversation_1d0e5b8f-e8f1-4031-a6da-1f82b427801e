# TraceFast Backend Implementation Plan (MVP)

**Document Version:** 1.0  
**Last Updated:** 2025年8月12日  
**Project Phase:** MVP Development

---

## 1. Project Overview

TraceFast backend serves as the core processing engine for the public procurement analysis platform. It provides APIs for document upload, processing, and analysis result retrieval while maintaining simplicity suitable for an MVP competition demo.

### Key Design Principles
- **Privacy First**: All processing done locally with open-source models
- **Simplicity**: Synchronous processing, SQLite database, no complex infrastructure
- **Scalability**: Clean architecture that can be upgraded to production systems
- **Competition Ready**: Focus on core functionality over infrastructure complexity

---

## 2. Architecture Overview

### 2.1 High-Level Architecture
```
Frontend (React) 
    ↓ HTTP API
Backend (FastAPI)
    ↓ Local Storage
Files + SQLite Database
    ↓ Processing Pipeline
OCR + Analysis Engines
    ↓ Results
Detective Whiteboard Data
```

### 2.2 Technology Stack
- **Web Framework**: FastAPI (already in place)
- **Database**: SQLite with SQLAlchemy ORM
- **File Storage**: Local filesystem with organized structure
- **Processing**: Synchronous function calls
- **Models**: Pydantic for API, SQLAlchemy for database

---

## 3. API Design

### 3.1 Endpoint Structure
```
/api/v1/
├── tenders/                 # Tender management
│   ├── GET /               # List all tenders
│   ├── POST /              # Create new tender
│   ├── GET /{tender_id}    # Get tender details
│   └── DELETE /{tender_id} # Delete tender
├── bids/                   # Bid management
│   ├── POST /upload        # Upload bid documents
│   ├── GET /{bid_id}       # Get bid details
│   └── DELETE /{bid_id}    # Delete bid
├── analysis/               # Analysis operations
│   ├── POST /start/{tender_id}     # Start analysis for tender
│   ├── GET /status/{tender_id}     # Get analysis status
│   └── GET /results/{tender_id}    # Get analysis results
└── health/                 # System health (already exists)
```

### 3.2 Key API Flows

#### Document Upload Flow
1. `POST /api/v1/bids/upload` - Upload multiple PDF files
2. Server validates files and stores them locally
3. Creates bid records in database
4. Returns bid IDs and upload confirmation

#### Analysis Flow
1. `POST /api/v1/analysis/start/{tender_id}` - Trigger analysis
2. Server processes all bids synchronously:
   - OCR extraction (placeholder)
   - Multi-dimensional comparison
   - Generate suspicious segments
3. `GET /api/v1/analysis/status/{tender_id}` - Poll processing status
4. `GET /api/v1/analysis/results/{tender_id}` - Retrieve results

---

## 4. Data Models

### 4.1 Database Schema (SQLAlchemy Models)

```python
# Tender Model
class Tender:
    id: str (UUID)
    title: str
    publish_date: datetime
    department: str
    budget: float
    analysis_status: enum ('pending', 'analyzing', 'completed')
    created_at: datetime
    updated_at: datetime

# Bid Model  
class Bid:
    id: str (UUID)
    tender_id: str (FK)
    company_name: str
    file_name: str
    file_path: str
    total_price: float
    upload_date: datetime
    created_at: datetime

# Analysis Result Model
class AnalysisResult:
    id: str (UUID)
    tender_id: str (FK)
    bid_pair: str (JSON: [bid_id_1, bid_id_2])
    overall_similarity: float
    category_scores: str (JSON)
    risk_level: enum ('low', 'medium', 'high', 'critical')
    created_at: datetime

# Suspicious Segment Model
class SuspiciousSegment:
    id: str (UUID)
    analysis_result_id: str (FK)
    content: str
    source_bid_id: str (FK)
    category: enum ('semantic', 'stylometry', 'structural', 'numerical')
    suspicion_level: enum ('low', 'medium', 'high', 'critical')
    similarity_score: float
    position_x: float (for whiteboard)
    position_y: float (for whiteboard)
    created_at: datetime
```

### 4.2 Pydantic API Models

Match the TypeScript interfaces from frontend:
- `TenderCreate`, `TenderResponse`
- `BidUpload`, `BidResponse`
- `AnalysisRequest`, `AnalysisStatus`, `AnalysisResults`
- `SuspiciousSegmentResponse`

---

## 5. Document Processing Pipeline

### 5.1 File Storage Structure
```
storage/
├── tenders/
│   └── {tender_id}/
│       └── bids/
│           ├── {bid_id_1}.pdf
│           ├── {bid_id_2}.pdf
│           └── extracted/
│               ├── {bid_id_1}/
│               │   ├── text.txt
│               │   └── layout.json
│               └── {bid_id_2}/
│                   ├── text.txt
│                   └── layout.json
```

### 5.2 Processing Steps

1. **File Validation**: Check PDF format, size limits
2. **OCR Processing** (Placeholder):
   ```python
   def extract_document_content(pdf_path: str) -> dict:
       # Placeholder for open-source OCR model
       return {
           "text": "extracted text content",
           "layout": {"pages": [], "sections": []},
           "metadata": {"pages": 10, "language": "zh"}
       }
   ```

3. **Text Preprocessing**: Clean and chunk text for analysis
4. **Multi-dimensional Analysis**: Run all comparison algorithms
5. **Results Aggregation**: Combine results into suspicious segments

---

## 6. Analysis Engine Design

### 6.1 Comparison Algorithms

#### Semantic Similarity (Placeholder)
```python
def semantic_comparison(text1: str, text2: str) -> float:
    # Placeholder for embedding-based similarity
    # Will use open-source embedding model
    return 0.85  # Mock similarity score
```

#### Stylometry Analysis
```python
def stylometry_analysis(text1: str, text2: str) -> float:
    # Analyze writing style patterns:
    # - Sentence length distribution
    # - Punctuation usage
    # - Word frequency patterns
    # - Readability metrics
    return 0.75  # Mock style similarity
```

#### Structural Analysis (Layout OCR)
```python
def structural_comparison(layout1: dict, layout2: dict) -> float:
    # Compare document structure:
    # - Section organization
    # - Table layouts
    # - Header/footer patterns
    # - Page formatting
    return 0.90  # Mock structural similarity
```

#### Numerical Analysis
```python
def numerical_comparison(text1: str, text2: str) -> dict:
    # Extract and compare:
    # - Prices and financial figures
    # - Technical specifications
    # - Quantities and measurements
    # - Dates and deadlines
    return {"price_similarity": 0.95, "spec_similarity": 0.80}
```

### 6.2 Results Aggregation
```python
def generate_suspicious_segments(
    bid_pair: tuple, 
    comparison_results: dict
) -> list[SuspiciousSegment]:
    # Combine all analysis types
    # Identify suspicious text segments
    # Calculate overall risk levels
    # Generate whiteboard positions
    pass
```

---

## 7. Implementation Structure

### 7.1 Directory Organization
```
apps/backend/src/app/
├── models/
│   ├── __init__.py
│   ├── database.py      # SQLAlchemy models
│   └── schemas.py       # Pydantic models
├── services/
│   ├── __init__.py
│   ├── file_service.py  # File upload/storage
│   ├── ocr_service.py   # OCR processing (placeholder)
│   ├── analysis_service.py  # Analysis coordination
│   └── comparison/
│       ├── __init__.py
│       ├── semantic.py
│       ├── stylometry.py
│       ├── structural.py
│       └── numerical.py
├── api/v1/
│   ├── __init__.py
│   ├── tenders.py       # Tender endpoints
│   ├── bids.py          # Bid endpoints
│   └── analysis.py      # Analysis endpoints
├── core/
│   ├── __init__.py
│   ├── config.py        # Enhanced configuration
│   ├── database.py      # Database connection
│   └── security.py      # Authentication (future)
├── storage/
│   ├── __init__.py
│   └── manager.py       # File storage management
└── utils/
    ├── __init__.py
    ├── exceptions.py    # Custom exceptions
    └── validators.py    # Input validation
```

---

## 8. Dependencies & Configuration

### 8.1 Additional Dependencies
```toml
dependencies = [
    "fastapi[standard]>=0.116.0",
    "uvicorn[standard]>=0.30.0",
    "pydantic>=2.7.0",
    "pydantic-settings>=2.3.3",
    "python-dotenv>=1.0.1",
    "sqlalchemy>=2.0.0",
    "alembic>=1.13.0",     # Database migrations
    "python-multipart>=0.0.6",  # File uploads
    "aiofiles>=23.2.0",    # Async file operations
    "pypdf>=3.17.0",       # PDF processing
    "python-magic>=0.4.27", # File type detection
]
```

### 8.2 Configuration Enhancement
```python
class Settings(BaseSettings):
    # Existing settings...
    
    # Database
    database_url: str = "sqlite:///./tracefast.db"
    
    # File Storage
    storage_path: str = "./storage"
    max_file_size: int = 50 * 1024 * 1024  # 50MB
    allowed_extensions: list[str] = [".pdf"]
    
    # Analysis
    max_concurrent_analysis: int = 5
    analysis_timeout: int = 300  # 5 minutes
    
    # OCR & LLM (placeholders)
    ocr_model_path: str = ""
    embedding_model_path: str = ""
```

---

## 9. Development Phases

### Phase 1: Foundation (Current Sprint)
- [x] Project structure setup
- [ ] Database models and migrations
- [ ] Basic API endpoints
- [ ] File upload functionality
- [ ] Local storage management

### Phase 2: Core Processing
- [ ] OCR service placeholders
- [ ] Analysis engine framework
- [ ] Comparison algorithm stubs
- [ ] Results aggregation

### Phase 3: Integration & Testing
- [ ] Frontend-backend integration
- [ ] End-to-end testing
- [ ] Error handling refinement
- [ ] Performance optimization

### Phase 4: Competition Preparation
- [ ] Demo data preparation
- [ ] Documentation finalization
- [ ] Deployment scripts
- [ ] Presentation materials

---

## 10. Future Scalability Considerations

### Production Upgrades (Post-MVP)
- **Database**: Migrate from SQLite to PostgreSQL
- **Processing**: Add Redis + Celery for background tasks
- **WebSockets**: Real-time updates for analysis progress
- **Containerization**: Docker deployment
- **Authentication**: JWT-based user management
- **Monitoring**: Logging and metrics collection

### Open Source Model Integration
- **OCR**: Integration points ready for models like PaddleOCR
- **Embeddings**: Support for sentence-transformers or similar
- **LLM**: Placeholder for local language models

---

## 11. Security & Privacy

### Data Protection
- All documents stored locally
- No external API calls for sensitive operations
- File access controls and validation
- Input sanitization and validation

### Audit Trail
- Operation logging
- File access tracking
- Analysis request history
- Error monitoring

---

This plan provides a comprehensive roadmap for implementing the TraceFast backend while maintaining simplicity suitable for an MVP competition demo. The architecture is designed to be easily upgradeable for production use while focusing on core functionality and user experience.