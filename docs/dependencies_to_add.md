# Dependencies to Add for PDF Viewer Implementation

## Required Dependencies

Add these dependencies to `apps/frontend/package.json`:

```bash
# Core PDF viewing
npm install react-pdf pdfjs-dist

# Type definitions
npm install -D @types/react-pdf

# PDF worker (for Vite configuration)
npm install vite-plugin-static-copy
```

## Vite Configuration

Update `apps/frontend/vite.config.ts`:

```typescript
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import { viteStaticCopy } from 'vite-plugin-static-copy';

export default defineConfig({
  plugins: [
    react(),
    viteStaticCopy({
      targets: [
        {
          src: 'node_modules/pdfjs-dist/build/pdf.worker.js',
          dest: 'assets'
        }
      ]
    })
  ],
  // ... rest of config
});
```

## PDF.js Configuration

Create `apps/frontend/src/utils/pdfConfig.ts`:

```typescript
import { pdfjs } from 'react-pdf';

// Configure PDF.js worker
pdfjs.GlobalWorkerOptions.workerSrc = new URL(
  'pdfjs-dist/build/pdf.worker.min.js',
  import.meta.url,
).toString();
```

## CSS Imports

Add to `apps/frontend/src/main.tsx` or your main CSS file:

```typescript
import 'react-pdf/dist/esm/Page/AnnotationLayer.css';
import 'react-pdf/dist/esm/Page/TextLayer.css';
```

## Alternative: CDN Configuration

If you prefer CDN over bundling the worker:

```typescript
// In pdfConfig.ts
pdfjs.GlobalWorkerOptions.workerSrc = `//unpkg.com/pdfjs-dist@${pdfjs.version}/build/pdf.worker.min.js`;
```