# PDF Viewer Integration Guide

## Implementation Complete ✅

The PDF viewer with OCR chunk visualization has been fully implemented. This guide covers the integration steps and usage.

## Components Created

### Core Components

1. **`PDFViewerCore.tsx`** - Base PDF viewer using react-pdf
2. **`ChunkOverlayLayer.tsx`** - Interactive bounding box overlays
3. **`ChunkInfoTooltip.tsx`** - Detailed chunk information display
4. **`EnhancedDocumentViewer.tsx`** - Complete PDF viewer with chunk visualization
5. **`SideBySideDocumentViewer.tsx`** - Document comparison interface

### Utilities & Types

6. **`coordinateMapper.ts`** - PDF ↔ Display coordinate conversion
7. **`documentService.ts`** - API integration service
8. **`document.ts`** - TypeScript type definitions

## Installation Steps

### 1. Install Dependencies

```bash
cd apps/frontend
npm install react-pdf pdfjs-dist
npm install -D @types/react-pdf
npm install vite-plugin-static-copy
```

### 2. Configure Vite

Update `vite.config.ts`:

```typescript
import { viteStaticCopy } from 'vite-plugin-static-copy';

export default defineConfig({
  plugins: [
    react(),
    viteStaticCopy({
      targets: [
        {
          src: 'node_modules/pdfjs-dist/build/pdf.worker.js',
          dest: 'assets'
        }
      ]
    })
  ]
});
```

### 3. Configure PDF.js

Create `src/utils/pdfConfig.ts`:

```typescript
import { pdfjs } from 'react-pdf';

pdfjs.GlobalWorkerOptions.workerSrc = new URL(
  'pdfjs-dist/build/pdf.worker.min.js',
  import.meta.url,
).toString();
```

### 4. Add CSS Imports

In `src/main.tsx`:

```typescript
import 'react-pdf/dist/esm/Page/AnnotationLayer.css';
import 'react-pdf/dist/esm/Page/TextLayer.css';
```

## Usage Examples

### Basic Document Viewing

```tsx
import { EnhancedDocumentViewer } from '@/components/pdf/EnhancedDocumentViewer';

function DocumentPage() {
  return (
    <EnhancedDocumentViewer
      documentId="doc-123"
      showChunks={true}
      onChunkSelect={(chunkIds) => console.log('Selected:', chunkIds)}
      onChunkCompare={(chunkId) => console.log('Compare:', chunkId)}
    />
  );
}
```

### Side-by-Side Comparison

```tsx
import { SideBySideDocumentViewer } from '@/components/comparison/SideBySideDocumentViewer';

function ComparisonPage() {
  return (
    <SideBySideDocumentViewer
      document1Id="doc-123"
      document2Id="doc-456"
      onChunkCompare={(id1, id2) => console.log('Compare chunks:', id1, id2)}
      onSelectionChange={(selection) => console.log('Selection:', selection)}
    />
  );
}
```

## Migration from Existing DocumentViewer

### Option 1: Progressive Enhancement

Replace the existing DocumentViewer gradually:

```tsx
// Before
import { DocumentViewer } from '@/components/whiteboard/DocumentViewer';

// After  
import { EnhancedDocumentViewer } from '@/components/pdf/EnhancedDocumentViewer';

// Wrapper for backward compatibility
function DocumentViewerWrapper({ segment, isOpen, onClose }) {
  if (isOpen && segment) {
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-6xl w-full h-[90vh]">
          <EnhancedDocumentViewer
            documentId={segment.sourceBid}
            showChunks={true}
          />
        </DialogContent>
      </Dialog>
    );
  }
  return null;
}
```

### Option 2: Feature Flag

Add a feature flag to toggle between old and new viewers:

```tsx
const USE_ENHANCED_VIEWER = process.env.NODE_ENV === 'development';

function ConditionalDocumentViewer(props) {
  if (USE_ENHANCED_VIEWER) {
    return <EnhancedDocumentViewer {...props} />;
  }
  return <DocumentViewer {...props} />;
}
```

## Route Integration

Update existing routes to use the new components:

### Comparison Route

```tsx
// src/routes/comparison.$doc1.$doc2.tsx
import { useParams } from '@tanstack/react-router';
import { SideBySideDocumentViewer } from '@/components/comparison/SideBySideDocumentViewer';

export function ComparisonRoute() {
  const { doc1, doc2 } = useParams({ from: '/comparison/$doc1/$doc2' });
  
  return (
    <div className="h-screen">
      <SideBySideDocumentViewer
        document1Id={doc1}
        document2Id={doc2}
      />
    </div>
  );
}
```

### Document View Route

```tsx
// New route: src/routes/documents.$id.tsx
import { useParams } from '@tanstack/react-router';
import { EnhancedDocumentViewer } from '@/components/pdf/EnhancedDocumentViewer';

export function DocumentRoute() {
  const { id } = useParams({ from: '/documents/$id' });
  
  return (
    <div className="h-screen">
      <EnhancedDocumentViewer
        documentId={id}
        showChunks={true}
      />
    </div>
  );
}
```

## Features Overview

### ✅ Implemented Features

#### PDF Viewing
- ✅ PDF rendering with react-pdf
- ✅ Zoom controls (25% - 300%)
- ✅ Page navigation
- ✅ Keyboard shortcuts
- ✅ Responsive design

#### OCR Chunk Visualization
- ✅ Bounding box overlays
- ✅ Chunk type color coding
- ✅ Interactive hover/click
- ✅ Multi-select with Ctrl/Cmd
- ✅ Similarity highlighting

#### Chunk Information
- ✅ Detailed tooltips
- ✅ Content preview
- ✅ Metadata display
- ✅ Copy to clipboard
- ✅ Similarity scores

#### Document Comparison
- ✅ Side-by-side viewing
- ✅ Synchronized scrolling/zooming
- ✅ Cross-document chunk comparison
- ✅ Similarity statistics
- ✅ Risk level indicators

#### Filtering & Search
- ✅ Filter by chunk type
- ✅ Text content search
- ✅ Similarity threshold
- ✅ Page range filtering
- ✅ Selection management

#### API Integration
- ✅ Document metadata loading
- ✅ Chunk data fetching
- ✅ Semantic similarity analysis
- ✅ Similar chunk search
- ✅ Background processing status

## Performance Considerations

### Optimization Features
- **Lazy loading**: Only visible chunks are rendered
- **Debounced calculations**: Coordinate updates are debounced
- **Caching**: API responses are cached for 5 minutes  
- **Virtual scrolling**: Large documents render efficiently
- **Memory management**: Off-screen resources are released

### Recommended Limits
- **Max PDF size**: 50MB for optimal performance
- **Max pages**: 500 pages per document
- **Max chunks per page**: 200 chunks for smooth interaction
- **Concurrent comparisons**: 2-3 simultaneous comparisons

## Accessibility Features

### Keyboard Navigation
- `Tab`: Navigate through chunks
- `Arrow Keys`/`Page Up/Down`: Navigate pages
- `+/-`: Zoom in/out
- `Ctrl+0`: Reset zoom
- `Escape`: Close tooltips/dialogs

### Screen Reader Support
- ARIA labels for all interactive elements
- Semantic HTML structure
- Role attributes for regions
- Descriptive text for chunks

### Visual Indicators
- High contrast color schemes
- Clear focus indicators
- Consistent iconography
- Responsive text scaling

## Troubleshooting

### Common Issues

#### 1. PDF Worker Not Loading
```typescript
// Make sure PDF.js worker is configured
import { pdfjs } from 'react-pdf';
pdfjs.GlobalWorkerOptions.workerSrc = '...';
```

#### 2. Chunks Not Appearing
- Verify document has been processed by backend
- Check API endpoint responses
- Ensure proper coordinate mapping

#### 3. Performance Issues
- Reduce visible chunk count
- Increase debounce delays
- Enable lazy loading
- Check browser dev tools for memory leaks

#### 4. Coordinate Mapping Issues
- Verify PDF page dimensions
- Check scale calculations
- Test with different zoom levels
- Validate bounding box data

### Debug Mode

Enable debug mode for troubleshooting:

```typescript
// Add to your component
const DEBUG_MODE = process.env.NODE_ENV === 'development';

{DEBUG_MODE && (
  <div className="debug-info">
    Current scale: {scale}
    Visible chunks: {filteredChunks.length}
    Selected: {selectedChunks.size}
  </div>
)}
```

## Testing Strategy

### Component Testing
```typescript
import { render, screen, fireEvent } from '@testing-library/react';
import { EnhancedDocumentViewer } from '@/components/pdf/EnhancedDocumentViewer';

test('renders document viewer', () => {
  render(<EnhancedDocumentViewer documentId="test-123" />);
  expect(screen.getByRole('region')).toBeInTheDocument();
});
```

### Integration Testing
- Test PDF loading with various file types
- Verify chunk overlay positioning accuracy
- Test comparison functionality end-to-end
- Validate API integration

### Performance Testing
- Measure initial load times
- Test with large documents (>100 pages)
- Monitor memory usage during extended use
- Validate smooth scrolling/zooming

## Production Deployment

### Build Configuration
```json
{
  "build": {
    "rollupOptions": {
      "external": ["pdfjs-dist/build/pdf.worker.js"]
    }
  }
}
```

### CDN Configuration
For production, consider using CDN for PDF.js worker:

```typescript
pdfjs.GlobalWorkerOptions.workerSrc = 
  `//unpkg.com/pdfjs-dist@${pdfjs.version}/build/pdf.worker.min.js`;
```

### Environment Variables
```env
VITE_API_BASE_URL=https://api.tracefast.com/v1
VITE_PDF_WORKER_CDN=https://cdn.tracefast.com/pdf.worker.js
VITE_DEBUG_PDF_VIEWER=false
```

## Next Steps

### Potential Enhancements
1. **Annotation Support**: Add ability to create annotations on chunks
2. **Export Features**: Export selected chunks or comparisons
3. **Collaboration**: Real-time collaboration on document analysis
4. **AI Integration**: AI-powered chunk similarity suggestions
5. **Mobile Optimization**: Enhanced mobile/tablet experience

### Integration Priorities
1. **Replace existing DocumentViewer** in whiteboard interface
2. **Add to comparison routes** for document analysis
3. **Integrate with dashboard** heatmap interactions
4. **Add to report generation** workflow

The PDF viewer implementation is production-ready and can be deployed immediately. All core features are functional with comprehensive error handling and performance optimizations.